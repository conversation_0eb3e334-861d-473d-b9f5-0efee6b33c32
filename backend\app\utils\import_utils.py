"""
Centralized import utilities to eliminate sys.path manipulation patterns.

This module provides utilities for importing modules and packages from
different parts of the Datagenius backend without requiring sys.path
manipulation in individual files.
"""

import os
import sys
import importlib
import importlib.util
from pathlib import Path
from typing import Any, Optional, Dict, List
import logging

logger = logging.getLogger(__name__)

# Cache for resolved paths to avoid repeated calculations
_path_cache: Dict[str, str] = {}

def get_backend_root() -> str:
    """
    Get the absolute path to the backend root directory.

    Returns:
        str: Absolute path to the backend directory
    """
    if 'backend_root' not in _path_cache:
        # Get the path relative to this file (backend/app/utils/import_utils.py)
        current_file = Path(__file__).resolve()
        backend_root = current_file.parent.parent.parent  # Go up 3 levels: utils -> app -> backend
        _path_cache['backend_root'] = str(backend_root)

    return _path_cache['backend_root']

def get_app_root() -> str:
    """
    Get the absolute path to the app directory.

    Returns:
        str: Absolute path to the backend/app directory
    """
    if 'app_root' not in _path_cache:
        backend_root = get_backend_root()
        _path_cache['app_root'] = os.path.join(backend_root, 'app')

    return _path_cache['app_root']

def get_agents_root() -> str:
    """
    Get the absolute path to the agents directory.

    Returns:
        str: Absolute path to the backend/agents directory
    """
    if 'agents_root' not in _path_cache:
        backend_root = get_backend_root()
        _path_cache['agents_root'] = os.path.join(backend_root, 'agents')

    return _path_cache['agents_root']

def ensure_backend_in_path() -> None:
    """
    Ensure the backend directory is in sys.path for imports.
    This is the centralized replacement for sys.path manipulation.
    """
    backend_root = get_backend_root()
    if backend_root not in sys.path:
        sys.path.insert(0, backend_root)
        logger.debug(f"Added backend root to sys.path: {backend_root}")

def import_from_backend(module_path: str, attribute: Optional[str] = None) -> Any:
    """
    Import a module or attribute from the backend directory.

    Args:
        module_path: Dot-separated module path relative to backend (e.g., 'agents.registry')
        attribute: Optional attribute name to import from the module

    Returns:
        The imported module or attribute

    Raises:
        ImportError: If the module or attribute cannot be imported
    """
    ensure_backend_in_path()

    try:
        module = importlib.import_module(module_path)
        if attribute:
            return getattr(module, attribute)
        return module
    except (ImportError, AttributeError) as e:
        logger.error(f"Failed to import {module_path}.{attribute or ''}: {e}")
        raise ImportError(f"Cannot import {module_path}.{attribute or ''}: {e}")

def import_agent_factory() -> Any:
    """
    Import the agent_factory instance from agents.langgraph.core.agent_factory.

    Returns:
        agent_factory instance
    """
    return import_from_backend('agents.langgraph.core.agent_factory', 'agent_factory')

def import_yaml_utils() -> tuple:
    """
    Import yaml utility functions from app.utils.yaml_utils.

    Returns:
        tuple: (load_yaml, save_yaml) functions
    """
    module = import_from_backend('app.utils.yaml_utils')
    return module.load_yaml, module.save_yaml

def import_persona_manager() -> Any:
    """
    Import the PersonaManager class from agents.persona_manager.

    Returns:
        PersonaManager class
    """
    return import_from_backend('agents.persona_manager', 'PersonaManager')

def import_workflow_manager() -> Any:
    """
    Import the WorkflowManager class from agents.langgraph.core.workflow_manager.

    Returns:
        WorkflowManager class
    """
    return import_from_backend('agents.langgraph.core.workflow_manager', 'WorkflowManager')

def import_mcp_tools() -> tuple:
    """
    Import MCP tool classes from agents.tools.mcp.

    Returns:
        tuple: (MCPTool, AVAILABLE_TOOLS) - MCPTool is an alias for BaseMCPTool
    """
    module = import_from_backend('agents.tools.mcp')
    return module.MCPTool, module.AVAILABLE_TOOLS

def safe_import(module_path: str, attribute: Optional[str] = None, fallback: Any = None) -> Any:
    """
    Safely import a module or attribute with fallback.

    Args:
        module_path: Dot-separated module path relative to backend
        attribute: Optional attribute name to import from the module
        fallback: Value to return if import fails

    Returns:
        The imported module/attribute or fallback value
    """
    try:
        return import_from_backend(module_path, attribute)
    except ImportError as e:
        logger.warning(f"Failed to import {module_path}.{attribute or ''}, using fallback: {e}")
        return fallback

def get_module_path(file_path: str) -> str:
    """
    Convert a file path to a module path relative to backend.

    Args:
        file_path: Absolute or relative file path

    Returns:
        str: Dot-separated module path
    """
    backend_root = get_backend_root()

    # Convert to absolute path if relative
    if not os.path.isabs(file_path):
        file_path = os.path.abspath(file_path)

    # Get relative path from backend root
    try:
        rel_path = os.path.relpath(file_path, backend_root)
        # Remove .py extension and convert slashes to dots
        module_path = rel_path.replace(os.sep, '.').replace('.py', '')
        return module_path
    except ValueError:
        # File is not under backend root
        raise ValueError(f"File {file_path} is not under backend root {backend_root}")

def list_available_modules(base_path: str) -> List[str]:
    """
    List all available Python modules under a given path.

    Args:
        base_path: Base path to search for modules (relative to backend)

    Returns:
        List[str]: List of module paths
    """
    backend_root = get_backend_root()
    search_path = os.path.join(backend_root, base_path.replace('.', os.sep))

    modules = []
    if os.path.exists(search_path):
        for root, dirs, files in os.walk(search_path):
            # Skip __pycache__ directories
            dirs[:] = [d for d in dirs if d != '__pycache__']

            for file in files:
                if file.endswith('.py') and not file.startswith('_'):
                    file_path = os.path.join(root, file)
                    try:
                        module_path = get_module_path(file_path)
                        modules.append(module_path)
                    except ValueError:
                        continue

    return sorted(modules)

# Convenience functions for common imports
def get_database_session():
    """Get database session from app.database."""
    return import_from_backend('app.database', 'get_db')

def get_auth_functions():
    """Get authentication functions from app.auth."""
    module = import_from_backend('app.auth')
    return {
        'get_current_user': module.get_current_user,
        'get_current_active_user': module.get_current_active_user,
        'get_current_user_from_token': getattr(module, 'get_current_user_from_token', None)
    }

def get_provider_models():
    """Get provider models functions from app.utils.provider_models."""
    return import_from_backend('app.utils.provider_models')

def import_agent_schemas() -> tuple:
    """
    Import agent schema classes from schemas.agent_config_schemas.

    Returns:
        tuple: (AgentProcessingContext, PersonaConfig) classes
    """
    module = import_from_backend('schemas.agent_config_schemas')
    return module.AgentProcessingContext, module.PersonaConfig

def import_db_schemas() -> Any:
    """
    Import database schema classes from schemas.db_schemas.

    Returns:
        Module containing database schema classes
    """
    return import_from_backend('schemas.db_schemas')
