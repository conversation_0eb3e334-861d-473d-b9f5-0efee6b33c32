"""
Analysis request parser component for the Datagenius agent system.

This module provides a component for parsing data analysis-related requests,
extracting key information, and determining the appropriate analysis task.
"""

import logging
import re
import sys
from typing import Dict, Any, List, Optional
from pathlib import Path

# Add backend root to path for import_utils
backend_root = Path(__file__).parent.parent.parent
if str(backend_root) not in sys.path:
    sys.path.insert(0, str(backend_root))

# Import AgentProcessingContext using centralized import
from app.utils.import_utils import import_agent_schemas
AgentProcessingContext, _ = import_agent_schemas()

from .base import AgentComponent

logger = logging.getLogger(__name__)


class AnalysisRequestParserComponent(AgentComponent):
    """Component for parsing data analysis-related requests."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the analysis request parser component.

        Args:
            config: Configuration dictionary for the component
        """
        # Define analysis types (kept for backward compatibility but not used for keyword matching)
        self.analysis_types = config.get("analysis_types", [
            "descriptive_analysis",
            "correlation_analysis",
            "trend_analysis",
            "segmentation_analysis",
            "predictive_analysis"
        ])

        # Define information extraction patterns
        self.extraction_patterns = config.get("extraction_patterns", {
            "target_variable": [
                r"target\s+variable(?:\s+is|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"predict(?:\s+the)?\s+(.*?)(?=\n\n|\n[A-Z]|$)",
                r"forecast(?:\s+the)?\s+(.*?)(?=\n\n|\n[A-Z]|$)"
            ],
            "time_period": [
                r"time\s+period(?:\s+is|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"date\s+range(?:\s+is|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"from\s+(.*?)\s+to\s+(.*?)(?=\n\n|\n[A-Z]|$)"
            ],
            "specific_columns": [
                r"columns?(?:\s+of\s+interest|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"variables?(?:\s+of\s+interest|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"focus\s+on\s+(?:columns|variables)(?:\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)"
            ],
            "specific_question": [
                r"question(?:\s+is|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"analyze(?:\s+the)?\s+(.*?)(?=\n\n|\n[A-Z]|$)",
                r"find(?:\s+out)?\s+(.*?)(?=\n\n|\n[A-Z]|$)"
            ]
        })

    async def process(self, context: "AgentProcessingContext") -> "AgentProcessingContext":
        """
        Process a request using this component.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object.
        """
        # Check if this component should be skipped
        if context.metadata.get("skip_analysis_parser", False):
            logger.debug(f"Skipping analysis request parser component: {self.name}")
            return context

        # Get message from context
        message = context.message or ""
        if not message:
            logger.warning("No message found in context")
            # Optionally set a response if this component is expected to always have a message
            # context.response = "No message provided for analysis parsing."
            return context

        try:
            # Extract information from the message using regex patterns
            extracted_info = self._extract_information(message)
            logger.info(f"Extracted information: {', '.join(extracted_info.keys())}")

            # Update context metadata with extracted information
            context.metadata.update(extracted_info) # Merge extracted info into metadata

            # Set a generic analysis type since we're not using keyword-based detection
            context.metadata["analysis_type"] = "general_analysis"
            context.metadata["prompt_template_name"] = "general_analysis"

            # Ensure extracted_info keys are also explicitly in metadata if needed by other logic
            context.metadata["extracted_info_keys"] = list(extracted_info.keys())

            return context

        except Exception as e:
            logger.error(f"Error parsing analysis request: {str(e)}", exc_info=True)
            context.add_error(self.name, f"analysis_parse_error: {str(e)}")
            # Optionally set a user-facing error response
            # context.response = "Sorry, I encountered an issue parsing your analysis request."
            return context



    def _extract_information(self, message: str) -> Dict[str, str]:
        """
        Extract analysis-related information from the message.

        Args:
            message: User message

        Returns:
            Dictionary of extracted information
        """
        extracted_info = {}

        # Apply extraction patterns
        for info_type, patterns in self.extraction_patterns.items():
            for pattern in patterns:
                matches = re.search(pattern, message, re.IGNORECASE | re.DOTALL)
                if matches:
                    extracted_text = matches.group(1).strip()
                    if extracted_text:
                        extracted_info[info_type] = extracted_text
                        break

        return extracted_info
