"""
Cart Repository Implementation.

Provides specialized repository operations for Cart entities,
replacing the cart-related CRUD functions from database.py.
"""

import uuid
import logging
from typing import List, Optional, Dict, Any
from decimal import Decimal
from sqlalchemy.orm import Session

from .base_repository import BaseRepository, RepositoryError
from ..models.database_models import Cart
from ..models.schemas import CartCreate, CartUpdate, CartResponse
from ..errors.correlation_context import get_correlation_logger

logger = get_correlation_logger(__name__)


class CartRepository(BaseRepository[Cart]):
    """Repository for Cart entity operations."""
    
    def __init__(self, session: Session):
        super().__init__(session, Cart)
        self.logger = logger
    
    def get_user_cart(self, user_id: int) -> Optional[Cart]:
        """
        Get the active cart for a user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            User's active cart or None if not found
        """
        try:
            return self.session.query(Cart).filter(
                Cart.user_id == user_id,
                Cart.status == 'active'
            ).first()
            
        except Exception as e:
            self.logger.error(f"Failed to get user cart: {e}")
            raise RepositoryError(
                f"Failed to get user cart: {str(e)}",
                entity_type="Cart",
                operation="get_user_cart"
            )
    
    def create_cart(self, user_id: int) -> Cart:
        """
        Create a new cart for a user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Created cart instance
            
        Raises:
            RepositoryError: If cart creation fails
        """
        try:
            cart_data = {
                'id': str(uuid.uuid4()),
                'user_id': user_id,
                'items': [],
                'total_amount': Decimal('0.00'),
                'currency': 'USD',
                'status': 'active'
            }
            
            cart = Cart(**cart_data)
            
            self.session.add(cart)
            self.session.commit()
            self.session.refresh(cart)
            
            self.logger.info(f"Created cart {cart.id} for user {user_id}")
            return cart
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to create cart: {e}")
            raise RepositoryError(
                f"Failed to create cart: {str(e)}",
                entity_type="Cart",
                operation="create"
            )
    
    def get_or_create_user_cart(self, user_id: int) -> Cart:
        """
        Get the user's active cart or create one if it doesn't exist.
        
        Args:
            user_id: ID of the user
            
        Returns:
            User's active cart
        """
        try:
            cart = self.get_user_cart(user_id)
            if not cart:
                cart = self.create_cart(user_id)
            return cart
            
        except Exception as e:
            self.logger.error(f"Failed to get or create user cart: {e}")
            raise RepositoryError(
                f"Failed to get or create user cart: {str(e)}",
                entity_type="Cart",
                operation="get_or_create_user_cart"
            )
    
    def add_item_to_cart(
        self,
        user_id: int,
        persona_id: str,
        price: Decimal,
        quantity: int = 1
    ) -> Cart:
        """
        Add an item to the user's cart.
        
        Args:
            user_id: ID of the user
            persona_id: ID of the persona to add
            price: Price of the persona
            quantity: Quantity to add (default: 1)
            
        Returns:
            Updated cart
        """
        try:
            cart = self.get_or_create_user_cart(user_id)
            
            # Check if item already exists in cart
            items = cart.items or []
            item_found = False
            
            for item in items:
                if item.get('persona_id') == persona_id:
                    item['quantity'] = item.get('quantity', 1) + quantity
                    item_found = True
                    break
            
            # Add new item if not found
            if not item_found:
                new_item = {
                    'persona_id': persona_id,
                    'price': float(price),
                    'quantity': quantity
                }
                items.append(new_item)
            
            cart.items = items
            cart.total_amount = self._calculate_cart_total(items)
            
            self.session.commit()
            self.session.refresh(cart)
            
            self.logger.info(f"Added item {persona_id} to cart {cart.id}")
            return cart
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to add item to cart: {e}")
            raise RepositoryError(
                f"Failed to add item to cart: {str(e)}",
                entity_type="Cart",
                operation="add_item_to_cart"
            )
    
    def remove_item_from_cart(
        self,
        user_id: int,
        persona_id: str
    ) -> Optional[Cart]:
        """
        Remove an item from the user's cart.
        
        Args:
            user_id: ID of the user
            persona_id: ID of the persona to remove
            
        Returns:
            Updated cart or None if cart not found
        """
        try:
            cart = self.get_user_cart(user_id)
            if not cart:
                self.logger.warning(f"Cart not found for user {user_id}")
                return None
            
            # Remove item from cart
            items = cart.items or []
            items = [item for item in items if item.get('persona_id') != persona_id]
            
            cart.items = items
            cart.total_amount = self._calculate_cart_total(items)
            
            self.session.commit()
            self.session.refresh(cart)
            
            self.logger.info(f"Removed item {persona_id} from cart {cart.id}")
            return cart
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to remove item from cart: {e}")
            raise RepositoryError(
                f"Failed to remove item from cart: {str(e)}",
                entity_type="Cart",
                operation="remove_item_from_cart"
            )
    
    def update_item_quantity(
        self,
        user_id: int,
        persona_id: str,
        quantity: int
    ) -> Optional[Cart]:
        """
        Update the quantity of an item in the cart.
        
        Args:
            user_id: ID of the user
            persona_id: ID of the persona
            quantity: New quantity (0 to remove item)
            
        Returns:
            Updated cart or None if cart not found
        """
        try:
            cart = self.get_user_cart(user_id)
            if not cart:
                self.logger.warning(f"Cart not found for user {user_id}")
                return None
            
            items = cart.items or []
            
            if quantity <= 0:
                # Remove item if quantity is 0 or negative
                items = [item for item in items if item.get('persona_id') != persona_id]
            else:
                # Update quantity
                item_found = False
                for item in items:
                    if item.get('persona_id') == persona_id:
                        item['quantity'] = quantity
                        item_found = True
                        break
                
                if not item_found:
                    self.logger.warning(f"Item {persona_id} not found in cart {cart.id}")
                    return cart
            
            cart.items = items
            cart.total_amount = self._calculate_cart_total(items)
            
            self.session.commit()
            self.session.refresh(cart)
            
            self.logger.info(f"Updated quantity for item {persona_id} in cart {cart.id}")
            return cart
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to update item quantity: {e}")
            raise RepositoryError(
                f"Failed to update item quantity: {str(e)}",
                entity_type="Cart",
                operation="update_item_quantity"
            )
    
    def clear_cart(self, user_id: int) -> Optional[Cart]:
        """
        Clear all items from the user's cart.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Cleared cart or None if cart not found
        """
        try:
            cart = self.get_user_cart(user_id)
            if not cart:
                self.logger.warning(f"Cart not found for user {user_id}")
                return None
            
            cart.items = []
            cart.total_amount = Decimal('0.00')
            
            self.session.commit()
            self.session.refresh(cart)
            
            self.logger.info(f"Cleared cart {cart.id}")
            return cart
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to clear cart: {e}")
            raise RepositoryError(
                f"Failed to clear cart: {str(e)}",
                entity_type="Cart",
                operation="clear_cart"
            )
    
    def checkout_cart(self, user_id: int) -> Optional[Cart]:
        """
        Mark the cart as checked out.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Checked out cart or None if cart not found
        """
        try:
            cart = self.get_user_cart(user_id)
            if not cart:
                self.logger.warning(f"Cart not found for user {user_id}")
                return None
            
            cart.status = 'checked_out'
            
            self.session.commit()
            self.session.refresh(cart)
            
            self.logger.info(f"Checked out cart {cart.id}")
            return cart
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to checkout cart: {e}")
            raise RepositoryError(
                f"Failed to checkout cart: {str(e)}",
                entity_type="Cart",
                operation="checkout_cart"
            )
    
    def get_cart_item_count(self, user_id: int) -> int:
        """
        Get the total number of items in the user's cart.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Total number of items in the cart
        """
        try:
            cart = self.get_user_cart(user_id)
            if not cart or not cart.items:
                return 0
            
            return sum(item.get('quantity', 1) for item in cart.items)
            
        except Exception as e:
            self.logger.error(f"Failed to get cart item count: {e}")
            raise RepositoryError(
                f"Failed to get cart item count: {str(e)}",
                entity_type="Cart",
                operation="get_cart_item_count"
            )
    
    def _calculate_cart_total(self, items: List[Dict[str, Any]]) -> Decimal:
        """
        Calculate the total amount for cart items.
        
        Args:
            items: List of cart items
            
        Returns:
            Total amount
        """
        total = Decimal('0.00')
        for item in items:
            price = Decimal(str(item.get('price', 0)))
            quantity = item.get('quantity', 1)
            total += price * quantity
        return total
