"""
Base template class for LangGraph workflow templates.

This module provides the foundation for creating reusable workflow patterns
with parameter validation, versioning, and metadata management.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Type, Union
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

from langgraph.graph import StateGraph, END
from ..states.agent_state import DatageniusAgentState
from ..events.event_bus import event_bus
from ..events.types import WorkflowStartedEvent

# CompiledGraph is not available in current LangGraph version
# Using Any type for compiled workflows
from typing import Any as CompiledGraph

logger = logging.getLogger(__name__)


class ParameterType(Enum):
    """Parameter types for template validation."""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    LIST = "list"
    DICT = "dict"
    AGENT_ID = "agent_id"
    BUSINESS_PROFILE_ID = "business_profile_id"


@dataclass
class TemplateParameter:
    """Template parameter definition with validation rules."""
    name: str
    parameter_type: ParameterType
    required: bool = True
    default_value: Any = None
    description: str = ""
    validation_rules: Dict[str, Any] = field(default_factory=dict)
    
    def validate(self, value: Any) -> bool:
        """Validate parameter value against type and rules."""
        try:
            # Check required parameters
            if self.required and value is None:
                return False
            
            # Use default if value is None and default exists
            if value is None and self.default_value is not None:
                value = self.default_value
            
            # Type validation
            if not self._validate_type(value):
                return False
            
            # Custom validation rules
            if not self._validate_rules(value):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating parameter {self.name}: {e}")
            return False
    
    def _validate_type(self, value: Any) -> bool:
        """Validate parameter type."""
        if value is None:
            return not self.required
        
        type_validators = {
            ParameterType.STRING: lambda v: isinstance(v, str),
            ParameterType.INTEGER: lambda v: isinstance(v, int),
            ParameterType.FLOAT: lambda v: isinstance(v, (int, float)),
            ParameterType.BOOLEAN: lambda v: isinstance(v, bool),
            ParameterType.LIST: lambda v: isinstance(v, list),
            ParameterType.DICT: lambda v: isinstance(v, dict),
            ParameterType.AGENT_ID: lambda v: isinstance(v, str) and len(v) > 0,
            ParameterType.BUSINESS_PROFILE_ID: lambda v: isinstance(v, str) and len(v) > 0
        }
        
        validator = type_validators.get(self.parameter_type)
        return validator(value) if validator else True
    
    def _validate_rules(self, value: Any) -> bool:
        """Validate against custom rules."""
        for rule_name, rule_value in self.validation_rules.items():
            if rule_name == "min_length" and isinstance(value, str):
                if len(value) < rule_value:
                    return False
            elif rule_name == "max_length" and isinstance(value, str):
                if len(value) > rule_value:
                    return False
            elif rule_name == "min_value" and isinstance(value, (int, float)):
                if value < rule_value:
                    return False
            elif rule_name == "max_value" and isinstance(value, (int, float)):
                if value > rule_value:
                    return False
            elif rule_name == "allowed_values":
                if value not in rule_value:
                    return False
        
        return True


@dataclass
class TemplateMetadata:
    """Template metadata for versioning and documentation."""
    name: str
    version: str
    description: str
    author: str = "Datagenius"
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    tags: List[str] = field(default_factory=list)
    category: str = "general"
    complexity: str = "medium"  # simple, medium, complex
    estimated_duration: float = 30.0  # seconds
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metadata to dictionary."""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "author": self.author,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "tags": self.tags,
            "category": self.category,
            "complexity": self.complexity,
            "estimated_duration": self.estimated_duration
        }


class WorkflowTemplate(ABC):
    """
    Base class for LangGraph workflow templates.
    
    This abstract base class provides the foundation for creating reusable
    workflow patterns with parameter validation, versioning, and event integration.
    """
    
    def __init__(self, template_name: str, version: str = "1.0.0"):
        self.metadata = TemplateMetadata(
            name=template_name,
            version=version,
            description=self.__doc__ or f"Workflow template: {template_name}"
        )
        self.parameters: Dict[str, TemplateParameter] = {}
        self.required_agents: List[str] = []
        self.optional_agents: List[str] = []
        self.supported_capabilities: List[str] = []
        self._compiled_workflows: Dict[str, Any] = {}
        
        # Initialize template-specific parameters
        self._initialize_parameters()
    
    @abstractmethod
    def _initialize_parameters(self):
        """Initialize template-specific parameters."""
        pass
    
    @abstractmethod
    async def create_workflow(self, params: Dict[str, Any]) -> Any:
        """
        Create workflow from template with parameters.
        
        Args:
            params: Dictionary of parameters for workflow creation
            
        Returns:
            CompiledGraph: The compiled LangGraph workflow
        """
        pass
    
    def add_parameter(self, parameter: TemplateParameter):
        """Add a parameter to the template."""
        self.parameters[parameter.name] = parameter
    
    def validate_parameters(self, params: Dict[str, Any]) -> tuple[bool, List[str]]:
        """
        Validate template parameters.
        
        Args:
            params: Parameters to validate
            
        Returns:
            Tuple of (is_valid, error_messages)
        """
        errors = []
        
        # Check all required parameters are present
        for param_name, param_def in self.parameters.items():
            if param_def.required and param_name not in params:
                errors.append(f"Required parameter '{param_name}' is missing")
                continue
            
            # Validate parameter if present
            value = params.get(param_name)
            if not param_def.validate(value):
                errors.append(f"Parameter '{param_name}' validation failed")
        
        # Check for unexpected parameters
        for param_name in params:
            if param_name not in self.parameters:
                errors.append(f"Unexpected parameter '{param_name}'")
        
        return len(errors) == 0, errors
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        """Get parameter schema for documentation/UI generation."""
        schema = {
            "template_name": self.metadata.name,
            "version": self.metadata.version,
            "parameters": {}
        }
        
        for param_name, param_def in self.parameters.items():
            schema["parameters"][param_name] = {
                "type": param_def.parameter_type.value,
                "required": param_def.required,
                "default": param_def.default_value,
                "description": param_def.description,
                "validation_rules": param_def.validation_rules
            }
        
        return schema
    
    async def create_workflow_with_validation(self, params: Dict[str, Any]) -> Any:
        """
        Create workflow with parameter validation and event publishing.
        
        Args:
            params: Parameters for workflow creation
            
        Returns:
            CompiledGraph: The compiled workflow
            
        Raises:
            ValueError: If parameter validation fails
        """
        # Validate parameters
        is_valid, errors = self.validate_parameters(params)
        if not is_valid:
            error_msg = f"Parameter validation failed: {', '.join(errors)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        # Apply default values for missing optional parameters
        validated_params = self._apply_defaults(params)
        
        # Create workflow
        workflow = await self.create_workflow(validated_params)
        
        # Publish workflow creation event
        await self._publish_workflow_event(validated_params)
        
        return workflow
    
    def _apply_defaults(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Apply default values for missing optional parameters."""
        validated_params = params.copy()
        
        for param_name, param_def in self.parameters.items():
            if param_name not in validated_params and param_def.default_value is not None:
                validated_params[param_name] = param_def.default_value
        
        return validated_params
    
    async def _publish_workflow_event(self, params: Dict[str, Any]):
        """Publish workflow started event."""
        try:
            workflow_id = params.get("workflow_id", f"{self.metadata.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            user_id = params.get("user_id", "system")
            
            event = WorkflowStartedEvent(
                workflow_id=workflow_id,
                workflow_type=self.metadata.name,
                user_id=user_id,
                agents_involved=self.required_agents + self.optional_agents,
                estimated_duration=self.metadata.estimated_duration
            )
            
            await event_bus.publish(event)
            logger.debug(f"Published workflow started event for {workflow_id}")
            
        except Exception as e:
            logger.error(f"Error publishing workflow event: {e}")
    
    def get_metadata(self) -> TemplateMetadata:
        """Get template metadata."""
        return self.metadata
    
    def update_metadata(self, **kwargs):
        """Update template metadata."""
        for key, value in kwargs.items():
            if hasattr(self.metadata, key):
                setattr(self.metadata, key, value)
        
        self.metadata.updated_at = datetime.now()
    
    def get_cache_key(self, params: Dict[str, Any]) -> str:
        """Generate cache key for workflow compilation."""
        # Create a deterministic key based on template and parameters
        param_str = "_".join(f"{k}:{v}" for k, v in sorted(params.items()))
        return f"{self.metadata.name}_{self.metadata.version}_{hash(param_str)}"
    
    def __str__(self) -> str:
        return f"WorkflowTemplate({self.metadata.name} v{self.metadata.version})"
    
    def __repr__(self) -> str:
        return (f"WorkflowTemplate(name='{self.metadata.name}', "
                f"version='{self.metadata.version}', "
                f"parameters={len(self.parameters)})")
