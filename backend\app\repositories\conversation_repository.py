"""
Conversation Repository Implementation.

Specialized repository for Conversation entity operations with conversation-specific
business logic and optimizations.
"""

import logging
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc

from ..models.database_models import Conversation, Message
from ..models.schemas import ConversationCreate, ConversationUpdate, MessageCreate, ConversationFilterParams
from .base_repository import BaseRepository, RepositoryError

logger = logging.getLogger(__name__)


class ConversationRepository(BaseRepository[Conversation]):
    """
    Specialized repository for Conversation entity operations.
    
    Provides conversation-specific methods for managing chat conversations,
    message history, and conversation metadata.
    """
    
    def __init__(self, db: Session):
        super().__init__(db, Conversation)
    
    def get_user_conversations(
        self,
        user_id: str,
        skip: int = 0,
        limit: int = 50,
        include_archived: bool = False
    ) -> List[Conversation]:
        """Get conversations for a specific user."""
        try:
            filters = {"user_id": user_id}
            
            if not include_archived:
                filters["is_archived"] = False
            
            return self.get_multi(
                skip=skip,
                limit=limit,
                order_by="updated_at",
                desc_order=True,
                **filters
            )
            
        except Exception as e:
            self.logger.error(f"Error getting conversations for user {user_id}: {e}")
            raise RepositoryError(
                "Failed to retrieve user conversations",
                entity_type="Conversation"
            )
    
    def get_active_conversations(
        self,
        user_id: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Conversation]:
        """Get active (non-archived) conversations."""
        try:
            filters = {"is_archived": False}
            
            if user_id:
                filters["user_id"] = user_id
            
            return self.get_multi(
                skip=skip,
                limit=limit,
                order_by="updated_at",
                desc_order=True,
                **filters
            )
            
        except Exception as e:
            self.logger.error(f"Error getting active conversations: {e}")
            raise RepositoryError(
                "Failed to retrieve active conversations",
                entity_type="Conversation"
            )
    
    def search_conversations(
        self,
        user_id: str,
        query: str,
        skip: int = 0,
        limit: int = 50
    ) -> List[Conversation]:
        """Search conversations by title or content."""
        try:
            base_query = self.db.query(Conversation).filter(
                and_(
                    Conversation.user_id == user_id,
                    Conversation.is_archived == False
                )
            )
            
            # Search in title and summary
            search_filter = or_(
                Conversation.title.ilike(f"%{query}%"),
                Conversation.summary.ilike(f"%{query}%")
            )
            
            base_query = base_query.filter(search_filter)
            base_query = self._apply_pagination(base_query, skip, limit)
            base_query = base_query.order_by(desc(Conversation.updated_at))
            
            return base_query.all()
            
        except Exception as e:
            self.logger.error(f"Error searching conversations for user {user_id}: {e}")
            raise RepositoryError(
                "Failed to search conversations",
                entity_type="Conversation"
            )
    
    def create_conversation(
        self,
        conversation_data: ConversationCreate,
        user_id: str,
        **kwargs
    ) -> Conversation:
        """Create a new conversation for a user."""
        try:
            # Add user_id to conversation data
            create_data = conversation_data.model_dump()
            create_data["user_id"] = user_id
            
            # Set default values
            create_data.setdefault("is_archived", False)
            create_data.setdefault("message_count", 0)
            
            return self.create(create_data, **kwargs)
            
        except Exception as e:
            self.logger.error(f"Error creating conversation for user {user_id}: {e}")
            raise RepositoryError(
                "Failed to create conversation",
                entity_type="Conversation"
            )
    
    def archive_conversation(self, conversation_id: str, user_id: str) -> Optional[Conversation]:
        """Archive a conversation (soft delete)."""
        try:
            # Verify ownership
            conversation = self.get(conversation_id)
            if not conversation or conversation.user_id != user_id:
                return None
            
            return self.update(conversation_id, {"is_archived": True})
            
        except Exception as e:
            self.logger.error(f"Error archiving conversation {conversation_id}: {e}")
            raise RepositoryError(
                "Failed to archive conversation",
                entity_type="Conversation"
            )
    
    def unarchive_conversation(self, conversation_id: str, user_id: str) -> Optional[Conversation]:
        """Unarchive a conversation."""
        try:
            # Verify ownership
            conversation = self.get(conversation_id)
            if not conversation or conversation.user_id != user_id:
                return None
            
            return self.update(conversation_id, {"is_archived": False})
            
        except Exception as e:
            self.logger.error(f"Error unarchiving conversation {conversation_id}: {e}")
            raise RepositoryError(
                "Failed to unarchive conversation",
                entity_type="Conversation"
            )
    
    def update_conversation_title(
        self,
        conversation_id: str,
        title: str,
        user_id: str
    ) -> Optional[Conversation]:
        """Update conversation title."""
        try:
            # Verify ownership
            conversation = self.get(conversation_id)
            if not conversation or conversation.user_id != user_id:
                return None
            
            return self.update(conversation_id, {"title": title})
            
        except Exception as e:
            self.logger.error(f"Error updating conversation title {conversation_id}: {e}")
            raise RepositoryError(
                "Failed to update conversation title",
                entity_type="Conversation"
            )
    
    def increment_message_count(self, conversation_id: str) -> Optional[Conversation]:
        """Increment the message count for a conversation."""
        try:
            conversation = self.get(conversation_id)
            if not conversation:
                return None
            
            new_count = (conversation.message_count or 0) + 1
            return self.update(conversation_id, {
                "message_count": new_count,
                "updated_at": datetime.now(timezone.utc)
            })
            
        except Exception as e:
            self.logger.error(f"Error incrementing message count for {conversation_id}: {e}")
            raise RepositoryError(
                "Failed to increment message count",
                entity_type="Conversation"
            )
    
    def update_conversation_summary(
        self,
        conversation_id: str,
        summary: str,
        user_id: str
    ) -> Optional[Conversation]:
        """Update conversation summary."""
        try:
            # Verify ownership
            conversation = self.get(conversation_id)
            if not conversation or conversation.user_id != user_id:
                return None
            
            return self.update(conversation_id, {"summary": summary})
            
        except Exception as e:
            self.logger.error(f"Error updating conversation summary {conversation_id}: {e}")
            raise RepositoryError(
                "Failed to update conversation summary",
                entity_type="Conversation"
            )
    
    def get_conversation_stats(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """Get conversation statistics."""
        try:
            base_query = self.db.query(Conversation)
            
            if user_id:
                base_query = base_query.filter(Conversation.user_id == user_id)
            
            total_conversations = base_query.count()
            active_conversations = base_query.filter(Conversation.is_archived == False).count()
            archived_conversations = base_query.filter(Conversation.is_archived == True).count()
            
            # Get average message count
            avg_messages = base_query.with_entities(
                func.avg(Conversation.message_count)
            ).scalar() or 0
            
            # Get recent conversations (last 7 days)
            seven_days_ago = datetime.now(timezone.utc).replace(
                day=datetime.now(timezone.utc).day - 7
            )
            
            recent_conversations = base_query.filter(
                Conversation.created_at >= seven_days_ago
            ).count()
            
            return {
                "total_conversations": total_conversations,
                "active_conversations": active_conversations,
                "archived_conversations": archived_conversations,
                "recent_conversations": recent_conversations,
                "average_message_count": round(float(avg_messages), 2)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting conversation statistics: {e}")
            raise RepositoryError(
                "Failed to retrieve conversation statistics",
                entity_type="Conversation"
            )
    
    def get_conversations_by_date_range(
        self,
        user_id: str,
        start_date: datetime,
        end_date: datetime,
        skip: int = 0,
        limit: int = 100
    ) -> List[Conversation]:
        """Get conversations within a date range."""
        try:
            query = self.db.query(Conversation).filter(
                and_(
                    Conversation.user_id == user_id,
                    Conversation.created_at >= start_date,
                    Conversation.created_at <= end_date
                )
            )

            query = self._apply_pagination(query, skip, limit)
            query = query.order_by(desc(Conversation.created_at))

            return query.all()

        except Exception as e:
            self.logger.error(f"Error getting conversations by date range: {e}")
            raise RepositoryError(
                "Failed to retrieve conversations by date range",
                entity_type="Conversation"
            )

    # Convenience methods to match database.py function signatures
    def get_conversation(self, conversation_id: str) -> Optional[Conversation]:
        """Get a conversation by ID. Convenience method to match database.py signature."""
        return self.get(conversation_id)

    def update_conversation(self, conversation_id: str, **update_data) -> Optional[Conversation]:
        """Update a conversation. Convenience method to match database.py signature."""
        if not update_data:
            # Just update the timestamp
            update_data = {"updated_at": datetime.now(timezone.utc)}
        return self.update(conversation_id, update_data)

    def update_conversation_timestamp(self, conversation_id: str) -> Optional[Conversation]:
        """Update conversation timestamp. Convenience method for chat.py."""
        return self.update_conversation(conversation_id)
    
    def bulk_archive_conversations(
        self,
        conversation_ids: List[str],
        user_id: str
    ) -> int:
        """Bulk archive conversations for a user."""
        try:
            # Verify ownership and update
            updated_count = self.db.query(Conversation).filter(
                and_(
                    Conversation.id.in_(conversation_ids),
                    Conversation.user_id == user_id
                )
            ).update(
                {
                    "is_archived": True,
                    "updated_at": datetime.now(timezone.utc)
                },
                synchronize_session=False
            )
            
            self.db.commit()
            
            self._log_audit("BULK_ARCHIVE", details={
                "user_id": user_id,
                "conversation_ids": conversation_ids,
                "updated_count": updated_count
            })
            
            return updated_count
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error bulk archiving conversations: {e}")
            raise RepositoryError(
                "Failed to bulk archive conversations",
                entity_type="Conversation"
            )
    
    def delete_old_archived_conversations(
        self,
        days_old: int = 90,
        user_id: Optional[str] = None
    ) -> int:
        """Delete archived conversations older than specified days."""
        try:
            cutoff_date = datetime.now(timezone.utc).replace(
                day=datetime.now(timezone.utc).day - days_old
            )
            
            query = self.db.query(Conversation).filter(
                and_(
                    Conversation.is_archived == True,
                    Conversation.updated_at < cutoff_date
                )
            )
            
            if user_id:
                query = query.filter(Conversation.user_id == user_id)
            
            deleted_count = query.delete(synchronize_session=False)
            self.db.commit()
            
            self._log_audit("DELETE_OLD_ARCHIVED", details={
                "days_old": days_old,
                "user_id": user_id,
                "deleted_count": deleted_count,
                "cutoff_date": cutoff_date.isoformat()
            })
            
            return deleted_count
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error deleting old archived conversations: {e}")
            raise RepositoryError(
                "Failed to delete old archived conversations",
                entity_type="Conversation"
            )
