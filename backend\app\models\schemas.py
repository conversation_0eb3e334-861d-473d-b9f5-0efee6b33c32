"""
Pydantic Schemas for Repository Pattern.

Provides organized Pydantic models for API requests and responses.
These schemas are used with repositories for type-safe operations.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Union, Generic, TypeVar
from pydantic import BaseModel, Field, EmailStr

T = TypeVar('T')

# Re-export existing Pydantic models
from .auth import (
    UserBase, UserCreate, UserUpdate, UserInDB, User as UserResponse,
    Token, TokenData, DeviceInfo
)

from .chat import (
    ConversationBase, ConversationCreate, ConversationUpdate, ConversationResponse,
    ConversationListResponse, MessageBase, MessageCreate, MessageUpdate, MessageResponse
)

from .business_profile import (
    BusinessProfileBase, BusinessProfileCreate, BusinessProfileUpdate, BusinessProfileResponse,
    BusinessType, BusinessSize, BusinessStage
)

from .persona import (
    PersonaBase, PersonaCreate, PersonaUpdate, PersonaResponse,
    PersonaVersionBase, PersonaVersionCreate, PersonaVersionUpdate, PersonaVersionResponse
)

from .provider import (
    ProviderBase, ProviderApiKeyCreate, ProviderApiKeyUpdate, ProviderApiKeyResponse
)

from .data_source import (
    DataSourceBase, DataSourceCreate, DataSourceUpdate, DataSourceResponse
)

# Additional schemas for repository operations
class BaseSchema(BaseModel):
    """Base schema with common configuration."""
    
    class Config:
        from_attributes = True
        populate_by_name = True


class PaginationParams(BaseModel):
    """Standard pagination parameters."""
    skip: int = Field(0, ge=0, description="Number of records to skip")
    limit: int = Field(100, ge=1, le=1000, description="Maximum number of records to return")


class PaginatedResponse(BaseModel, Generic[T]):
    """Generic paginated response."""
    items: List[T]
    total: int
    skip: int
    limit: int
    has_more: bool = False
    
    def __init__(self, **data):
        super().__init__(**data)
        self.has_more = (self.skip + len(self.items)) < self.total


class FilterParams(BaseModel):
    """Base filter parameters."""
    search: Optional[str] = Field(None, description="Search term")
    is_active: Optional[bool] = Field(None, description="Filter by active status")
    created_after: Optional[datetime] = Field(None, description="Filter by creation date")
    created_before: Optional[datetime] = Field(None, description="Filter by creation date")


class SortParams(BaseModel):
    """Sorting parameters."""
    sort_by: str = Field("created_at", description="Field to sort by")
    sort_order: str = Field("desc", pattern="^(asc|desc)$", description="Sort order")


# User-specific schemas
class UserFilterParams(FilterParams):
    """User-specific filter parameters."""
    is_verified: Optional[bool] = Field(None, description="Filter by verification status")
    is_superuser: Optional[bool] = Field(None, description="Filter by superuser status")
    selected_industry: Optional[str] = Field(None, description="Filter by selected industry")


class UserStats(BaseModel):
    """User statistics."""
    conversation_count: int = 0
    message_count: int = 0
    file_count: int = 0
    task_count: int = 0
    business_profile_count: int = 0
    last_activity: Optional[datetime] = None


class UserWithStats(UserResponse):
    """User response with statistics."""
    stats: UserStats


# Conversation-specific schemas
class ConversationFilterParams(FilterParams):
    """Conversation-specific filter parameters."""
    persona_id: Optional[str] = Field(None, description="Filter by persona ID")
    is_archived: Optional[bool] = Field(None, description="Filter by archived status")
    user_id: Optional[int] = Field(None, description="Filter by user ID")


class ConversationStats(BaseModel):
    """Conversation statistics."""
    message_count: int = 0
    last_message_at: Optional[datetime] = None
    total_tokens: Optional[int] = None


class ConversationWithStats(ConversationResponse):
    """Conversation response with statistics."""
    stats: ConversationStats


# Message-specific schemas
class MessageFilterParams(FilterParams):
    """Message-specific filter parameters."""
    conversation_id: Optional[str] = Field(None, description="Filter by conversation ID")
    sender: Optional[str] = Field(None, description="Filter by sender (user/ai)")
    parent_message_id: Optional[str] = Field(None, description="Filter by parent message")


# Business Profile-specific schemas
class BusinessProfileFilterParams(FilterParams):
    """Business profile-specific filter parameters."""
    industry: Optional[str] = Field(None, description="Filter by industry")
    business_type: Optional[BusinessType] = Field(None, description="Filter by business type")
    business_size: Optional[BusinessSize] = Field(None, description="Filter by business size")
    user_id: Optional[int] = Field(None, description="Filter by user ID")


class BusinessProfileStats(BaseModel):
    """Business profile statistics."""
    conversation_count: int = 0
    data_source_count: int = 0
    last_activity: Optional[datetime] = None


class BusinessProfileWithStats(BusinessProfileResponse):
    """Business profile response with statistics."""
    stats: BusinessProfileStats


# File-specific schemas
class FileBase(BaseSchema):
    """Base file schema."""
    filename: str
    file_size: int
    num_rows: Optional[int] = None
    columns: Optional[List[str]] = None


class FileCreate(FileBase):
    """File creation schema."""
    file_path: str


class FileUpdate(BaseModel):
    """File update schema."""
    filename: Optional[str] = None
    is_active: Optional[bool] = None


class FileResponse(FileBase):
    """File response schema."""
    id: str
    user_id: int
    file_path: str
    created_at: datetime
    updated_at: datetime


class FileFilterParams(FilterParams):
    """File-specific filter parameters."""
    user_id: Optional[int] = Field(None, description="Filter by user ID")
    file_type: Optional[str] = Field(None, description="Filter by file type")
    min_size: Optional[int] = Field(None, description="Minimum file size")
    max_size: Optional[int] = Field(None, description="Maximum file size")


# Task-specific schemas
class TaskBase(BaseSchema):
    """Base task schema."""
    task_type: str
    status: str = "pending"
    message: Optional[str] = None


class TaskCreate(TaskBase):
    """Task creation schema."""
    pass


class TaskUpdate(BaseModel):
    """Task update schema."""
    status: Optional[str] = None
    message: Optional[str] = None
    result_file_path: Optional[str] = None


class TaskResponse(TaskBase):
    """Task response schema."""
    id: str
    user_id: int
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None
    result_file_path: Optional[str] = None


class TaskFilterParams(FilterParams):
    """Task-specific filter parameters."""
    user_id: Optional[int] = Field(None, description="Filter by user ID")
    task_type: Optional[str] = Field(None, description="Filter by task type")
    status: Optional[str] = Field(None, description="Filter by status")


# Export all schemas
__all__ = [
    # Base schemas
    "BaseSchema",
    "PaginationParams", 
    "PaginatedResponse",
    "FilterParams",
    "SortParams",
    
    # User schemas
    "UserBase",
    "UserCreate", 
    "UserUpdate",
    "UserInDB",
    "UserResponse",
    "UserFilterParams",
    "UserStats",
    "UserWithStats",
    
    # Conversation schemas
    "ConversationBase",
    "ConversationCreate",
    "ConversationUpdate", 
    "ConversationResponse",
    "ConversationListResponse",
    "ConversationFilterParams",
    "ConversationStats",
    "ConversationWithStats",
    
    # Message schemas
    "MessageBase",
    "MessageCreate",
    "MessageUpdate",
    "MessageResponse", 
    "MessageFilterParams",
    
    # Business Profile schemas
    "BusinessProfileBase",
    "BusinessProfileCreate",
    "BusinessProfileUpdate",
    "BusinessProfileResponse",
    "BusinessProfileFilterParams", 
    "BusinessProfileStats",
    "BusinessProfileWithStats",
    "BusinessType",
    "BusinessSize", 
    "BusinessStage",
    
    # File schemas
    "FileBase",
    "FileCreate",
    "FileUpdate",
    "FileResponse",
    "FileFilterParams",
    
    # Task schemas
    "TaskBase", 
    "TaskCreate",
    "TaskUpdate",
    "TaskResponse",
    "TaskFilterParams",
    
    # Auth schemas
    "Token",
    "TokenData",
    "DeviceInfo",
    
    # Persona schemas
    "PersonaBase"
]
