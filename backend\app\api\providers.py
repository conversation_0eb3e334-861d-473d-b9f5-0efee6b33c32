"""
Provider API endpoints for the Datagenius backend.

This module provides API endpoints for AI providers.
"""

import logging
from typing import Optional, List, Dict
from fastapi import APIRouter, Depends, HTTPException, Query

from ..models.provider import (
    ProviderBase, ProviderListResponse, ProviderResponse,
    ProviderApiKeyCreate, ProviderApiKeyResponse, ProviderSettings,
    ProviderModel, ProviderModelsResponse
)
from ..models.auth import User
from ..dependencies import get_provider_api_key_repository, get_user_repository
from ..repositories.provider_api_key_repository import ProviderApiKeyRepository
from ..repositories.user_repository import UserRepository
from ..auth import get_current_active_user
from .. import config

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/providers", tags=["Providers"])

# Define a function to get global provider availability
def get_global_provider_availability():
    """
    Get the availability of providers based on global API keys in .env file.

    Returns:
        Dictionary mapping provider IDs to availability status
    """
    return {
        "openai": bool(config.OPENAI_API_KEY),
        "groq": bool(config.GROQ_API_KEY),
        "gemini": bool(config.GEMINI_API_KEY),
        "openrouter": bool(config.OPENROUTER_API_KEY),
        "anthropic": False,  # Not currently supported globally
        "requesty": False    # Not currently supported globally
    }

# Define available providers
AVAILABLE_PROVIDERS = [
    {
        "id": "openai",
        "name": "OpenAI",
        "description": "OpenAI's GPT models for text generation and more.",
        "endpoint": config.OPENAI_ENDPOINT,
        "is_available": bool(config.OPENAI_API_KEY)
    },
    {
        "id": "anthropic",
        "name": "Anthropic",
        "description": "Anthropic's Claude models for text generation.",
        "endpoint": None,
        "is_available": False
    },
    {
        "id": "groq",
        "name": "Groq",
        "description": "Groq's high-performance inference API for LLMs.",
        "endpoint": config.GROQ_ENDPOINT,
        "is_available": bool(config.GROQ_API_KEY)
    },
    {
        "id": "gemini",
        "name": "Google Gemini",
        "description": "Google's Gemini models for text and multimodal tasks.",
        "endpoint": config.GEMINI_ENDPOINT,
        "is_available": bool(config.GEMINI_API_KEY)
    },
    {
        "id": "openrouter",
        "name": "OpenRouter",
        "description": "Access to multiple LLM providers through a single API.",
        "endpoint": config.OPENROUTER_ENDPOINT,
        "is_available": bool(config.OPENROUTER_API_KEY)
    },
    {
        "id": "requesty",
        "name": "Requesty",
        "description": "Requesty AI for text generation and more.",
        "endpoint": None,
        "is_available": False
    }
]

# Models are now loaded dynamically from provider APIs


@router.get("/settings", response_model=ProviderSettings)
async def get_provider_settings(
    user_repo: UserRepository = Depends(get_user_repository),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get provider settings.

    Args:
        db: Database session
        current_user: Current authenticated user

    Returns:
        Provider settings
    """
    logger.info(f"User {current_user.id} getting provider settings")

    try:
        # Get user from repository
        user = user_repo.get_by_id(current_user.id)
        if not user:
            logger.warning(f"User {current_user.id} not found, returning default settings")
            return ProviderSettings(default_provider="", use_local_llm=False)

        # Extract provider settings from user attributes
        settings = {
            "default_provider": user.default_provider or "",
            "use_local_llm": user.use_local_llm if user.use_local_llm is not None else False,
            "memory_service_provider": user.memory_service_provider or "",
            "memory_service_model": user.memory_service_model or "",
            "concierge_agent_provider": user.concierge_agent_provider or "",
            "concierge_agent_model": user.concierge_agent_model or ""
        }

        logger.info(f"Raw settings from database: {settings}")

        # Create default settings only if user doesn't have any settings at all
        if not any(settings.values()):
            logger.info(f"Creating default provider settings for user {current_user.id}")
            default_settings = {
                "default_provider": "",
                "use_local_llm": False,
                "memory_service_provider": "",
                "memory_service_model": "",
                "concierge_agent_provider": "",
                "concierge_agent_model": ""
            }

            # Save default settings to database
            logger.info(f"Saving default settings to database: {default_settings}")
            user_repo.update(current_user.id, default_settings)
            logger.info(f"Default settings saved successfully")

            logger.info(f"Default provider settings created for user {current_user.id}")
            return ProviderSettings(**default_settings)

        logger.info(f"Provider settings found for user {current_user.id}: {settings}")

        # Validate settings against the model
        try:
            # Ensure default_provider is not None
            if settings.get('default_provider') is None:
                settings['default_provider'] = ""

            provider_settings = ProviderSettings(**settings)
            logger.info(f"Provider settings validated successfully: {provider_settings.model_dump()}")
            return provider_settings
        except Exception as e:
            logger.error(f"Error validating provider settings: {str(e)}")

            # Instead of creating default settings, try to fix the existing settings
            logger.info(f"Attempting to fix settings after validation error")

            # Create a fixed version of the settings without overwriting existing values
            fixed_settings = {
                "default_provider": settings.get('default_provider', ""),
                "use_local_llm": settings.get('use_local_llm', False)
            }

            # Ensure default_provider is a string
            if fixed_settings["default_provider"] is None:
                fixed_settings["default_provider"] = ""

            logger.info(f"Fixed settings: {fixed_settings}")

            try:
                # Create a valid ProviderSettings object
                provider_settings = ProviderSettings(**fixed_settings)
                logger.info(f"Successfully created valid settings object: {provider_settings.model_dump()}")
                return provider_settings
            except Exception as fix_error:
                logger.error(f"Error creating fixed settings: {str(fix_error)}")
                # Only as a last resort, return empty default settings without saving them
                return ProviderSettings(default_provider="", use_local_llm=False)
    except Exception as e:
        logger.error(f"Error getting provider settings: {str(e)}", exc_info=True)

        # Don't raise an exception, just return default settings
        logger.info(f"Returning default settings due to error, but not saving them")
        default_settings = ProviderSettings(default_provider="", use_local_llm=False)
        return default_settings


@router.post("/settings", response_model=ProviderSettings)
async def set_provider_settings(
    settings: ProviderSettings,
    user_repo: UserRepository = Depends(get_user_repository),
    current_user: User = Depends(get_current_active_user)
):
    """
    Set provider settings.

    Args:
        settings: Provider settings
        db: Database session
        current_user: Current authenticated user

    Returns:
        Updated provider settings
    """
    logger.info(f"User {current_user.id} setting provider settings: {settings.model_dump()}")

    try:
        # Log the settings model
        logger.info(f"Settings model before saving: {settings}")

        # Log the settings being saved
        logger.info(f"Saving settings to database: {settings.model_dump()}")

        # Save settings to database using user repository
        user_repo.update(current_user.id, settings.model_dump())
        logger.info(f"Settings saved successfully")

        # Verify settings were saved correctly by retrieving them
        updated_user = user_repo.get_by_id(current_user.id)
        if updated_user:
            saved_settings = {
                "default_provider": updated_user.default_provider or "",
                "use_local_llm": updated_user.use_local_llm if updated_user.use_local_llm is not None else False,
                "memory_service_provider": updated_user.memory_service_provider or "",
                "memory_service_model": updated_user.memory_service_model or "",
                "concierge_agent_provider": updated_user.concierge_agent_provider or "",
                "concierge_agent_model": updated_user.concierge_agent_model or ""
            }
            logger.info(f"Verified saved settings: {saved_settings}")

        return settings
    except Exception as e:
        logger.error(f"Error setting provider settings: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error saving provider settings: {str(e)}")


@router.get("", response_model=ProviderListResponse)
async def list_providers(
    provider_api_key_repo: ProviderApiKeyRepository = Depends(get_provider_api_key_repository),
    current_user: User = Depends(get_current_active_user)
):
    """
    List all available providers.

    Args:
        db: Database session
        current_user: Current authenticated user

    Returns:
        List of available providers
    """
    logger.info(f"User {current_user.id} requested list of providers")

    # Get user's API keys
    user_api_keys = provider_api_key_repo.get_user_provider_api_keys(current_user.id)
    user_provider_ids = [key.provider_id for key in user_api_keys]

    # Update availability based on user's API keys
    providers = []
    for provider in AVAILABLE_PROVIDERS:
        provider_copy = provider.copy()
        if provider_copy["id"] in user_provider_ids:
            provider_copy["is_available"] = True
        providers.append(ProviderBase(**provider_copy))

    return {"providers": providers}


@router.get("/{provider_id}", response_model=ProviderResponse)
async def get_provider(
    provider_id: str,
    provider_api_key_repo: ProviderApiKeyRepository = Depends(get_provider_api_key_repository),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get a specific provider.

    Args:
        provider_id: ID of the provider
        db: Database session
        current_user: Current authenticated user

    Returns:
        Provider information
    """
    logger.info(f"User {current_user.id} requested provider {provider_id}")

    # Find the provider
    provider = next((p for p in AVAILABLE_PROVIDERS if p["id"] == provider_id), None)
    if not provider:
        logger.error(f"Provider {provider_id} not found")
        raise HTTPException(status_code=404, detail=f"Provider {provider_id} not found")

    # Check if the user has an API key for this provider
    api_key = provider_api_key_repo.get_provider_api_key(current_user.id, provider_id)
    if api_key:
        provider = provider.copy()
        provider["is_available"] = True

    return {"provider": ProviderBase(**provider)}


@router.post("/api-key", response_model=ProviderApiKeyResponse)
async def set_api_key(
    api_key_data: ProviderApiKeyCreate,
    provider_api_key_repo: ProviderApiKeyRepository = Depends(get_provider_api_key_repository),
    current_user: User = Depends(get_current_active_user)
):
    """
    Set an API key for a provider.

    Args:
        api_key_data: Provider API key data
        db: Database session
        current_user: Current authenticated user

    Returns:
        Success message
    """
    logger.info(f"User {current_user.id} setting API key for provider {api_key_data.provider_id}")

    # Check if the provider exists
    provider = next((p for p in AVAILABLE_PROVIDERS if p["id"] == api_key_data.provider_id), None)
    if not provider:
        logger.error(f"Provider {api_key_data.provider_id} not found")
        raise HTTPException(status_code=404, detail=f"Provider {api_key_data.provider_id} not found")

    # Validate the API key by making a test request to the provider's API
    is_valid = False
    message = ""

    try:
        # Import here to avoid circular imports
        import sys
        import os

        # Add the parent directory to sys.path to allow importing from agents
        parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        if parent_dir not in sys.path:
            sys.path.insert(0, parent_dir)

        from agents.utils.provider_utils import validate_api_key

        # Validate the API key
        is_valid, message = validate_api_key(api_key_data.provider_id, api_key_data.api_key)
    except Exception as e:
        logger.error(f"Error validating API key: {str(e)}", exc_info=True)
        is_valid = False
        message = f"Error validating API key: {str(e)}"

    # Only save the API key if it's valid
    if is_valid:
        provider_api_key_repo.create_or_update_provider_api_key(current_user.id, api_key_data.provider_id, api_key_data.api_key)

    return {
        "provider_id": api_key_data.provider_id,
        "is_valid": is_valid,
        "message": message or (f"API key for {provider['name']} " + ("saved successfully" if is_valid else "is invalid"))
    }


@router.delete("/api-key/{provider_id}", response_model=Dict[str, str])
async def delete_api_key(
    provider_id: str,
    provider_api_key_repo: ProviderApiKeyRepository = Depends(get_provider_api_key_repository),
    current_user: User = Depends(get_current_active_user)
):
    """
    Delete an API key for a provider.

    Args:
        provider_id: ID of the provider
        db: Database session
        current_user: Current authenticated user

    Returns:
        Success message
    """
    logger.info(f"User {current_user.id} deleting API key for provider {provider_id}")

    # Check if the provider exists
    provider = next((p for p in AVAILABLE_PROVIDERS if p["id"] == provider_id), None)
    if not provider:
        logger.error(f"Provider {provider_id} not found")
        raise HTTPException(status_code=404, detail=f"Provider {provider_id} not found")

    # Delete the API key
    success = provider_api_key_repo.delete_provider_api_key(current_user.id, provider_id)
    if not success:
        logger.error(f"API key for provider {provider_id} not found")
        raise HTTPException(status_code=404, detail=f"API key for provider {provider_id} not found")

    return {"message": f"API key for {provider['name']} deleted successfully"}


@router.get("/{provider_id}/models", response_model=ProviderModelsResponse)
async def get_provider_models(
    provider_id: str,
    provider_api_key_repo: ProviderApiKeyRepository = Depends(get_provider_api_key_repository),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get available models for a provider.

    Args:
        provider_id: ID of the provider
        db: Database session
        current_user: Current authenticated user

    Returns:
        List of available models for the provider
    """
    logger.info(f"User {current_user.id} requested models for provider {provider_id}")

    # Check if provider exists
    provider = next((p for p in AVAILABLE_PROVIDERS if p["id"] == provider_id), None)
    if not provider:
        logger.error(f"Provider {provider_id} not found")
        raise HTTPException(status_code=404, detail=f"Provider {provider_id} not found")

    # Get API key for the provider
    api_key = None
    endpoint = provider.get("endpoint")

    # Try user-specific API key first
    user_api_key = provider_api_key_repo.get_provider_api_key(current_user.id, provider_id)
    if user_api_key:
        api_key = user_api_key.api_key
    else:
        # Fall back to global API key
        if provider_id == "openai":
            api_key = config.OPENAI_API_KEY
        elif provider_id == "groq":
            api_key = config.GROQ_API_KEY
        elif provider_id == "gemini":
            api_key = config.GEMINI_API_KEY
        elif provider_id == "openrouter":
            api_key = config.OPENROUTER_API_KEY
        # Add other providers as needed

    # Fetch models dynamically - no more fallback to hardcoded models
    try:
        # Import the provider models utility
        from ..utils.provider_models import fetch_provider_models

        # Log the API key and endpoint for debugging (mask the API key)
        masked_key = "None" if not api_key else f"{api_key[:4]}...{api_key[-4:]}" if len(api_key) > 8 else "***"
        logger.info(f"Fetching models for provider {provider_id} with API key {masked_key} and endpoint {endpoint}")

        # If we don't have an API key or endpoint, return an empty list with a message
        if not api_key or not endpoint:
            logger.warning(f"Missing API key or endpoint for provider {provider_id}")
            return {"models": [], "message": "API key or endpoint not configured"}

        # Fetch models from the provider API
        models_data = fetch_provider_models(provider_id, api_key, endpoint)

        # Log the number of models fetched
        logger.info(f"Fetched {len(models_data)} models for provider {provider_id}")

        # If debugging, log the first few models
        if len(models_data) > 0:
            logger.debug(f"First model: {models_data[0]}")

        # Convert to ProviderModel format
        models = [
            ProviderModel(
                id=model.get("id"),
                name=model.get("name"),
                description=model.get("description", ""),
                context_length=model.get("context_length"),
                created=model.get("created"),
                provider=model.get("provider"),
                pricing=model.get("pricing"),
                category=model.get("category")
            )
            for model in models_data
        ]

        # Return the models
        return {"models": models}
    except Exception as e:
        logger.error(f"Error fetching models for provider {provider_id}: {str(e)}", exc_info=True)
        # Return an empty list with the error message
        return {"models": [], "message": f"Error fetching models: {str(e)}"}


@router.get("/global-availability", response_model=Dict[str, bool])
@router.get("/global_availability", response_model=Dict[str, bool])  # Add alternative URL with underscore
async def get_global_availability(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get the availability of providers based on global API keys in .env file.

    Args:
        current_user: Current authenticated user

    Returns:
        Dictionary mapping provider IDs to availability status
    """
    logger.info(f"User {current_user.id} requested global provider availability")

    # Get global provider availability
    availability = get_global_provider_availability()

    # Log the availability for debugging
    logger.info(f"Global provider availability: {availability}")

    # Log the environment variables for debugging
    logger.info(f"GROQ_API_KEY set: {bool(config.GROQ_API_KEY)}")
    logger.info(f"OPENAI_API_KEY set: {bool(config.OPENAI_API_KEY)}")
    logger.info(f"GEMINI_API_KEY set: {bool(config.GEMINI_API_KEY)}")
    logger.info(f"OPENROUTER_API_KEY set: {bool(config.OPENROUTER_API_KEY)}")

    return availability


@router.get("/api-key/{provider_id}/status", response_model=ProviderApiKeyResponse)
async def check_api_key_status(
    provider_id: str,
    provider_api_key_repo: ProviderApiKeyRepository = Depends(get_provider_api_key_repository),
    current_user: User = Depends(get_current_active_user)
):
    """
    Check the status of an API key for a provider.

    Args:
        provider_id: ID of the provider
        db: Database session
        current_user: Current authenticated user

    Returns:
        API key status
    """
    logger.info(f"User {current_user.id} checking API key status for provider {provider_id}")

    # Check if the provider exists
    provider = next((p for p in AVAILABLE_PROVIDERS if p["id"] == provider_id), None)
    if not provider:
        logger.error(f"Provider {provider_id} not found")
        raise HTTPException(status_code=404, detail=f"Provider {provider_id} not found")

    # Check if the user has an API key for this provider
    api_key = provider_api_key_repo.get_provider_api_key(current_user.id, provider_id)
    if not api_key:
        return {
            "provider_id": provider_id,
            "is_valid": False,
            "message": f"No API key found for {provider['name']}"
        }

    # Validate the API key by making a test request to the provider's API
    is_valid = False
    message = ""

    try:
        # Import here to avoid circular imports
        import sys
        import os

        # Add the parent directory to sys.path to allow importing from agents
        parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        if parent_dir not in sys.path:
            sys.path.insert(0, parent_dir)

        from agents.utils.provider_utils import validate_api_key

        # Validate the API key
        is_valid, message = validate_api_key(provider_id, api_key.api_key)
    except Exception as e:
        logger.error(f"Error validating API key: {str(e)}", exc_info=True)
        is_valid = False
        message = f"Error validating API key: {str(e)}"

    return {
        "provider_id": provider_id,
        "is_valid": is_valid,
        "message": message or (f"API key for {provider['name']} " + ("is valid" if is_valid else "is invalid"))
    }


