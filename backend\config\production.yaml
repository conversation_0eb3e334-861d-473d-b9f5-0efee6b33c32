# Production environment configuration
# This file contains production-specific settings that override defaults

name: "Datagenius"
version: "1.0.0"
description: "AI-powered data analysis platform"

environment:
  environment: "production"
  debug: false
  testing: false

database:
  url: "*********************************************************/datagenius"
  echo: false
  pool_size: 20
  max_overflow: 30
  pool_pre_ping: true
  pool_recycle: 3600
  connect_timeout: 10
  statement_timeout: 30000
  auto_migrate: false  # Manual migrations in production
  backup_enabled: true
  backup_schedule: "0 2 * * *"  # Daily at 2 AM
  backup_retention_days: 30

redis:
  max_connections: 50
  socket_timeout: 3
  socket_connect_timeout: 3
  retry_on_timeout: true
  health_check_interval: 30
  default_ttl: 3600  # 1 hour

security:
  jwt_secret_key: "production-secret-key-should-be-set-via-environment-variable"
  access_token_expire_minutes: 15  # Shorter for production
  refresh_token_expire_days: 7
  max_refresh_count: 10
  enforce_ip_validation: true
  ip_change_lockout: 300  # 5 minutes lockout for production
  max_upload_size: 10485760  # 10MB
  rate_limiting_enabled: true
  rate_limit_requests: 100
  rate_limit_window_minutes: 15
  rate_limit_burst: 20
  cors_allow_credentials: true
  enforce_https: true
  hsts_max_age: 31536000
  content_security_policy: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:; frame-ancestors 'none';"
  require_email_verification: true
  two_factor_auth_enabled: false  # Can be enabled later
  max_login_attempts: 3
  lockout_duration_minutes: 30

llm:
  default_provider: "groq"
  fallback_providers:
    - "groq"
    - "openai"
    - "gemini"
  default_temperature: 0.7
  default_max_tokens: 2048
  enable_caching: true
  cache_ttl_seconds: 3600  # 1 hour
  enable_streaming: true
  content_filter_enabled: true
  log_requests: true
  log_responses: false  # Don't log responses in production
  track_usage: true

email:
  enabled: true
  # SMTP settings should be configured via environment variables
  use_tls: true
  template_dir: "templates/email"

files:
  upload_dir: "/var/datagenius/uploads"
  max_upload_size: 10485760  # 10MB
  chunking_performance_profile: "balanced"
  chunking_use_adaptive: true
  chunking_enable_caching: true
  chunking_batch_size: 16
  chunking_parallel_workers: 4

vector:
  # Qdrant settings should be configured via environment variables
  qdrant_https: true
  mem0_self_hosted: false
  mem0_default_ttl: 2592000  # 30 days
  mem0_max_memories: 10000
  mem0_memory_threshold: 0.7

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "/var/log/datagenius/app.log"
  max_file_size: 10485760  # 10MB
  backup_count: 10

monitoring:
  enabled: true
  performance_tracking: true
  error_tracking: true
  alert_on_errors: true
  alert_threshold: 10
  alert_window_minutes: 5

# Phase 3: AI-Powered Systems Configuration (Production)
phase3:
  enabled: true
  optimization_level: "advanced"  # Higher optimization for production
  debug_mode: false  # Disable debug mode in production

  # Predictive Optimization
  predictive_optimization:
    enabled: true
    confidence_threshold: 0.8  # Higher threshold for production
    suggestion_limit: 5
    pattern_learning: true
    ab_testing: true  # Enable A/B testing in production
    training_interval_hours: 6  # More frequent training for better accuracy
    model_persistence: true
    model_storage_path: "/app/models/phase3"

  # Self-Healing System
  self_healing:
    enabled: true
    strategy: "balanced"  # Balanced approach for production
    failure_prediction: true
    automatic_remediation: true  # Enable automatic remediation
    retry_attempts: 3
    health_check_interval: 30  # Check every 30 seconds
    proactive_healing: true
    healing_timeout: 120

  # Advanced Collaboration
  collaboration:
    enabled: true
    team_formation: true
    skill_matching: true
    collaborative_learning: true
    consensus_building: true
    max_team_size: 5  # Full team size for production
    timeout_seconds: 300
    coordination_strategy: "adaptive"

  # Performance Monitoring
  performance_monitoring:
    enabled: true
    collection_interval: 60  # Standard collection interval
    retention_days: 30  # Standard retention for production
    alert_thresholds:
      workflow_failure_rate: 0.05  # 5% failure rate threshold
      execution_time_increase: 0.15  # 15% increase threshold
      memory_usage: 0.85  # 85% memory usage threshold
      prediction_accuracy: 0.8  # 80% accuracy threshold
      healing_success_rate: 0.9  # 90% healing success threshold
    real_time_alerts: true

  # Resource Management
  resources:
    max_concurrent_optimizations: 20  # Higher limit for production
    optimization_timeout: 180  # 3 minutes timeout
    memory_limit_mb: 2048  # 2GB memory limit
    cpu_limit_percent: 80.0
    event_queue_size: 50000  # Large queue for production
    worker_pool_size: 10

  # Machine Learning Models
  ml_models:
    auto_training: true
    training_data_retention_days: 180  # 6 months retention
    model_validation: true
    cross_validation_folds: 5
    performance_threshold: 0.75
    model_backup_enabled: true
    model_versioning: true

# Frontend URL should be set via environment variable in production
# google_redirect_uri should be set via environment variable in production
