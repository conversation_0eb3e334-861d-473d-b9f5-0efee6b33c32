# Testing environment configuration
# This file contains testing-specific settings that override defaults

name: "Datagenius Test"
version: "1.0.0-test"
description: "AI-powered data analysis platform (Testing)"

environment:
  environment: "testing"
  debug: false
  testing: true

database:
  url: "sqlite:///./test_datagenius.db"
  echo: false
  pool_size: 1
  max_overflow: 0
  pool_pre_ping: false
  auto_migrate: true
  backup_enabled: false

redis:
  url: "redis://localhost:6379/1"  # Different database for testing
  max_connections: 5
  socket_timeout: 1
  default_ttl: 300  # 5 minutes for testing

security:
  jwt_secret_key: "test-secret-key-not-for-production"
  access_token_expire_minutes: 5  # Short for testing
  refresh_token_expire_days: 1
  max_refresh_count: 5
  enforce_ip_validation: false
  ip_change_lockout: 0  # Disabled for testing
  max_upload_size: 1048576  # 1MB for testing
  rate_limiting_enabled: false
  cors_origins:
    - "http://localhost:3000"
    - "http://localhost:5173"
    - "http://testserver"
  enforce_https: false
  require_email_verification: false
  two_factor_auth_enabled: false
  max_login_attempts: 10  # Higher for testing

llm:
  default_provider: "groq"
  fallback_providers:
    - "groq"
  default_temperature: 0.1  # Lower for consistent testing
  default_max_tokens: 512  # Smaller for testing
  enable_caching: false  # Disabled for testing
  enable_streaming: false  # Disabled for testing
  content_filter_enabled: false
  log_requests: false
  log_responses: false
  track_usage: false

email:
  enabled: false
  sender: "<EMAIL>"
  smtp_server: "localhost"
  smtp_port: 1025
  use_tls: false

files:
  upload_dir: "test_uploads"
  max_upload_size: 1048576  # 1MB
  chunking_performance_profile: "fast"
  chunking_use_adaptive: false  # Disabled for consistent testing
  chunking_enable_caching: false
  chunking_batch_size: 4
  chunking_parallel_workers: 1

vector:
  qdrant_host: "localhost"
  qdrant_port: 6333
  qdrant_https: false
  mem0_self_hosted: true
  mem0_default_ttl: 300  # 5 minutes for testing
  mem0_max_memories: 100
  mem0_memory_threshold: 0.5

logging:
  level: "WARNING"  # Reduce log noise during testing
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: null  # No file logging during tests
  max_file_size: 1048576  # 1MB
  backup_count: 1

monitoring:
  enabled: false  # Disabled for testing
  performance_tracking: false
  error_tracking: false
  alert_on_errors: false

frontend_url: "http://testserver"

google_redirect_uri: "http://testserver/auth/google/callback"
