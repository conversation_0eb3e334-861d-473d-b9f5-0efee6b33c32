"""
Test Suite for Phase 4: Integration and Performance Tests

Comprehensive integration tests and performance validation for Phase 4 components
including end-to-end workflows and system performance benchmarks.
"""

import pytest
import asyncio
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch

from ..marketplace.capability_marketplace import CapabilityMarketplace
from ..marketplace.trading_engine import TradingEngine
from ..marketplace.certification_system import CertificationSystem
from ..ai.workflow_composer import AIWorkflowComposer
from ..ai.pattern_recognition import WorkflowPatternRecognition
from ..events.event_bus import event_bus, LangGraphEvent


class TestPhase4Integration:
    """Integration tests for Phase 4 components."""
    
    @pytest.fixture
    async def integrated_system(self):
        """Create an integrated system with all Phase 4 components."""
        marketplace = CapabilityMarketplace()
        trading_engine = TradingEngine()
        certification_system = CertificationSystem()
        workflow_composer = AIWorkflowComposer()
        pattern_recognition = WorkflowPatternRecognition()
        
        # Mock initialization to avoid external dependencies
        with patch.object(marketplace.registry, 'initialize'), \
             patch.object(marketplace.trading_engine, 'initialize'), \
             patch.object(marketplace.certification_system, 'initialize'), \
             patch.object(trading_engine, '_load_trading_data'), \
             patch.object(certification_system, '_load_certifications'), \
             patch.object(workflow_composer.template_manager, 'initialize'), \
             patch.object(workflow_composer.router, 'initialize'), \
             patch.object(workflow_composer, '_load_workflow_patterns'), \
             patch.object(pattern_recognition, '_load_patterns'):
            
            await marketplace.initialize()
            await trading_engine.initialize()
            await certification_system.initialize()
            await workflow_composer.initialize()
            await pattern_recognition.initialize()
        
        return {
            "marketplace": marketplace,
            "trading_engine": trading_engine,
            "certification_system": certification_system,
            "workflow_composer": workflow_composer,
            "pattern_recognition": pattern_recognition
        }
    
    @pytest.mark.asyncio
    async def test_end_to_end_capability_lifecycle(self, integrated_system):
        """Test complete capability lifecycle from listing to trading to certification."""
        marketplace = integrated_system["marketplace"]
        certification_system = integrated_system["certification_system"]
        
        # Step 1: List a capability
        capability_data = {
            "capability_id": "e2e_cap_001",
            "agent_id": "e2e_agent",
            "name": "End-to-End Test Capability",
            "description": "A capability for end-to-end testing",
            "category": "data_analysis",
            "version": "1.0.0",
            "status": "available",
            "trading_mode": "fixed_price",
            "price": 20.0
        }
        
        with patch.object(marketplace, '_get_capability_performance', 
                         return_value={"score": 0.9, "success_rate": 0.95, "avg_execution_time": 1.0}):
            capability_id = await marketplace.list_capability(capability_data)
        
        assert capability_id == "e2e_cap_001"
        assert capability_id in marketplace.listings
        
        # Step 2: Request certification
        with patch.object(certification_system, '_start_certification_process') as mock_cert:
            cert_id = await certification_system.request_certification(
                capability_id, "e2e_agent", certification_system.criteria.keys().__iter__().__next__()
            )
        
        assert cert_id.startswith("cert_")
        mock_cert.assert_called_once()
        
        # Step 3: Create a capability request
        request_data = {
            "request_id": "e2e_req_001",
            "requester_id": "e2e_user",
            "requirements": {"type": "data_analysis", "quality": "high"},
            "budget": 25.0,
            "deadline": (datetime.now() + timedelta(hours=24)).isoformat()
        }
        
        with patch.object(marketplace, '_find_capability_matches', 
                         return_value=[marketplace.listings[capability_id]]):
            request_id = await marketplace.request_capability(request_data)
        
        assert request_id == "e2e_req_001"
        assert len(marketplace.active_trades) > 0
    
    @pytest.mark.asyncio
    async def test_workflow_composition_with_marketplace_integration(self, integrated_system):
        """Test workflow composition integrated with marketplace capabilities."""
        composer = integrated_system["workflow_composer"]
        marketplace = integrated_system["marketplace"]
        
        # Add capabilities to marketplace
        capabilities = [
            {
                "capability_id": f"comp_cap_{i}",
                "agent_id": f"comp_agent_{i}",
                "name": f"Composition Capability {i}",
                "description": f"Capability {i} for composition testing",
                "category": "data_analysis" if i % 2 == 0 else "content_generation",
                "version": "1.0.0",
                "status": "available",
                "trading_mode": "fixed_price",
                "price": 10.0 + i * 5
            }
            for i in range(3)
        ]
        
        for cap_data in capabilities:
            with patch.object(marketplace, '_get_capability_performance', 
                             return_value={"score": 0.8, "success_rate": 0.9, "avg_execution_time": 1.5}):
                await marketplace.list_capability(cap_data)
        
        # Compose workflow
        requirements_text = "Analyze data and generate a comprehensive report"
        
        # Mock composition process
        with patch.object(composer.requirement_analyzer, 'analyze') as mock_analyze, \
             patch.object(composer.pattern_recognizer, 'find_patterns', return_value=[]), \
             patch.object(composer, '_generate_workflow_structure') as mock_structure, \
             patch.object(composer, '_select_optimal_agents') as mock_agents, \
             patch.object(composer.workflow_optimizer, 'optimize') as mock_optimize:
            
            # Setup mocks
            mock_analyze.return_value = [Mock(requirement_type=Mock(value="data_analysis"))]
            mock_structure.return_value = {
                "nodes": [{"id": "node_1", "type": "data_analysis"}],
                "edges": [],
                "entry_point": "node_1"
            }
            mock_agents.return_value = {"node_1": "comp_agent_0"}
            mock_optimize.return_value = {
                "nodes": [{"id": "node_1", "type": "data_analysis"}],
                "edges": [],
                "entry_point": "node_1",
                "estimated_time": 900.0,
                "estimated_cost": 15.0,
                "confidence": 0.9
            }
            
            workflow = await composer.compose_workflow(requirements_text)
            
            assert workflow is not None
            assert workflow.confidence_score == 0.9
            assert len(workflow.agent_assignments) > 0
    
    @pytest.mark.asyncio
    async def test_pattern_learning_from_marketplace_data(self, integrated_system):
        """Test pattern learning from marketplace execution data."""
        pattern_recognition = integrated_system["pattern_recognition"]
        marketplace = integrated_system["marketplace"]
        
        # Simulate workflow executions
        workflow_executions = [
            {
                "workflow_structure": {
                    "nodes": [
                        {"id": "node_1", "type": "data_collection"},
                        {"id": "node_2", "type": "data_analysis"},
                        {"id": "node_3", "type": "reporting"}
                    ],
                    "edges": [
                        {"from": "node_1", "to": "node_2"},
                        {"from": "node_2", "to": "node_3"}
                    ]
                },
                "performance": {
                    "success_rate": 0.9,
                    "execution_time": 1200.0,
                    "cost": 20.0,
                    "quality_score": 0.85
                }
            }
            for _ in range(5)  # Multiple executions of similar workflow
        ]
        
        # Mock pattern learning
        with patch.object(pattern_recognition, '_is_novel_pattern', return_value=True), \
             patch.object(pattern_recognition, '_validate_pattern_quality', return_value=True):
            
            learned_patterns = []
            for execution in workflow_executions:
                pattern_id = await pattern_recognition.learn_new_pattern(
                    execution["workflow_structure"], 
                    execution["performance"]
                )
                if pattern_id:
                    learned_patterns.append(pattern_id)
            
            # Should learn at least one pattern
            assert len(learned_patterns) > 0
    
    @pytest.mark.asyncio
    async def test_event_driven_integration(self, integrated_system):
        """Test event-driven integration between components."""
        marketplace = integrated_system["marketplace"]
        certification_system = integrated_system["certification_system"]
        
        # Test capability listing triggers certification
        capability_data = {
            "capability_id": "event_cap_001",
            "agent_id": "event_agent",
            "name": "Event Test Capability",
            "description": "Capability for event testing",
            "category": "automation",
            "version": "1.0.0",
            "status": "available",
            "trading_mode": "auction",
            "price": 15.0
        }
        
        # Mock certification request
        with patch.object(marketplace, '_get_capability_performance', 
                         return_value={"score": 0.85, "success_rate": 0.9, "avg_execution_time": 2.0}), \
             patch.object(certification_system, 'request_certification') as mock_cert_request:
            
            await marketplace.list_capability(capability_data)
            
            # Simulate the event handler being called
            event = LangGraphEvent(
                event_type="marketplace.capability_listed",
                timestamp=datetime.now(),
                source="capability_marketplace",
                data={
                    "capability_id": "event_cap_001",
                    "agent_id": "event_agent"
                }
            )
            
            await certification_system._handle_capability_listing(event)
            
            # Verify certification was requested
            mock_cert_request.assert_called_once()


class TestPhase4Performance:
    """Performance tests for Phase 4 components."""
    
    @pytest.mark.asyncio
    async def test_marketplace_performance(self):
        """Test marketplace performance under load."""
        marketplace = CapabilityMarketplace()
        
        with patch.object(marketplace.registry, 'initialize'), \
             patch.object(marketplace.trading_engine, 'initialize'), \
             patch.object(marketplace.certification_system, 'initialize'):
            await marketplace.initialize()
        
        # Performance test: List many capabilities
        start_time = time.time()
        
        capabilities = []
        for i in range(100):
            capability_data = {
                "capability_id": f"perf_cap_{i}",
                "agent_id": f"perf_agent_{i}",
                "name": f"Performance Test Capability {i}",
                "description": f"Capability {i} for performance testing",
                "category": "automation",
                "version": "1.0.0",
                "status": "available",
                "trading_mode": "fixed_price",
                "price": 10.0 + i
            }
            
            with patch.object(marketplace, '_get_capability_performance', 
                             return_value={"score": 0.8, "success_rate": 0.9, "avg_execution_time": 1.0}):
                capability_id = await marketplace.list_capability(capability_data)
                capabilities.append(capability_id)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Performance assertions
        assert len(capabilities) == 100
        assert execution_time < 10.0  # Should complete in under 10 seconds
        assert len(marketplace.listings) == 100
        
        # Test discovery performance
        start_time = time.time()
        discovered = await marketplace.discover_capabilities(limit=50)
        end_time = time.time()
        discovery_time = end_time - start_time
        
        assert len(discovered) == 50
        assert discovery_time < 1.0  # Discovery should be fast
    
    @pytest.mark.asyncio
    async def test_trading_engine_performance(self):
        """Test trading engine performance under load."""
        trading_engine = TradingEngine()
        
        with patch.object(trading_engine, '_load_trading_data'):
            await trading_engine.initialize()
        
        # Create multiple trades
        start_time = time.time()
        
        trades = []
        for i in range(50):
            mock_request = Mock()
            mock_request.request_id = f"perf_req_{i}"
            mock_request.requester_id = f"perf_user_{i}"
            mock_request.requirements = {"type": "automation"}
            mock_request.budget = 50.0 + i
            mock_request.deadline = datetime.now() + timedelta(hours=24)
            
            with patch.object(trading_engine, '_determine_trading_mode', return_value="auction"), \
                 patch.object(trading_engine, '_initiate_bidding'):
                trade_id = await trading_engine.initiate_trade(mock_request, [])
                trades.append(trade_id)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Performance assertions
        assert len(trades) == 50
        assert execution_time < 5.0  # Should complete in under 5 seconds
        assert len(trading_engine.trades) == 50
    
    @pytest.mark.asyncio
    async def test_certification_system_performance(self):
        """Test certification system performance."""
        certification_system = CertificationSystem()
        
        with patch.object(certification_system, '_load_certifications'):
            await certification_system.initialize()
        
        # Request multiple certifications
        start_time = time.time()
        
        certifications = []
        for i in range(30):
            with patch.object(certification_system, '_start_certification_process'):
                cert_id = await certification_system.request_certification(
                    f"perf_cap_{i}", 
                    f"perf_agent_{i}", 
                    list(certification_system.criteria.keys())[0]
                )
                certifications.append(cert_id)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Performance assertions
        assert len(certifications) == 30
        assert execution_time < 3.0  # Should complete in under 3 seconds
        assert len(certification_system.certifications) == 30
    
    @pytest.mark.asyncio
    async def test_workflow_composer_performance(self):
        """Test workflow composer performance."""
        composer = AIWorkflowComposer()
        
        with patch.object(composer.template_manager, 'initialize'), \
             patch.object(composer.router, 'initialize'), \
             patch.object(composer, '_load_workflow_patterns'):
            await composer.initialize()
        
        # Compose multiple workflows
        start_time = time.time()
        
        workflows = []
        for i in range(20):
            requirements_text = f"Perform task {i} with data analysis and reporting"
            
            # Mock the composition process for performance testing
            with patch.object(composer.requirement_analyzer, 'analyze', 
                             return_value=[Mock(requirement_type=Mock(value="automation"))]), \
                 patch.object(composer.pattern_recognizer, 'find_patterns', return_value=[]), \
                 patch.object(composer, '_generate_workflow_structure', 
                             return_value={"nodes": [], "edges": [], "entry_point": "start"}), \
                 patch.object(composer, '_select_optimal_agents', return_value={}), \
                 patch.object(composer.workflow_optimizer, 'optimize', 
                             return_value={"nodes": [], "edges": [], "entry_point": "start", 
                                         "estimated_time": 600.0, "estimated_cost": 10.0, "confidence": 0.8}):
                
                workflow = await composer.compose_workflow(requirements_text)
                workflows.append(workflow)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Performance assertions
        assert len(workflows) == 20
        assert execution_time < 5.0  # Should complete in under 5 seconds
        assert len(composer.composed_workflows) == 20
    
    @pytest.mark.asyncio
    async def test_pattern_recognition_performance(self):
        """Test pattern recognition performance."""
        pattern_recognition = WorkflowPatternRecognition()
        
        with patch.object(pattern_recognition, '_load_patterns'):
            await pattern_recognition.initialize()
        
        # Add test patterns
        for i in range(10):
            pattern = Mock()
            pattern.pattern_id = f"perf_pattern_{i}"
            pattern.reliability_score = 0.8
            pattern_recognition.patterns[f"perf_pattern_{i}"] = pattern
        
        # Test pattern recognition performance
        workflow_structure = {
            "nodes": [
                {"id": "node_1", "type": "data_analysis"},
                {"id": "node_2", "type": "reporting"}
            ],
            "edges": [{"from": "node_1", "to": "node_2"}],
            "entry_point": "node_1"
        }
        
        start_time = time.time()
        
        # Recognize patterns multiple times
        for _ in range(50):
            with patch.object(pattern_recognition, '_calculate_similarity', return_value=0.5), \
                 patch.object(pattern_recognition, '_create_pattern_match', 
                             return_value=Mock(confidence_score=0.7)):
                matches = await pattern_recognition.recognize_patterns(workflow_structure)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Performance assertions
        assert execution_time < 2.0  # Should complete in under 2 seconds
    
    @pytest.mark.asyncio
    async def test_memory_usage(self):
        """Test memory usage of Phase 4 components."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create and initialize all components
        marketplace = CapabilityMarketplace()
        trading_engine = TradingEngine()
        certification_system = CertificationSystem()
        composer = AIWorkflowComposer()
        pattern_recognition = WorkflowPatternRecognition()
        
        with patch.object(marketplace.registry, 'initialize'), \
             patch.object(marketplace.trading_engine, 'initialize'), \
             patch.object(marketplace.certification_system, 'initialize'), \
             patch.object(trading_engine, '_load_trading_data'), \
             patch.object(certification_system, '_load_certifications'), \
             patch.object(composer.template_manager, 'initialize'), \
             patch.object(composer.router, 'initialize'), \
             patch.object(composer, '_load_workflow_patterns'), \
             patch.object(pattern_recognition, '_load_patterns'):
            
            await marketplace.initialize()
            await trading_engine.initialize()
            await certification_system.initialize()
            await composer.initialize()
            await pattern_recognition.initialize()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory usage should be reasonable (less than 100MB increase)
        assert memory_increase < 100, f"Memory increase too high: {memory_increase}MB"
