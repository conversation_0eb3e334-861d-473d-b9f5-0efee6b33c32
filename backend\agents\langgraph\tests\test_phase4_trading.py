"""
Test Suite for Phase 4: Trading Engine and Certification System

Comprehensive tests for the trading engine, certification system,
and related trading functionality.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch

from ..marketplace.trading_engine import (
    TradingEngine, Trade, Bid, TradeStatus, BidStatus
)
from ..marketplace.certification_system import (
    CertificationSystem, CertificationRecord, CertificationLevel, 
    CertificationStatus, CertificationCriteria
)
from ..events.event_bus import LangGraphEvent


class TestTradingEngine:
    """Test suite for TradingEngine."""
    
    @pytest.fixture
    async def trading_engine(self):
        """Create a trading engine instance for testing."""
        engine = TradingEngine()
        with patch.object(engine, '_load_trading_data'):
            await engine.initialize()
        return engine
    
    @pytest.mark.asyncio
    async def test_trading_engine_initialization(self):
        """Test trading engine initialization."""
        engine = TradingEngine()
        
        with patch.object(engine, '_load_trading_data') as mock_load:
            await engine.initialize()
            mock_load.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_initiate_trade(self, trading_engine):
        """Test trade initiation functionality."""
        # Mock request object
        mock_request = Mock()
        mock_request.request_id = "req_001"
        mock_request.requester_id = "user_001"
        mock_request.requirements = {"type": "data_analysis"}
        mock_request.budget = 100.0
        mock_request.deadline = datetime.now() + timedelta(hours=24)
        
        # Mock matching capabilities
        mock_capabilities = [Mock(), Mock()]
        
        with patch.object(trading_engine, '_determine_trading_mode', return_value="auction"), \
             patch.object(trading_engine, '_initiate_bidding') as mock_bidding:
            
            trade_id = await trading_engine.initiate_trade(mock_request, mock_capabilities)
            
            assert trade_id.startswith("trade_")
            assert trade_id in trading_engine.trades
            
            trade = trading_engine.trades[trade_id]
            assert trade.request_id == "req_001"
            assert trade.requester_id == "user_001"
            assert trade.budget == 100.0
            assert trade.status == TradeStatus.PENDING
            
            mock_bidding.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_submit_bid(self, trading_engine):
        """Test bid submission functionality."""
        # Create a test trade
        trade = Trade(
            trade_id="trade_001",
            request_id="req_001",
            requester_id="user_001",
            capability_requirements={"type": "data_analysis"},
            budget=100.0,
            deadline=datetime.now() + timedelta(hours=24),
            status=TradeStatus.ACTIVE
        )
        trading_engine.trades["trade_001"] = trade
        
        bid_data = {
            "trade_id": "trade_001",
            "bidder_id": "agent_001",
            "capability_id": "cap_001",
            "price": 50.0,
            "execution_time_estimate": 1800.0,
            "quality_guarantee": 0.9
        }
        
        bid_id = await trading_engine.submit_bid(bid_data)
        
        assert bid_id.startswith("bid_")
        assert bid_id in trading_engine.bids
        assert len(trade.bids) == 1
        
        bid = trading_engine.bids[bid_id]
        assert bid.trade_id == "trade_001"
        assert bid.bidder_id == "agent_001"
        assert bid.price == 50.0
        assert bid.status == BidStatus.SUBMITTED
    
    @pytest.mark.asyncio
    async def test_select_optimal_agent(self, trading_engine):
        """Test optimal agent selection functionality."""
        # Create test trade with bids
        trade = Trade(
            trade_id="trade_001",
            request_id="req_001",
            requester_id="user_001",
            capability_requirements={"type": "data_analysis"},
            budget=100.0,
            deadline=datetime.now() + timedelta(hours=24),
            status=TradeStatus.ACTIVE
        )
        
        # Create test bids
        bid1 = Bid(
            bid_id="bid_001",
            trade_id="trade_001",
            bidder_id="agent_001",
            capability_id="cap_001",
            price=60.0,
            quality_guarantee=0.8,
            status=BidStatus.SUBMITTED
        )
        
        bid2 = Bid(
            bid_id="bid_002",
            trade_id="trade_001",
            bidder_id="agent_002",
            capability_id="cap_002",
            price=50.0,
            quality_guarantee=0.9,
            status=BidStatus.SUBMITTED
        )
        
        trade.bids = [bid1, bid2]
        trading_engine.trades["trade_001"] = trade
        trading_engine.bids["bid_001"] = bid1
        trading_engine.bids["bid_002"] = bid2
        
        # Mock bid scoring
        with patch.object(trading_engine, '_calculate_bid_score', 
                         side_effect=[0.75, 0.85]):  # bid2 scores higher
            
            selected_agent = await trading_engine.select_optimal_agent("trade_001")
            
            assert selected_agent == "agent_002"
            assert trade.selected_agent == "agent_002"
            assert trade.final_price == 50.0
            assert trade.status == TradeStatus.COMPLETED
            assert bid2.status == BidStatus.ACCEPTED
            assert bid1.status == BidStatus.REJECTED
    
    @pytest.mark.asyncio
    async def test_calculate_bid_score(self, trading_engine):
        """Test bid scoring algorithm."""
        trade = Trade(
            trade_id="trade_001",
            request_id="req_001",
            requester_id="user_001",
            capability_requirements={"type": "data_analysis"},
            budget=100.0,
            deadline=datetime.now() + timedelta(hours=24)
        )
        
        bid = Bid(
            bid_id="bid_001",
            trade_id="trade_001",
            bidder_id="agent_001",
            capability_id="cap_001",
            price=50.0,
            execution_time_estimate=1800.0,
            quality_guarantee=0.9
        )
        
        with patch.object(trading_engine, '_get_agent_performance', 
                         return_value={"success_rate": 0.85}):
            
            score = await trading_engine._calculate_bid_score(bid, trade)
            
            assert 0.0 <= score <= 1.0
            # Score should be relatively high due to good price, quality, and performance
            assert score > 0.5
    
    @pytest.mark.asyncio
    async def test_market_analytics(self, trading_engine):
        """Test market analytics functionality."""
        # Add test data
        trade = Trade(
            trade_id="trade_001",
            request_id="req_001",
            requester_id="user_001",
            capability_requirements={"type": "data_analysis"},
            budget=100.0,
            deadline=datetime.now() + timedelta(hours=24),
            status=TradeStatus.COMPLETED,
            final_price=50.0
        )
        trading_engine.trades["trade_001"] = trade
        
        bid = Bid(
            bid_id="bid_001",
            trade_id="trade_001",
            bidder_id="agent_001",
            capability_id="cap_001",
            price=50.0,
            status=BidStatus.ACCEPTED
        )
        trading_engine.bids["bid_001"] = bid
        
        # Mock analytics methods
        with patch.object(trading_engine, '_get_price_trends', return_value={}), \
             patch.object(trading_engine, '_get_price_distribution', return_value={}), \
             patch.object(trading_engine, '_get_agent_performance_summary', return_value={}):
            
            analytics = await trading_engine.get_market_analytics()
            
            assert "trading_overview" in analytics
            assert "pricing_analysis" in analytics
            assert "performance_metrics" in analytics
            assert "market_efficiency" in analytics
            
            assert analytics["trading_overview"]["total_trades"] == 1
            assert analytics["trading_overview"]["completed_trades"] == 1
            assert analytics["trading_overview"]["total_bids"] == 1
    
    @pytest.mark.asyncio
    async def test_agent_performance_update(self, trading_engine):
        """Test agent performance tracking."""
        agent_id = "agent_001"
        
        # Initial performance update
        await trading_engine._update_agent_performance(agent_id, True, 1500.0, 0.9)
        
        perf = trading_engine.agent_performance[agent_id]
        assert perf["success_rate"] > 0.8  # Should be close to 1.0 for successful execution
        assert perf["average_execution_time"] < 1800.0  # Should be updated
        assert perf["quality_score"] > 0.8
        assert perf["total_executions"] == 1
        
        # Second performance update
        await trading_engine._update_agent_performance(agent_id, False, 2000.0, 0.7)
        
        perf = trading_engine.agent_performance[agent_id]
        assert perf["success_rate"] < 1.0  # Should decrease due to failure
        assert perf["total_executions"] == 2


class TestCertificationSystem:
    """Test suite for CertificationSystem."""
    
    @pytest.fixture
    async def certification_system(self):
        """Create a certification system instance for testing."""
        system = CertificationSystem()
        with patch.object(system, '_load_certifications'):
            await system.initialize()
        return system
    
    @pytest.mark.asyncio
    async def test_certification_system_initialization(self):
        """Test certification system initialization."""
        system = CertificationSystem()
        
        with patch.object(system, '_load_certifications') as mock_load:
            await system.initialize()
            mock_load.assert_called_once()
        
        # Check that criteria are initialized
        assert CertificationLevel.BASIC in system.criteria
        assert CertificationLevel.EXPERT in system.criteria
    
    @pytest.mark.asyncio
    async def test_request_certification(self, certification_system):
        """Test certification request functionality."""
        capability_id = "cap_001"
        agent_id = "agent_001"
        target_level = CertificationLevel.INTERMEDIATE
        
        with patch.object(certification_system, '_start_certification_process') as mock_start:
            cert_id = await certification_system.request_certification(
                capability_id, agent_id, target_level
            )
            
            assert cert_id.startswith("cert_")
            assert cert_id in certification_system.certifications
            
            cert = certification_system.certifications[cert_id]
            assert cert.capability_id == capability_id
            assert cert.agent_id == agent_id
            assert cert.level == target_level
            assert cert.status == CertificationStatus.PENDING
            
            mock_start.assert_called_once_with(cert)
    
    @pytest.mark.asyncio
    async def test_certification_process(self, certification_system):
        """Test the certification process."""
        cert = CertificationRecord(
            certification_id="cert_001",
            capability_id="cap_001",
            agent_id="agent_001",
            level=CertificationLevel.INTERMEDIATE
        )
        
        # Mock the certification process methods
        with patch.object(certification_system, '_collect_performance_data', 
                         return_value={"success_rate": 0.9, "performance_score": 0.85}), \
             patch.object(certification_system, '_run_certification_tests', 
                         return_value={"success_rate_test": True, "performance_test": True}), \
             patch.object(certification_system, '_perform_quality_assessment', 
                         return_value={"code_quality": 0.9}), \
             patch.object(certification_system, '_check_compliance', 
                         return_value={"security_compliance": True}):
            
            await certification_system._start_certification_process(cert)
            
            assert cert.status == CertificationStatus.COMPLETED
            assert cert.started_at is not None
            assert cert.completed_at is not None
    
    @pytest.mark.asyncio
    async def test_certification_failure(self, certification_system):
        """Test certification failure scenarios."""
        cert = CertificationRecord(
            certification_id="cert_002",
            capability_id="cap_002",
            agent_id="agent_002",
            level=CertificationLevel.EXPERT
        )
        
        # Mock failing certification tests
        with patch.object(certification_system, '_collect_performance_data', 
                         return_value={"success_rate": 0.5, "performance_score": 0.6}), \
             patch.object(certification_system, '_run_certification_tests', 
                         return_value={"success_rate_test": False, "performance_test": False}), \
             patch.object(certification_system, '_perform_quality_assessment', 
                         return_value={"code_quality": 0.7}), \
             patch.object(certification_system, '_check_compliance', 
                         return_value={"security_compliance": True}):
            
            await certification_system._start_certification_process(cert)
            
            assert cert.status == CertificationStatus.FAILED
            assert "Failed to meet certification requirements" in cert.notes
    
    @pytest.mark.asyncio
    async def test_get_certification_level(self, certification_system):
        """Test getting certification level for a capability."""
        capability_id = "cap_001"
        
        # No certifications - should return basic
        level = await certification_system.get_certification_level(capability_id)
        assert level == "basic"
        
        # Add valid certification
        cert = CertificationRecord(
            certification_id="cert_001",
            capability_id=capability_id,
            agent_id="agent_001",
            level=CertificationLevel.ADVANCED,
            status=CertificationStatus.COMPLETED,
            expires_at=datetime.now() + timedelta(days=30)
        )
        certification_system.certifications["cert_001"] = cert
        
        level = await certification_system.get_certification_level(capability_id)
        assert level == "advanced"
        
        # Add expired certification
        cert.expires_at = datetime.now() - timedelta(days=1)
        level = await certification_system.get_certification_level(capability_id)
        assert level == "basic"
    
    @pytest.mark.asyncio
    async def test_validate_certification(self, certification_system):
        """Test certification validation."""
        # Valid certification
        cert = CertificationRecord(
            certification_id="cert_001",
            capability_id="cap_001",
            agent_id="agent_001",
            level=CertificationLevel.INTERMEDIATE,
            status=CertificationStatus.COMPLETED,
            expires_at=datetime.now() + timedelta(days=30)
        )
        certification_system.certifications["cert_001"] = cert
        
        is_valid = await certification_system.validate_certification("cert_001")
        assert is_valid == True
        
        # Expired certification
        cert.expires_at = datetime.now() - timedelta(days=1)
        is_valid = await certification_system.validate_certification("cert_001")
        assert is_valid == False
        assert cert.status == CertificationStatus.EXPIRED
        
        # Non-existent certification
        is_valid = await certification_system.validate_certification("cert_999")
        assert is_valid == False
    
    @pytest.mark.asyncio
    async def test_certification_analytics(self, certification_system):
        """Test certification analytics functionality."""
        # Add test certifications
        cert1 = CertificationRecord(
            certification_id="cert_001",
            capability_id="cap_001",
            agent_id="agent_001",
            level=CertificationLevel.BASIC,
            status=CertificationStatus.COMPLETED
        )
        
        cert2 = CertificationRecord(
            certification_id="cert_002",
            capability_id="cap_002",
            agent_id="agent_002",
            level=CertificationLevel.ADVANCED,
            status=CertificationStatus.FAILED
        )
        
        certification_system.certifications["cert_001"] = cert1
        certification_system.certifications["cert_002"] = cert2
        
        # Mock analytics methods
        with patch.object(certification_system, '_get_performance_trends', return_value={}), \
             patch.object(certification_system, '_get_quality_metrics', return_value={}), \
             patch.object(certification_system, '_get_compliance_status', return_value={}):
            
            analytics = await certification_system.get_certification_analytics()
            
            assert "overview" in analytics
            assert "level_distribution" in analytics
            assert "success_rates" in analytics
            
            assert analytics["overview"]["total_certifications"] == 2
            assert analytics["overview"]["active_certifications"] == 1
            assert analytics["level_distribution"]["basic"] == 1
            assert analytics["level_distribution"]["advanced"] == 0  # Failed certification
    
    def test_certification_criteria(self, certification_system):
        """Test certification criteria validation."""
        # Test basic criteria
        basic_criteria = certification_system.criteria[CertificationLevel.BASIC]
        assert basic_criteria.min_success_rate == 0.8
        assert basic_criteria.min_performance_score == 0.6
        
        # Test expert criteria
        expert_criteria = certification_system.criteria[CertificationLevel.EXPERT]
        assert expert_criteria.min_success_rate == 0.95
        assert expert_criteria.min_performance_score == 0.9
        assert "error_handling" in expert_criteria.required_features
        assert "optimization" in expert_criteria.required_features
