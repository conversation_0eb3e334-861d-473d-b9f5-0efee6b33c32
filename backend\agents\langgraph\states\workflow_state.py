"""
Workflow-specific state definitions for LangGraph workflows.

This module provides state definitions for different types of workflows
including sequential, parallel, and conditional execution patterns.
"""

from typing import TypedDict, Annotated, List, Dict, Any, Optional, Union
from datetime import datetime
import operator
from enum import Enum

from .agent_state import DatageniusAgentState


class WorkflowType(str, Enum):
    """Types of workflows supported by the system."""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"
    COLLABORATIVE = "collaborative"
    PIPELINE = "pipeline"


class WorkflowPhase(str, Enum):
    """Phases of workflow execution."""
    INITIALIZATION = "initialization"
    PLANNING = "planning"
    EXECUTION = "execution"
    SYNTHESIS = "synthesis"
    VALIDATION = "validation"
    COMPLETION = "completion"


class WorkflowState(DatageniusAgentState):
    """
    Extended state for workflow-specific operations.
    
    This extends the base DatageniusAgentState with workflow-specific
    fields for managing complex multi-step processes.
    """
    
    # Workflow configuration
    workflow_type: WorkflowType
    workflow_phase: WorkflowPhase
    workflow_config: Dict[str, Any]
    
    # Step management
    current_step: int
    total_steps: int
    step_history: Annotated[List[Dict[str, Any]], operator.add]
    step_results: Dict[str, Any]
    
    # Parallel execution tracking
    parallel_branches: Dict[str, Dict[str, Any]]
    completed_branches: List[str]
    failed_branches: List[str]
    
    # Conditional logic
    conditions_met: Dict[str, bool]
    decision_points: List[Dict[str, Any]]
    
    # Resource management
    allocated_resources: Dict[str, Any]
    resource_usage: Dict[str, float]
    
    # Progress tracking
    progress_percentage: float
    estimated_completion_time: Optional[datetime]
    
    # Checkpointing
    last_checkpoint: Optional[datetime]
    checkpoint_data: Dict[str, Any]


def create_workflow_state(
    base_state: DatageniusAgentState,
    workflow_type: WorkflowType,
    workflow_config: Dict[str, Any]
) -> WorkflowState:
    """
    Create a workflow state from a base agent state.
    
    Args:
        base_state: Base agent state
        workflow_type: Type of workflow
        workflow_config: Workflow configuration
        
    Returns:
        Initialized WorkflowState
    """
    workflow_state = WorkflowState(**base_state)
    
    # Add workflow-specific fields
    workflow_state.update({
        "workflow_type": workflow_type,
        "workflow_phase": WorkflowPhase.INITIALIZATION,
        "workflow_config": workflow_config,
        "current_step": 0,
        "total_steps": workflow_config.get("total_steps", 1),
        "step_history": [],
        "step_results": {},
        "parallel_branches": {},
        "completed_branches": [],
        "failed_branches": [],
        "conditions_met": {},
        "decision_points": [],
        "allocated_resources": {},
        "resource_usage": {},
        "progress_percentage": 0.0,
        "estimated_completion_time": None,
        "last_checkpoint": None,
        "checkpoint_data": {}
    })
    
    return workflow_state


def advance_workflow_step(state: WorkflowState, step_result: Dict[str, Any]) -> WorkflowState:
    """
    Advance the workflow to the next step.
    
    Args:
        state: Current workflow state
        step_result: Result of the current step
        
    Returns:
        Updated workflow state
    """
    # Record step completion
    step_record = {
        "step_number": state["current_step"],
        "timestamp": datetime.now().isoformat(),
        "result": step_result,
        "agent": state["current_agent"]
    }
    state["step_history"].append(step_record)
    state["step_results"][str(state["current_step"])] = step_result
    
    # Advance to next step
    state["current_step"] += 1
    
    # Update progress
    if state["total_steps"] > 0:
        state["progress_percentage"] = (state["current_step"] / state["total_steps"]) * 100
    
    # Update phase if needed
    if state["current_step"] >= state["total_steps"]:
        state["workflow_phase"] = WorkflowPhase.COMPLETION
    
    return state


def add_parallel_branch(state: WorkflowState, branch_id: str, branch_config: Dict[str, Any]) -> WorkflowState:
    """
    Add a parallel execution branch to the workflow.
    
    Args:
        state: Current workflow state
        branch_id: Identifier for the branch
        branch_config: Configuration for the branch
        
    Returns:
        Updated workflow state
    """
    state["parallel_branches"][branch_id] = {
        "config": branch_config,
        "status": "pending",
        "start_time": None,
        "end_time": None,
        "result": None,
        "error": None
    }
    
    return state


def complete_parallel_branch(state: WorkflowState, branch_id: str, result: Dict[str, Any]) -> WorkflowState:
    """
    Mark a parallel branch as completed.
    
    Args:
        state: Current workflow state
        branch_id: Identifier for the branch
        result: Branch execution result
        
    Returns:
        Updated workflow state
    """
    if branch_id in state["parallel_branches"]:
        state["parallel_branches"][branch_id].update({
            "status": "completed",
            "end_time": datetime.now().isoformat(),
            "result": result
        })
        
        if branch_id not in state["completed_branches"]:
            state["completed_branches"].append(branch_id)
    
    return state


def fail_parallel_branch(state: WorkflowState, branch_id: str, error: str) -> WorkflowState:
    """
    Mark a parallel branch as failed.
    
    Args:
        state: Current workflow state
        branch_id: Identifier for the branch
        error: Error message
        
    Returns:
        Updated workflow state
    """
    if branch_id in state["parallel_branches"]:
        state["parallel_branches"][branch_id].update({
            "status": "failed",
            "end_time": datetime.now().isoformat(),
            "error": error
        })
        
        if branch_id not in state["failed_branches"]:
            state["failed_branches"].append(branch_id)
    
    return state


def check_workflow_completion(state: WorkflowState) -> bool:
    """
    Check if the workflow is complete.
    
    Args:
        state: Current workflow state
        
    Returns:
        True if workflow is complete, False otherwise
    """
    if state["workflow_type"] == WorkflowType.SEQUENTIAL:
        return state["current_step"] >= state["total_steps"]
    
    elif state["workflow_type"] == WorkflowType.PARALLEL:
        total_branches = len(state["parallel_branches"])
        completed_branches = len(state["completed_branches"]) + len(state["failed_branches"])
        return completed_branches >= total_branches
    
    elif state["workflow_type"] == WorkflowType.CONDITIONAL:
        # Check if all required conditions are met
        required_conditions = state["workflow_config"].get("required_conditions", [])
        return all(state["conditions_met"].get(condition, False) for condition in required_conditions)
    
    return False
