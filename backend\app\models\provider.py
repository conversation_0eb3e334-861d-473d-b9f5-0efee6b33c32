"""
Provider models for the Datagenius backend.

This module provides Pydantic models for AI provider-related functionality.
"""

from typing import List, Optional, Dict
from pydantic import BaseModel, Field, field_validator


class ProviderBase(BaseModel):
    """Base model for provider data."""
    id: str
    name: str
    description: str
    endpoint: Optional[str] = None
    is_available: bool = True


class ProviderApiKey(BaseModel):
    """Model for provider API key."""
    provider_id: str
    api_key: str
    user_id: int


class ProviderApiKeyCreate(BaseModel):
    """Model for creating a provider API key."""
    provider_id: str
    api_key: str


class ProviderApiKeyUpdate(BaseModel):
    """Model for updating a provider API key."""
    api_key: Optional[str] = None


class ProviderApiKeyResponse(BaseModel):
    """Model for provider API key response."""
    provider_id: str
    is_valid: bool = False
    message: Optional[str] = None


class ProviderListResponse(BaseModel):
    """Model for provider list response."""
    providers: List[ProviderBase]


class ProviderResponse(BaseModel):
    """Model for provider response."""
    provider: ProviderBase


class ProviderModel(BaseModel):
    """Model for AI provider model."""
    id: str
    name: str
    description: Optional[str] = None
    context_length: Optional[int] = None
    created: Optional[int] = None
    provider: Optional[str] = None
    pricing: Optional[Dict] = None
    category: Optional[str] = None


class ProviderModelsResponse(BaseModel):
    """Model for provider models response."""
    models: List[ProviderModel]
    message: Optional[str] = None


class ProviderSettings(BaseModel):
    """Model for provider settings."""
    default_provider: str = Field("", description="The default provider to use")
    use_local_llm: bool = Field(False, description="Whether to use local LLM when available")
    memory_service_provider: str = Field("", description="LLM provider for memory service")
    memory_service_model: str = Field("", description="LLM model for memory service")
    concierge_agent_provider: str = Field("", description="LLM provider for concierge agent")
    concierge_agent_model: str = Field("", description="LLM model for concierge agent")

    class Config:
        extra = "ignore"  # Ignore extra fields
        validate_assignment = True  # Validate values on assignment

    @field_validator('default_provider', mode='before')
    @classmethod
    def validate_default_provider(cls, v):
        # Convert None to empty string
        if v is None:
            return ""
        return v
