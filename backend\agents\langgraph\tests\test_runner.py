"""
Simple test runner for the LangGraph event system.

This script provides a basic test runner to verify the event system
functionality without complex pytest setup.
"""

import asyncio
import sys
import time
from datetime import datetime
from pathlib import Path

# Add the backend directory to the path
backend_dir = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(backend_dir))

from agents.langgraph.events.event_bus import LangGraphEventBus, LangGraphEvent, EventPriority
from agents.langgraph.events.types import AgentRegistrationEvent, WorkflowCompletedEvent


async def test_basic_event_functionality():
    """Test basic event bus functionality."""
    print("Testing basic event functionality...")
    
    event_bus = LangGraphEventBus(max_history=100)
    received_events = []
    
    async def event_handler(event):
        received_events.append(event)
        print(f"Received event: {event.event_type} from {event.source}")
    
    # Subscribe to events
    event_bus.subscribe("test.event", event_handler)
    
    # Start event bus
    await event_bus.start()
    
    # Publish test event
    test_event = LangGraphEvent(
        event_type="test.event",
        timestamp=datetime.now(),
        source="test_runner",
        data={"message": "Hello, Event System!"}
    )
    
    await event_bus.publish(test_event)
    
    # Wait for processing
    await asyncio.sleep(0.2)
    
    # Stop event bus
    await event_bus.stop()
    
    # Verify results
    assert len(received_events) == 1, f"Expected 1 event, got {len(received_events)}"
    assert received_events[0].event_type == "test.event"
    assert received_events[0].data["message"] == "Hello, Event System!"
    
    print("✓ Basic event functionality test passed!")
    return True


async def test_event_types():
    """Test event type creation."""
    print("Testing event types...")
    
    # Test AgentRegistrationEvent
    agent_event = AgentRegistrationEvent(
        agent_id="test-agent",
        capabilities=["analysis", "reporting"],
        metadata={"version": "1.0"}
    )
    
    assert agent_event.event_type == "agent.registered"
    assert agent_event.data["agent_id"] == "test-agent"
    assert agent_event.data["capabilities"] == ["analysis", "reporting"]
    assert agent_event.priority == EventPriority.HIGH
    
    # Test WorkflowCompletedEvent
    workflow_event = WorkflowCompletedEvent(
        workflow_id="test-workflow",
        execution_time=15.5,
        success=True,
        results={"output": "test result"}
    )
    
    assert workflow_event.event_type == "workflow.completed"
    assert workflow_event.data["workflow_id"] == "test-workflow"
    assert workflow_event.data["execution_time"] == 15.5
    assert workflow_event.data["success"] is True
    
    print("✓ Event types test passed!")
    return True


async def test_performance():
    """Test event system performance."""
    print("Testing performance...")
    
    event_bus = LangGraphEventBus(max_history=1000)
    processed_count = 0
    
    async def performance_handler(event):
        nonlocal processed_count
        processed_count += 1
    
    event_bus.subscribe("performance.test", performance_handler)
    await event_bus.start()
    
    # Test parameters
    num_events = 500  # Reduced for simpler testing
    start_time = time.perf_counter()
    
    # Publish events
    for i in range(num_events):
        event = LangGraphEvent(
            event_type="performance.test",
            timestamp=datetime.now(),
            source="performance_test",
            data={"index": i}
        )
        await event_bus.publish(event)
    
    # Wait for processing
    await asyncio.sleep(1.0)
    
    end_time = time.perf_counter()
    total_time = end_time - start_time
    
    await event_bus.stop()
    
    # Calculate metrics
    throughput = processed_count / total_time if total_time > 0 else 0
    
    print(f"Processed {processed_count}/{num_events} events in {total_time:.3f}s")
    print(f"Throughput: {throughput:.2f} events/second")
    
    # Verify performance requirements (relaxed for testing)
    assert processed_count == num_events, f"Only {processed_count}/{num_events} events processed"
    assert throughput > 100, f"Throughput {throughput:.2f} events/sec is too low"
    
    print("✓ Performance test passed!")
    return True


async def test_priority_handling():
    """Test priority event handling."""
    print("Testing priority handling...")
    
    event_bus = LangGraphEventBus(max_history=100)
    received_priorities = []
    
    async def priority_handler(event):
        received_priorities.append(event.data["priority"])
    
    event_bus.subscribe("priority.test", priority_handler)
    await event_bus.start()
    
    # Publish events in reverse priority order
    priorities = [
        ("low", EventPriority.LOW),
        ("medium", EventPriority.MEDIUM),
        ("high", EventPriority.HIGH),
        ("critical", EventPriority.CRITICAL)
    ]
    
    for priority_name, priority_enum in priorities:
        event = LangGraphEvent(
            event_type="priority.test",
            timestamp=datetime.now(),
            source="priority_test",
            data={"priority": priority_name},
            priority=priority_enum
        )
        await event_bus.publish(event)
    
    # Wait for processing
    await asyncio.sleep(0.3)
    
    await event_bus.stop()
    
    # Verify priority order (critical should be first)
    assert len(received_priorities) == 4
    assert received_priorities[0] == "critical"
    assert received_priorities[1] == "high"
    
    print("✓ Priority handling test passed!")
    return True


async def test_error_handling():
    """Test error handling in event callbacks."""
    print("Testing error handling...")
    
    event_bus = LangGraphEventBus(max_history=100)
    successful_events = []
    
    async def failing_handler(event):
        if event.data.get("should_fail"):
            raise Exception("Intentional test error")
    
    async def successful_handler(event):
        successful_events.append(event)
    
    event_bus.subscribe("error.test", failing_handler)
    event_bus.subscribe("error.test", successful_handler)
    await event_bus.start()
    
    # Publish event that should cause error
    error_event = LangGraphEvent(
        event_type="error.test",
        timestamp=datetime.now(),
        source="error_test",
        data={"should_fail": True}
    )
    
    await event_bus.publish(error_event)
    await asyncio.sleep(0.2)
    
    await event_bus.stop()
    
    # Verify that successful handler still processed the event
    assert len(successful_events) == 1
    
    print("✓ Error handling test passed!")
    return True


async def run_all_tests():
    """Run all tests."""
    print("Starting LangGraph Event System Tests...")
    print("=" * 50)
    
    tests = [
        test_basic_event_functionality,
        test_event_types,
        test_performance,
        test_priority_handling,
        test_error_handling
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            result = await test()
            if result:
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ {test.__name__} failed: {e}")
            failed += 1
        
        print()  # Add spacing between tests
    
    print("=" * 50)
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Event system is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nTests interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Test runner failed: {e}")
        sys.exit(1)
