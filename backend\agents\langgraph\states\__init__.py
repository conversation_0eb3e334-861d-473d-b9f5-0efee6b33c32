"""
State definitions for LangGraph workflows in Datagenius.

This module provides the unified state management system for all workflows
and agent interactions in the Datagenius multi-agent system.

Legacy state classes (DatageniusAgentState, WorkflowState, CollaborationState)
have been consolidated into UnifiedDatageniusState for better consistency
and maintainability.
"""

from .unified_state import (
    UnifiedDatageniusState,
    WorkflowStatus,
    ConversationStage,
    AgentRole,
    ToolStatus,
    create_unified_state,
    update_workflow_status,
    add_cross_agent_insight,
    get_relevant_insights,
    build_agent_consensus,
    record_agent_vote,
    load_business_profile_context,
    update_agent_transition
)

from .state_migration_utils import (
    migrate_datagenius_agent_state,
    migrate_workflow_state,
    migrate_collaboration_state,
    auto_migrate_state,
    ensure_unified_state
)

# Backward compatibility aliases for gradual migration
DatageniusAgentState = UnifiedDatageniusState
WorkflowState = UnifiedDatageniusState
CollaborationState = UnifiedDatageniusState

__all__ = [
    # Primary unified state
    "UnifiedDatageniusState",
    "WorkflowStatus",
    "ConversationStage",
    "AgentRole",
    "ToolStatus",

    # State management functions
    "create_unified_state",
    "update_workflow_status",
    "add_cross_agent_insight",
    "get_relevant_insights",
    "build_agent_consensus",
    "record_agent_vote",
    "load_business_profile_context",
    "update_agent_transition",

    # Migration utilities
    "migrate_datagenius_agent_state",
    "migrate_workflow_state",
    "migrate_collaboration_state",
    "auto_migrate_state",
    "ensure_unified_state",

    # Backward compatibility aliases
    "DatageniusAgentState",
    "WorkflowState",
    "CollaborationState"
]
