# Development environment configuration
# This file contains development-specific settings that override defaults

name: "Datagenius Development"
version: "1.0.0-dev"
description: "AI-powered data analysis platform (Development)"

environment:
  environment: "development"
  debug: true
  testing: false

database:
  url: "*********************************************************/datagenius"
  echo: true
  pool_size: 5
  max_overflow: 10
  pool_pre_ping: true
  pool_recycle: 3600
  connect_timeout: 30
  statement_timeout: 30000
  auto_migrate: true
  backup_enabled: false

redis:
  url: "redis://localhost:6379/0"
  max_connections: 10
  socket_timeout: 5
  default_ttl: 1800  # 30 minutes for development

security:
  jwt_secret_key: "development-secret-key-change-in-production-must-be-32-chars"
  access_token_expire_minutes: 60  # Longer for development
  refresh_token_expire_days: 14
  max_refresh_count: 50
  enforce_ip_validation: false
  ip_change_lockout: 0  # 0 = disabled for development
  max_upload_size: 52428800  # 50MB for development
  rate_limiting_enabled: false  # Disabled for development
  cors_origins:
    - "http://localhost:3000"
    - "http://localhost:5173"
    - "http://localhost:8080"
  enforce_https: false
  require_email_verification: false
  two_factor_auth_enabled: false

llm:
  default_provider: "groq"
  fallback_providers:
    - "groq"
    - "openai"
  default_temperature: 0.7
  default_max_tokens: 2048
  enable_caching: true
  cache_ttl_seconds: 1800  # 30 minutes
  enable_streaming: true
  content_filter_enabled: false  # Relaxed for development
  log_requests: true
  log_responses: true  # Enable for debugging
  track_usage: true

email:
  enabled: false  # Disabled for development
  sender: "<EMAIL>"
  smtp_server: "localhost"
  smtp_port: 1025  # MailHog default port
  use_tls: false

files:
  upload_dir: "temp_uploads_dev"
  max_upload_size: 52428800  # 50MB
  chunking_performance_profile: "fast"
  chunking_use_adaptive: true
  chunking_enable_caching: true
  chunking_batch_size: 8  # Smaller for development
  chunking_parallel_workers: 2

vector:
  qdrant_host: "localhost"
  qdrant_port: 6333
  qdrant_https: false
  mem0_self_hosted: true
  mem0_default_ttl: 86400  # 1 day for development
  mem0_max_memories: 500
  mem0_memory_threshold: 0.6  # Lower threshold for development

logging:
  level: "DEBUG"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/datagenius_dev.log"
  max_file_size: 5242880  # 5MB
  backup_count: 3

monitoring:
  enabled: true
  performance_tracking: true
  error_tracking: true
  alert_on_errors: false  # No alerts in development
  alert_threshold: 50

# Phase 3: AI-Powered Systems Configuration
phase3:
  enabled: true
  optimization_level: "standard"
  debug_mode: true  # Enable debug mode for development

  # Predictive Optimization
  predictive_optimization:
    enabled: true
    confidence_threshold: 0.6  # Lower threshold for development
    suggestion_limit: 3
    pattern_learning: true
    ab_testing: false  # Disabled for development
    training_interval_hours: 1  # More frequent training in dev

  # Self-Healing System
  self_healing:
    enabled: true
    strategy: "conservative"  # Conservative approach for development
    failure_prediction: true
    automatic_remediation: false  # Manual approval in development
    retry_attempts: 2
    health_check_interval: 60  # Check every minute

  # Advanced Collaboration
  collaboration:
    enabled: true
    team_formation: true
    skill_matching: true
    collaborative_learning: true
    consensus_building: true
    max_team_size: 3  # Smaller teams for development
    timeout_seconds: 180

  # Performance Monitoring
  performance_monitoring:
    enabled: true
    collection_interval: 30  # More frequent collection in dev
    retention_days: 7  # Shorter retention for development
    alert_thresholds:
      workflow_failure_rate: 0.2  # Higher tolerance in dev
      execution_time_increase: 0.3
      memory_usage: 0.9
      prediction_accuracy: 0.6
      healing_success_rate: 0.7

  # Resource Management
  resources:
    max_concurrent_optimizations: 5  # Lower limit for development
    optimization_timeout: 60
    memory_limit_mb: 512
    cpu_limit_percent: 70.0
    event_queue_size: 1000

# Phase 4: Platform Evolution Configuration
phase4:
  enabled: true
  marketplace:
    enabled: true
    enable_debug_logging: true
  workflow_composer:
    enabled: true
    enable_debug_mode: true
  pattern_recognition:
    enabled: true
    enable_debug_logging: true
  trading_engine:
    enabled: true
  certification_system:
    enabled: true

# Phase 5: Migration Completion Configuration
phase5:
  enabled: true
  full_deployment:
    enabled: true
    remove_legacy_code: false  # Keep for development debugging
    remove_feature_flags: false  # Keep for development testing
    update_documentation: true
  performance_optimization:
    enabled: true
    cache_ttl_seconds: 1800  # 30 minutes for development
    cache_max_size_mb: 256  # Smaller cache for development
  monitoring:
    enabled: true
    production_monitoring: false  # Use development monitoring
    health_checks_enabled: true
    metrics_collection_enabled: true
    alert_system_enabled: false  # No alerts in development
  configuration:
    auto_update_configs: false  # Manual control in development
    validate_integration: true
    backup_old_configs: true

# Feature Flags for Development
feature_flags:
  # Core Features
  langgraph_enabled: true
  intelligent_routing: true
  multi_agent_collaboration: true
  state_persistence: true
  performance_monitoring: true
  legacy_fallback: false  # Disabled for Phase 5

  # Phase 4 Features
  phase4_platform_evolution: true
  capability_marketplace: true
  ai_workflow_composer: true
  pattern_recognition: true
  trading_engine: true
  certification_system: true

  # Phase 5 Features
  phase5_migration_completion: true
  full_deployment_enabled: true
  performance_optimization_enabled: true
  production_monitoring_enabled: false  # Development monitoring instead
  legacy_code_removed: false  # Keep for development

frontend_url: "http://localhost:5173"

google_redirect_uri: "http://localhost:5173/auth/google/callback"
