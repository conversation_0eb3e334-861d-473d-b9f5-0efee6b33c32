"""
Persona Repository Implementation.

Provides specialized repository operations for Persona entities,
replacing the persona-related CRUD functions from database.py.
"""

import uuid
import logging
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session

from .base_repository import BaseRepository, RepositoryError
from ..models.database_models import Persona
from ..models.schemas import PersonaCreate, PersonaUpdate, PersonaResponse
from ..errors.correlation_context import get_correlation_logger

logger = get_correlation_logger(__name__)


class PersonaRepository(BaseRepository[Persona]):
    """Repository for Persona entity operations."""
    
    def __init__(self, session: Session):
        super().__init__(session, Persona)
        self.logger = logger
    
    def create_persona(self, persona_data: Dict[str, Any]) -> Persona:
        """
        Create a new persona.
        
        Args:
            persona_data: Dictionary containing persona data
            
        Returns:
            Created persona instance
            
        Raises:
            RepositoryError: If persona creation fails
        """
        try:
            # Ensure ID is set
            if 'id' not in persona_data:
                persona_data['id'] = str(uuid.uuid4())
            
            persona = Persona(**persona_data)
            
            self.session.add(persona)
            self.session.commit()
            self.session.refresh(persona)
            
            self.logger.info(f"Created persona {persona.id}: {persona.name}")
            return persona
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to create persona: {e}")
            raise RepositoryError(
                f"Failed to create persona: {str(e)}",
                entity_type="Persona",
                operation="create"
            )
    
    def get_personas(
        self,
        skip: int = 0,
        limit: int = 100,
        industry: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_public: Optional[bool] = None
    ) -> List[Persona]:
        """
        Get personas with filtering and pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            industry: Optional industry filter
            is_active: Optional active status filter
            is_public: Optional public status filter
            
        Returns:
            List of personas matching the criteria
        """
        try:
            query = self.session.query(Persona)
            
            # Apply filters
            if industry:
                query = query.filter(Persona.industry == industry)
            if is_active is not None:
                query = query.filter(Persona.is_active == is_active)
            if is_public is not None:
                query = query.filter(Persona.is_public == is_public)
            
            return query.order_by(Persona.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get personas: {e}")
            raise RepositoryError(
                f"Failed to get personas: {str(e)}",
                entity_type="Persona",
                operation="get_personas"
            )
    
    def update_persona(
        self,
        persona_id: str,
        persona_data: Dict[str, Any]
    ) -> Optional[Persona]:
        """
        Update a persona.
        
        Args:
            persona_id: ID of the persona to update
            persona_data: Dictionary containing update data
            
        Returns:
            Updated persona or None if not found
        """
        try:
            persona = self.get_by_id(persona_id)
            if not persona:
                self.logger.warning(f"Persona {persona_id} not found for update")
                return None
            
            # Update allowed fields
            allowed_fields = [
                'name', 'description', 'industry', 'is_active', 'is_public',
                'price', 'avatar_url', 'system_prompt', 'welcome_message',
                'capabilities', 'tags', 'metadata'
            ]
            
            for key, value in persona_data.items():
                if key in allowed_fields:
                    setattr(persona, key, value)
            
            self.session.commit()
            self.session.refresh(persona)
            
            self.logger.info(f"Updated persona {persona_id}")
            return persona
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to update persona: {e}")
            raise RepositoryError(
                f"Failed to update persona: {str(e)}",
                entity_type="Persona",
                operation="update"
            )
    
    def delete_persona(self, persona_id: str) -> bool:
        """
        Delete a persona.
        
        Args:
            persona_id: ID of the persona to delete
            
        Returns:
            True if persona was deleted, False if not found
            
        Raises:
            RepositoryError: If deletion fails
        """
        try:
            persona = self.get_by_id(persona_id)
            if not persona:
                self.logger.warning(f"Persona {persona_id} not found for deletion")
                return False
            
            self.session.delete(persona)
            self.session.commit()
            
            self.logger.info(f"Deleted persona {persona_id}")
            return True
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to delete persona: {e}")
            raise RepositoryError(
                f"Failed to delete persona: {str(e)}",
                entity_type="Persona",
                operation="delete"
            )
    
    def get_persona_count(self, is_active: Optional[bool] = None) -> int:
        """
        Get the count of personas.
        
        Args:
            is_active: Optional active status filter
            
        Returns:
            Number of personas matching the criteria
        """
        try:
            query = self.session.query(Persona)
            if is_active is not None:
                query = query.filter(Persona.is_active == is_active)
            return query.count()
            
        except Exception as e:
            self.logger.error(f"Failed to get persona count: {e}")
            raise RepositoryError(
                f"Failed to get persona count: {str(e)}",
                entity_type="Persona",
                operation="get_persona_count"
            )
    
    def get_personas_by_industry(
        self,
        industry: str,
        skip: int = 0,
        limit: int = 100,
        is_active: Optional[bool] = None
    ) -> List[Persona]:
        """
        Get personas by industry.
        
        Args:
            industry: Industry to filter by
            skip: Number of records to skip
            limit: Maximum number of records to return
            is_active: Optional active status filter
            
        Returns:
            List of personas in the specified industry
        """
        try:
            query = self.session.query(Persona).filter(Persona.industry == industry)
            
            if is_active is not None:
                query = query.filter(Persona.is_active == is_active)
            
            return query.order_by(Persona.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get personas by industry: {e}")
            raise RepositoryError(
                f"Failed to get personas by industry: {str(e)}",
                entity_type="Persona",
                operation="get_personas_by_industry"
            )
    
    def get_public_personas(
        self,
        skip: int = 0,
        limit: int = 100,
        industry: Optional[str] = None
    ) -> List[Persona]:
        """
        Get public personas.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            industry: Optional industry filter
            
        Returns:
            List of public personas
        """
        try:
            query = self.session.query(Persona).filter(
                Persona.is_public == True,
                Persona.is_active == True
            )
            
            if industry:
                query = query.filter(Persona.industry == industry)
            
            return query.order_by(Persona.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get public personas: {e}")
            raise RepositoryError(
                f"Failed to get public personas: {str(e)}",
                entity_type="Persona",
                operation="get_public_personas"
            )
    
    def search_personas(
        self,
        search_term: str,
        skip: int = 0,
        limit: int = 100,
        industry: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> List[Persona]:
        """
        Search personas by name or description.
        
        Args:
            search_term: Term to search for
            skip: Number of records to skip
            limit: Maximum number of records to return
            industry: Optional industry filter
            is_active: Optional active status filter
            
        Returns:
            List of matching personas
        """
        try:
            query = self.session.query(Persona).filter(
                (Persona.name.ilike(f"%{search_term}%")) |
                (Persona.description.ilike(f"%{search_term}%"))
            )
            
            if industry:
                query = query.filter(Persona.industry == industry)
            if is_active is not None:
                query = query.filter(Persona.is_active == is_active)
            
            return query.order_by(Persona.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to search personas: {e}")
            raise RepositoryError(
                f"Failed to search personas: {str(e)}",
                entity_type="Persona",
                operation="search_personas"
            )
    
    def activate_persona(self, persona_id: str) -> Optional[Persona]:
        """
        Activate a persona.
        
        Args:
            persona_id: ID of the persona to activate
            
        Returns:
            Activated persona or None if not found
        """
        try:
            persona = self.get_by_id(persona_id)
            if not persona:
                self.logger.warning(f"Persona {persona_id} not found for activation")
                return None
            
            persona.is_active = True
            
            self.session.commit()
            self.session.refresh(persona)
            
            self.logger.info(f"Activated persona {persona_id}")
            return persona
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to activate persona: {e}")
            raise RepositoryError(
                f"Failed to activate persona: {str(e)}",
                entity_type="Persona",
                operation="activate"
            )
    
    def deactivate_persona(self, persona_id: str) -> Optional[Persona]:
        """
        Deactivate a persona.
        
        Args:
            persona_id: ID of the persona to deactivate
            
        Returns:
            Deactivated persona or None if not found
        """
        try:
            persona = self.get_by_id(persona_id)
            if not persona:
                self.logger.warning(f"Persona {persona_id} not found for deactivation")
                return None
            
            persona.is_active = False
            
            self.session.commit()
            self.session.refresh(persona)
            
            self.logger.info(f"Deactivated persona {persona_id}")
            return persona
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to deactivate persona: {e}")
            raise RepositoryError(
                f"Failed to deactivate persona: {str(e)}",
                entity_type="Persona",
                operation="deactivate"
            )
