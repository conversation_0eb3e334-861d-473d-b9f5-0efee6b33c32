from fastapi import APIRouter, Depends, HTTPException, Body
from fastapi.responses import StreamingResponse
from typing import List, Literal, Optional
import io

from ..services.reports_service import ReportGenerator # Changed
from ..models.reports import ReportConfig, Report, ReportSchedule, ReportExport # Changed
from ..dependencies import get_db_service # Added for dependency injection
from ..repositories.database_service import DatabaseService
from ..api.admin import get_current_active_user # For actual user
from ..models.auth import User # For User type hint

router = APIRouter(prefix="/api/reports", tags=["reports"])

# Dependency function for reports service
def get_reports_service(db_service: DatabaseService = Depends(get_db_service)) -> ReportGenerator:
    with db_service.get_session() as db:
        return ReportGenerator(db=db)

@router.post("/generate", response_model=Report)
async def generate_report_route(
    report_config: ReportConfig,
    current_user: User = Depends(get_current_active_user), # Changed to actual user
    reports_service: ReportGenerator = Depends(get_reports_service) # Injected service
):
    """
    Generate a new report based on the provided configuration.
    The user_id from the authenticated user will override any in ReportConfig if necessary.
    """
    # Ensure user_id in config matches authenticated user or is set by them
    # Pydantic models for ReportConfig and ReportSchedule now use int for user_id
    if report_config.user_id != current_user.id:
        # Allow admin to generate for others if needed, or enforce strict match
        # For now, let's assume user_id in config must match authenticated user if provided,
        # otherwise, set it.
        if report_config.user_id is not None and report_config.user_id != 0: # Check if it was filled with a different ID
             raise HTTPException(status_code=403, detail="Cannot generate report for another user unless admin.")
        report_config.user_id = current_user.id # Set to current user
    
    return await reports_service.generate_report(report_config)

@router.post("/schedule", response_model=ReportSchedule) # Pydantic model for response
async def schedule_report_route(
    schedule_config: ReportSchedule, # Pydantic model for request
    current_user: User = Depends(get_current_active_user), # Changed
    reports_service: ReportGenerator = Depends(get_reports_service) # Injected service
):
    """
    Schedule a report to be generated based on a cron expression.
    """
    if schedule_config.user_id != current_user.id:
        # Security check: user can only schedule reports for themselves
        # (or admin can schedule for others - this logic can be added if needed)
        raise HTTPException(status_code=403, detail="Cannot schedule reports for another user.")
    
    # schedule_report service method now returns SQLAlchemy model.
    # We need to convert it to Pydantic ReportSchedule for the response.
    db_schedule = await reports_service.schedule_report(schedule_config)
    return ReportSchedule.model_validate(db_schedule)


@router.get("/scheduled", response_model=List[ReportSchedule]) # Pydantic model for response
async def get_scheduled_reports_route(
    current_user: User = Depends(get_current_active_user), # Changed
    reports_service: ReportGenerator = Depends(get_reports_service) # Injected service
):
    """
    Get all reports scheduled by the current user.
    """
    db_schedules = await reports_service.get_scheduled_reports(current_user.id)
    return [ReportSchedule.model_validate(s) for s in db_schedules]

@router.get("/{report_id}", response_model=Report) # Pydantic model for response
async def get_generated_report_route(
    report_id: str,
    current_user: User = Depends(get_current_active_user), # Changed
    reports_service: ReportGenerator = Depends(get_reports_service) # Injected service
):
    """
    Retrieve a specific generated report by its ID.
    Ensures the user has access to this report (dummy check for now).
    """
    db_report = await reports_service.get_generated_report(report_id, current_user.id)
    if not db_report:
        raise HTTPException(status_code=404, detail="Report not found or access denied")
    # Convert SQLAlchemy model to Pydantic model for response
    return Report.model_validate(db_report)

@router.get("/{report_id}/export", response_class=StreamingResponse)
async def export_report_route(
    report_id: str,
    format: Literal["csv", "excel", "pdf"] = "csv",
    current_user: User = Depends(get_current_active_user), # Changed
    reports_service: ReportGenerator = Depends(get_reports_service) # Injected service
):
    """
    Export a generated report in the specified format (csv, excel, pdf).
    """
    # First, verify user has access to the report
    report_meta_db = await reports_service.get_generated_report(report_id, current_user.id)
    if not report_meta_db: # Checks if report exists and belongs to user
        raise HTTPException(status_code=404, detail="Report not found or access denied.")

    exported_report: ReportExport = await reports_service.export_report(report_id, format, current_user.id)
    
    media_type_map = {
        "csv": "text/csv",
        "excel": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "pdf": "application/pdf"
    }
    
    return StreamingResponse(
        io.BytesIO(exported_report.content),
        media_type=media_type_map[format],
        headers={"Content-Disposition": f"attachment; filename={exported_report.filename}"}
    )

# Placeholder for /api/reports/{report_id}/viz.png if needed for visualization_url
# This would typically serve an image generated and stored by the report service.
# @router.get("/{report_id}/viz.png")
# async def get_report_visualization(report_id: str, user_id: str = Depends(get_current_user_id)):
#     # Logic to retrieve and serve the visualization image
#     # For now, this is just a placeholder
#     raise HTTPException(status_code=404, detail="Visualization not available")
