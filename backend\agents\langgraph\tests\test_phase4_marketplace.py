"""
Test Suite for Phase 4: Capability Marketplace Components

Comprehensive tests for the capability marketplace, trading engine,
certification system, and related components.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch

from ..marketplace.capability_marketplace import (
    CapabilityMarketplace, CapabilityListing, CapabilityRequest,
    CapabilityStatus, TradingMode
)
from ..marketplace.capability_registry import (
    CapabilityRegistry, CapabilityMetadata, CapabilityType
)
from ..marketplace.trading_engine import (
    TradingEngine, Trade, Bid, TradeStatus, BidStatus
)
from ..marketplace.certification_system import (
    CertificationSystem, CertificationRecord, CertificationLevel, CertificationStatus
)


class TestCapabilityMarketplace:
    """Test suite for CapabilityMarketplace."""

    @pytest.fixture
    async def marketplace(self):
        """Create a test marketplace instance."""
        marketplace = CapabilityMarketplace()
        await marketplace.initialize()
        return marketplace

    @pytest.mark.asyncio
    async def test_marketplace_initialization(self, marketplace):
        """Test marketplace initialization."""
        assert marketplace.registry is not None
        assert marketplace.trading_engine is not None
        assert marketplace.certification_system is not None
        assert isinstance(marketplace.listings, dict)
        assert isinstance(marketplace.requests, dict)

    @pytest.mark.asyncio
    async def test_list_capability(self, marketplace):
        """Test capability listing."""
        listing_data = {
            "capability_id": "test_cap_1",
            "agent_id": "test_agent_1",
            "name": "Test Capability",
            "description": "A test capability",
            "category": "data_analysis",
            "version": "1.0.0",
            "price": 10.0,
            "status": CapabilityStatus.AVAILABLE,
            "trading_mode": TradingMode.FIXED_PRICE
        }

        listing_id = await marketplace.list_capability(listing_data)
        assert listing_id is not None
        assert listing_id in marketplace.listings

        listing = marketplace.listings[listing_id]
        assert listing.name == "Test Capability"
        assert listing.agent_id == "test_agent_1"

    @pytest.mark.asyncio
    async def test_request_capability(self, marketplace):
        """Test capability request."""
        # First list a capability
        listing_data = {
            "capability_id": "test_cap_1",
            "agent_id": "test_agent_1",
            "name": "Test Capability",
            "description": "A test capability",
            "category": "data_analysis",
            "version": "1.0.0",
            "price": 10.0,
            "status": CapabilityStatus.AVAILABLE,
            "trading_mode": TradingMode.FIXED_PRICE
        }
        await marketplace.list_capability(listing_data)

        # Now request it
        request_data = {
            "requester_id": "test_requester",
            "requirements": {"category": "data_analysis"},
            "budget": 15.0,
            "deadline": datetime.now() + timedelta(hours=1)
        }

        request_id = await marketplace.request_capability(request_data)
        assert request_id is not None
        assert request_id in marketplace.requests

    @pytest.mark.asyncio
    async def test_discover_capabilities(self, marketplace):
        """Test capability discovery."""
        # List some capabilities first
        for i in range(3):
            listing_data = {
                "capability_id": f"test_cap_{i}",
                "agent_id": f"test_agent_{i}",
                "name": f"Test Capability {i}",
                "description": f"A test capability {i}",
                "category": "data_analysis",
                "version": "1.0.0",
                "price": 10.0 + i,
                "status": CapabilityStatus.AVAILABLE,
                "trading_mode": TradingMode.FIXED_PRICE
            }
            await marketplace.list_capability(listing_data)

        # Discover capabilities
        capabilities = await marketplace.discover_capabilities()
        assert len(capabilities) == 3

        # Test with filters
        filtered_capabilities = await marketplace.discover_capabilities(
            filters={"category": "data_analysis"}
        )
        assert len(filtered_capabilities) == 3


class TestCapabilityRegistry:
    """Test suite for CapabilityRegistry."""

    @pytest.fixture
    async def registry(self):
        """Create a test registry instance."""
        registry = CapabilityRegistry()
        await registry.initialize()
        return registry

    @pytest.mark.asyncio
    async def test_registry_initialization(self, registry):
        """Test registry initialization."""
        assert isinstance(registry.capabilities, dict)
        assert isinstance(registry.agent_capabilities, dict)
        assert isinstance(registry.type_index, dict)
        assert len(registry.type_index) == len(CapabilityType)

    @pytest.mark.asyncio
    async def test_register_capability(self, registry):
        """Test capability registration."""
        metadata = CapabilityMetadata(
            capability_id="test_cap_1",
            name="Test Capability",
            description="A test capability",
            capability_type=CapabilityType.DATA_ANALYSIS,
            version="1.0.0",
            agent_id="test_agent_1"
        )

        capability_id = await registry.register_capability(metadata)
        assert capability_id == "test_cap_1"
        assert capability_id in registry.capabilities
        assert "test_agent_1" in registry.agent_capabilities
        assert capability_id in registry.agent_capabilities["test_agent_1"]

    @pytest.mark.asyncio
    async def test_discover_capabilities(self, registry):
        """Test capability discovery."""
        # Register some capabilities
        for i in range(3):
            metadata = CapabilityMetadata(
                capability_id=f"test_cap_{i}",
                name=f"Test Capability {i}",
                description=f"A test capability {i}",
                capability_type=CapabilityType.DATA_ANALYSIS,
                version="1.0.0",
                agent_id=f"test_agent_{i}"
            )
            await registry.register_capability(metadata)

        # Discover all capabilities
        capabilities = await registry.discover_capabilities()
        assert len(capabilities) == 3

        # Test with filters
        filtered_capabilities = await registry.discover_capabilities(
            filters={"capability_type": CapabilityType.DATA_ANALYSIS}
        )
        assert len(filtered_capabilities) == 3


class TestTradingEngine:
    """Test suite for TradingEngine."""

    @pytest.fixture
    async def trading_engine(self):
        """Create a test trading engine instance."""
        engine = TradingEngine()
        await engine.initialize()
        return engine

    @pytest.mark.asyncio
    async def test_trading_engine_initialization(self, trading_engine):
        """Test trading engine initialization."""
        assert isinstance(trading_engine.trades, dict)
        assert isinstance(trading_engine.bids, dict)
        assert isinstance(trading_engine.agent_performance, dict)
        assert isinstance(trading_engine.market_prices, dict)

    @pytest.mark.asyncio
    async def test_initiate_trade(self, trading_engine):
        """Test trade initiation."""
        request_data = {
            "request_id": "test_request_1",
            "requester_id": "test_requester",
            "capability_requirements": {"category": "data_analysis"},
            "budget": 100.0,
            "deadline": datetime.now() + timedelta(hours=1)
        }

        # Mock matching capabilities
        with patch.object(trading_engine, '_find_matching_capabilities', return_value=[]):
            trade_id = await trading_engine.initiate_trade(request_data)
            assert trade_id is not None
            assert trade_id in trading_engine.trades

            trade = trading_engine.trades[trade_id]
            assert trade.requester_id == "test_requester"
            assert trade.budget == 100.0

    @pytest.mark.asyncio
    async def test_submit_bid(self, trading_engine):
        """Test bid submission."""
        # First create a trade
        request_data = {
            "request_id": "test_request_1",
            "requester_id": "test_requester",
            "capability_requirements": {"category": "data_analysis"},
            "budget": 100.0,
            "deadline": datetime.now() + timedelta(hours=1)
        }

        with patch.object(trading_engine, '_find_matching_capabilities', return_value=[]):
            trade_id = await trading_engine.initiate_trade(request_data)

        # Submit a bid
        bid_data = {
            "agent_id": "test_agent_1",
            "capability_id": "test_cap_1",
            "price": 50.0,
            "estimated_execution_time": 1800.0,
            "quality_score": 0.9
        }

        bid_id = await trading_engine.submit_bid(trade_id, bid_data)
        assert bid_id is not None
        assert bid_id in trading_engine.bids

        bid = trading_engine.bids[bid_id]
        assert bid.agent_id == "test_agent_1"
        assert bid.price == 50.0


class TestCertificationSystem:
    """Test suite for CertificationSystem."""

    @pytest.fixture
    async def certification_system(self):
        """Create a test certification system instance."""
        system = CertificationSystem()
        await system.initialize()
        return system

    @pytest.mark.asyncio
    async def test_certification_system_initialization(self, certification_system):
        """Test certification system initialization."""
        assert isinstance(certification_system.certifications, dict)
        assert isinstance(certification_system.criteria, dict)
        assert len(certification_system.criteria) == len(CertificationLevel)

    @pytest.mark.asyncio
    async def test_request_certification(self, certification_system):
        """Test certification request."""
        certification_id = await certification_system.request_certification(
            capability_id="test_cap_1",
            agent_id="test_agent_1",
            target_level=CertificationLevel.BASIC
        )

        assert certification_id is not None
        assert certification_id in certification_system.certifications

        certification = certification_system.certifications[certification_id]
        assert certification.capability_id == "test_cap_1"
        assert certification.agent_id == "test_agent_1"
        assert certification.target_level == CertificationLevel.BASIC

    @pytest.mark.asyncio
    async def test_get_certification_status(self, certification_system):
        """Test certification status retrieval."""
        # Request certification first
        certification_id = await certification_system.request_certification(
            capability_id="test_cap_1",
            agent_id="test_agent_1",
            target_level=CertificationLevel.BASIC
        )

        status = await certification_system.get_certification_status(certification_id)
        assert status is not None
        assert status["certification_id"] == certification_id
        assert status["status"] == CertificationStatus.PENDING.value

    @pytest.mark.asyncio
    async def test_get_agent_certifications(self, certification_system):
        """Test agent certifications retrieval."""
        # Request multiple certifications for the same agent
        for i in range(3):
            await certification_system.request_certification(
                capability_id=f"test_cap_{i}",
                agent_id="test_agent_1",
                target_level=CertificationLevel.BASIC
            )

        certifications = await certification_system.get_agent_certifications("test_agent_1")
        assert len(certifications) == 3

        for cert in certifications:
            assert cert.agent_id == "test_agent_1"
from ..marketplace.certification_system import (
    CertificationSystem, CertificationRecord, CertificationLevel, CertificationStatus
)
from ..events.event_bus import LangGraphEvent


class TestCapabilityMarketplace:
    """Test suite for CapabilityMarketplace."""
    
    @pytest.fixture
    async def marketplace(self):
        """Create a marketplace instance for testing."""
        marketplace = CapabilityMarketplace()
        # Mock the initialization to avoid external dependencies
        with patch.object(marketplace.registry, 'initialize'), \
             patch.object(marketplace.trading_engine, 'initialize'), \
             patch.object(marketplace.certification_system, 'initialize'):
            await marketplace.initialize()
        return marketplace
    
    @pytest.mark.asyncio
    async def test_marketplace_initialization(self):
        """Test marketplace initialization."""
        marketplace = CapabilityMarketplace()
        
        with patch.object(marketplace.registry, 'initialize') as mock_registry, \
             patch.object(marketplace.trading_engine, 'initialize') as mock_trading, \
             patch.object(marketplace.certification_system, 'initialize') as mock_cert:
            
            await marketplace.initialize()
            
            mock_registry.assert_called_once()
            mock_trading.assert_called_once()
            mock_cert.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_list_capability(self, marketplace):
        """Test capability listing functionality."""
        capability_data = {
            "capability_id": "test_cap_001",
            "agent_id": "test_agent",
            "name": "Test Capability",
            "description": "A test capability",
            "category": "data_analysis",
            "version": "1.0.0",
            "status": "available",
            "trading_mode": "fixed_price",
            "price": 10.0,
            "tags": ["test", "analysis"]
        }
        
        # Mock performance data
        with patch.object(marketplace, '_get_capability_performance', 
                         return_value={"score": 0.85, "success_rate": 0.92, "avg_execution_time": 1.5}):
            
            capability_id = await marketplace.list_capability(capability_data)
            
            assert capability_id == "test_cap_001"
            assert capability_id in marketplace.listings
            
            listing = marketplace.listings[capability_id]
            assert listing.name == "Test Capability"
            assert listing.agent_id == "test_agent"
            assert listing.price == 10.0
            assert listing.performance_score == 0.85
    
    @pytest.mark.asyncio
    async def test_request_capability(self, marketplace):
        """Test capability request functionality."""
        request_data = {
            "request_id": "req_001",
            "requester_id": "user_001",
            "requirements": {"type": "data_analysis", "complexity": "medium"},
            "budget": 50.0,
            "deadline": (datetime.now() + timedelta(hours=24)).isoformat()
        }
        
        # Mock finding matches
        with patch.object(marketplace, '_find_capability_matches', return_value=[]):
            request_id = await marketplace.request_capability(request_data)
            
            assert request_id == "req_001"
            assert request_id in marketplace.requests
            
            request = marketplace.requests[request_id]
            assert request.requester_id == "user_001"
            assert request.budget == 50.0
    
    @pytest.mark.asyncio
    async def test_discover_capabilities(self, marketplace):
        """Test capability discovery functionality."""
        # Add test capabilities
        test_capabilities = [
            CapabilityListing(
                capability_id=f"cap_{i}",
                agent_id=f"agent_{i}",
                name=f"Capability {i}",
                description=f"Test capability {i}",
                category="data_analysis",
                version="1.0.0",
                status=CapabilityStatus.AVAILABLE,
                trading_mode=TradingMode.FIXED_PRICE,
                performance_score=0.8 + i * 0.05
            )
            for i in range(3)
        ]
        
        for cap in test_capabilities:
            marketplace.listings[cap.capability_id] = cap
        
        # Test discovery without filters
        discovered = await marketplace.discover_capabilities()
        assert len(discovered) == 3
        
        # Test discovery with filters
        filters = {"min_performance": 0.85}
        filtered = await marketplace.discover_capabilities(filters)
        assert len(filtered) == 2  # Only capabilities with performance >= 0.85
    
    @pytest.mark.asyncio
    async def test_get_capability_recommendations(self, marketplace):
        """Test capability recommendation functionality."""
        # Mock recommendation methods
        with patch.object(marketplace, '_get_requester_profile', 
                         return_value={"preferences": {}, "history": []}), \
             patch.object(marketplace, '_analyze_requirements', 
                         return_value={"analyzed_needs": [], "complexity": "medium"}), \
             patch.object(marketplace, '_generate_recommendations', 
                         return_value=[("cap_001", 0.9), ("cap_002", 0.8)]):
            
            # Add test capabilities
            marketplace.listings["cap_001"] = CapabilityListing(
                capability_id="cap_001",
                agent_id="agent_001",
                name="High Performance Cap",
                description="High performance capability",
                category="data_analysis",
                version="1.0.0",
                status=CapabilityStatus.AVAILABLE,
                trading_mode=TradingMode.FIXED_PRICE,
                performance_score=0.95
            )
            
            recommendations = await marketplace.get_capability_recommendations(
                "user_001", {"task": "data analysis"}
            )
            
            assert len(recommendations) == 1  # Only cap_001 exists in listings
            assert recommendations[0][0].capability_id == "cap_001"
    
    @pytest.mark.asyncio
    async def test_optimize_pricing(self, marketplace):
        """Test pricing optimization functionality."""
        # Add test capability
        capability = CapabilityListing(
            capability_id="cap_001",
            agent_id="agent_001",
            name="Test Capability",
            description="Test capability",
            category="data_analysis",
            version="1.0.0",
            status=CapabilityStatus.AVAILABLE,
            trading_mode=TradingMode.FIXED_PRICE,
            price=10.0
        )
        marketplace.listings["cap_001"] = capability
        
        # Mock market data and optimization
        with patch.object(marketplace, '_get_market_data', 
                         return_value={"avg_price": 15.0, "demand": 0.8}), \
             patch.object(marketplace, '_calculate_optimal_price', 
                         return_value=12.0):
            
            optimized_price = await marketplace.optimize_pricing("cap_001")
            
            assert optimized_price == 12.0
            assert marketplace.listings["cap_001"].price == 12.0
    
    @pytest.mark.asyncio
    async def test_marketplace_analytics(self, marketplace):
        """Test marketplace analytics functionality."""
        # Add test data
        marketplace.listings["cap_001"] = CapabilityListing(
            capability_id="cap_001",
            agent_id="agent_001",
            name="Test Capability",
            description="Test capability",
            category="data_analysis",
            version="1.0.0",
            status=CapabilityStatus.AVAILABLE,
            trading_mode=TradingMode.FIXED_PRICE
        )
        
        # Mock analytics methods
        with patch.object(marketplace, '_get_top_categories', return_value=[]), \
             patch.object(marketplace, '_get_certification_distribution', return_value={}), \
             patch.object(marketplace, '_get_price_trends', return_value={}), \
             patch.object(marketplace, '_get_demand_patterns', return_value={}), \
             patch.object(marketplace, '_get_supply_analysis', return_value={}), \
             patch.object(marketplace, '_identify_optimization_opportunities', return_value=[]), \
             patch.object(marketplace, '_identify_market_gaps', return_value=[]), \
             patch.object(marketplace, '_identify_growth_areas', return_value=[]):
            
            analytics = await marketplace.get_marketplace_analytics()
            
            assert "overview" in analytics
            assert "performance" in analytics
            assert "market" in analytics
            assert "recommendations" in analytics
            
            assert analytics["overview"]["total_capabilities"] == 1
            assert analytics["overview"]["available_capabilities"] == 1


class TestCapabilityRegistry:
    """Test suite for CapabilityRegistry."""
    
    @pytest.fixture
    async def registry(self):
        """Create a registry instance for testing."""
        registry = CapabilityRegistry()
        with patch.object(registry, '_load_capabilities'):
            await registry.initialize()
        return registry
    
    @pytest.mark.asyncio
    async def test_register_capability(self, registry):
        """Test capability registration."""
        capability_data = {
            "capability_id": "test_cap_001",
            "agent_id": "test_agent",
            "name": "Test Capability",
            "description": "A test capability",
            "type": "data_analysis",
            "version": "1.0.0",
            "tags": ["test", "analysis"]
        }
        
        capability_id = await registry.register_capability(capability_data)
        
        assert capability_id == "test_cap_001"
        assert capability_id in registry.capabilities
        
        capability = registry.capabilities[capability_id]
        assert capability.name == "Test Capability"
        assert capability.capability_type == CapabilityType.DATA_ANALYSIS
    
    @pytest.mark.asyncio
    async def test_discover_capabilities(self, registry):
        """Test capability discovery."""
        # Register test capabilities
        for i in range(3):
            await registry.register_capability({
                "capability_id": f"cap_{i}",
                "agent_id": f"agent_{i}",
                "name": f"Capability {i}",
                "type": "data_analysis",
                "is_public": i < 2  # First two are public
            })
        
        # Test public discovery
        public_capabilities = await registry.discover_capabilities()
        assert len(public_capabilities) == 2
        
        # Test private discovery
        all_capabilities = await registry.discover_capabilities(include_private=True)
        assert len(all_capabilities) == 3
        
        # Test filtered discovery
        filtered = await registry.discover_capabilities(
            filters={"type": "data_analysis"}
        )
        assert len(filtered) == 2  # Only public ones
    
    @pytest.mark.asyncio
    async def test_check_compatibility(self, registry):
        """Test capability compatibility checking."""
        # Register capability with compatibility constraints
        await registry.register_capability({
            "capability_id": "restricted_cap",
            "agent_id": "agent_001",
            "name": "Restricted Capability",
            "type": "automation",
            "compatible_agents": ["agent_002", "agent_003"],
            "incompatible_agents": ["agent_004"]
        })
        
        # Test compatibility
        assert await registry.check_compatibility("restricted_cap", "agent_002") == True
        assert await registry.check_compatibility("restricted_cap", "agent_004") == False
        assert await registry.check_compatibility("restricted_cap", "agent_005") == False
    
    @pytest.mark.asyncio
    async def test_update_capability_performance(self, registry):
        """Test capability performance updates."""
        # Register capability
        await registry.register_capability({
            "capability_id": "perf_cap",
            "agent_id": "agent_001",
            "name": "Performance Capability",
            "type": "optimization"
        })
        
        # Update performance
        performance_data = {
            "execution_time": 2.5,
            "memory_usage": 128.0,
            "success_rate": 0.95
        }
        
        await registry.update_capability_performance("perf_cap", performance_data)
        
        capability = registry.capabilities["perf_cap"]
        assert capability.average_execution_time == 2.5
        assert capability.memory_usage == 128.0
        assert capability.success_rate == 0.95
    
    def test_registry_stats(self, registry):
        """Test registry statistics."""
        # Add test capabilities
        registry.capabilities["cap_001"] = CapabilityMetadata(
            capability_id="cap_001",
            name="Test Cap 1",
            description="Test",
            capability_type=CapabilityType.DATA_ANALYSIS,
            version="1.0.0",
            agent_id="agent_001",
            is_certified=True
        )
        
        registry.capabilities["cap_002"] = CapabilityMetadata(
            capability_id="cap_002",
            name="Test Cap 2",
            description="Test",
            capability_type=CapabilityType.CONTENT_GENERATION,
            version="1.0.0",
            agent_id="agent_002",
            is_public=False
        )
        
        # Update indexes
        registry._update_indexes(registry.capabilities["cap_001"])
        registry._update_indexes(registry.capabilities["cap_002"])
        
        stats = registry.get_registry_stats()
        
        assert stats["total_capabilities"] == 2
        assert stats["certified_capabilities"] == 1
        assert stats["public_capabilities"] == 1
        assert "data_analysis" in stats["capabilities_by_type"]
