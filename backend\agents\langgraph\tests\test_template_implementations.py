"""
Integration tests for specific template implementations.

This module tests the actual template implementations including
SimpleAnalysisTemplate, BusinessAnalysisTemplate, and DashboardGenerationTemplate.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch

from ..templates.simple_analysis_template import SimpleAnalysisTemplate
from ..templates.business_analysis_template import BusinessAnalysisTemplate
from ..templates.dashboard_generation_template import DashboardGenerationTemplate
from ..states.agent_state import DatageniusAgentState


class TestSimpleAnalysisTemplate:
    """Test Simple Analysis Template implementation."""
    
    @pytest.fixture
    def template(self):
        """Create simple analysis template."""
        return SimpleAnalysisTemplate()
    
    def test_template_initialization(self, template):
        """Test template initialization."""
        assert template.metadata.name == "simple_analysis"
        assert template.metadata.category == "analysis"
        assert template.metadata.complexity == "simple"
        assert "analysis-agent" in template.required_agents
        assert len(template.parameters) > 0
    
    def test_parameter_schema(self, template):
        """Test parameter schema generation."""
        schema = template.get_parameter_schema()
        
        assert "data_source" in schema["parameters"]
        assert "analysis_type" in schema["parameters"]
        assert "user_id" in schema["parameters"]
        assert "output_format" in schema["parameters"]
        
        # Check required parameters
        assert schema["parameters"]["data_source"]["required"] is True
        assert schema["parameters"]["user_id"]["required"] is True
        
        # Check optional parameters with defaults
        assert schema["parameters"]["analysis_type"]["required"] is False
        assert schema["parameters"]["analysis_type"]["default"] == "comprehensive"
    
    def test_parameter_validation_success(self, template):
        """Test successful parameter validation."""
        valid_params = {
            "data_source": "business_profile",
            "user_id": "test_user_123",
            "business_profile_id": "profile_456"
        }
        
        is_valid, errors = template.validate_parameters(valid_params)
        assert is_valid is True
        assert len(errors) == 0
    
    def test_parameter_validation_failure(self, template):
        """Test parameter validation failure."""
        invalid_params = {
            "data_source": "invalid_source",  # Not in allowed values
            "user_id": "test_user_123"
        }
        
        is_valid, errors = template.validate_parameters(invalid_params)
        assert is_valid is False
        assert len(errors) > 0
    
    @pytest.mark.asyncio
    async def test_workflow_creation(self, template):
        """Test workflow creation."""
        params = {
            "data_source": "business_profile",
            "user_id": "test_user_123",
            "business_profile_id": "profile_456",
            "analysis_type": "comprehensive"
        }
        
        # Mock event bus
        with patch('backend.agents.langgraph.templates.base_template.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            workflow = await template.create_workflow(params)
            
            assert workflow is not None
            assert hasattr(workflow, '_template_params')
            assert workflow._template_params == params
    
    @pytest.mark.asyncio
    async def test_workflow_execution_simulation(self, template):
        """Test simulated workflow execution."""
        params = {
            "data_source": "business_profile",
            "user_id": "test_user_123",
            "business_profile_id": "profile_456",
            "analysis_type": "comprehensive",
            "output_format": "markdown"
        }
        
        # Create initial state
        initial_state = DatageniusAgentState()
        initial_state["template_params"] = params
        
        # Test individual nodes
        state = await template._load_data_node(initial_state)
        assert state["workflow_status"] == "data_loaded"
        assert "loaded_data" in state
        
        state = await template._validate_data_node(state)
        assert state["workflow_status"] == "data_validated"
        assert "data_validation" in state
        
        state = await template._perform_analysis_node(state)
        assert state["workflow_status"] == "analysis_completed"
        assert "analysis_results" in state
        
        state = await template._generate_report_node(state)
        assert state["workflow_status"] == "report_generated"
        assert "generated_report" in state
        
        state = await template._finalize_output_node(state)
        assert state["workflow_status"] == "completed"
        assert "final_output" in state


class TestBusinessAnalysisTemplate:
    """Test Business Analysis Template implementation."""
    
    @pytest.fixture
    def template(self):
        """Create business analysis template."""
        return BusinessAnalysisTemplate()
    
    def test_template_initialization(self, template):
        """Test template initialization."""
        assert template.metadata.name == "business_analysis"
        assert template.metadata.category == "business"
        assert template.metadata.complexity == "complex"
        assert "business-analyst" in template.required_agents
        assert "market-research" in template.required_agents
        assert len(template.optional_agents) > 0
    
    def test_parameter_schema(self, template):
        """Test parameter schema generation."""
        schema = template.get_parameter_schema()
        
        # Check required parameters
        assert "business_profile_id" in schema["parameters"]
        assert "user_id" in schema["parameters"]
        assert schema["parameters"]["business_profile_id"]["required"] is True
        assert schema["parameters"]["user_id"]["required"] is True
        
        # Check optional parameters
        assert "analysis_depth" in schema["parameters"]
        assert "include_competitive_analysis" in schema["parameters"]
        assert "include_financial_projections" in schema["parameters"]
        
        # Check default values
        assert schema["parameters"]["analysis_depth"]["default"] == "comprehensive"
        assert schema["parameters"]["include_competitive_analysis"]["default"] is True
    
    def test_parameter_validation_success(self, template):
        """Test successful parameter validation."""
        valid_params = {
            "business_profile_id": "profile_123",
            "user_id": "user_456",
            "analysis_depth": "comprehensive",
            "include_competitive_analysis": True,
            "include_financial_projections": True
        }
        
        is_valid, errors = template.validate_parameters(valid_params)
        assert is_valid is True
        assert len(errors) == 0
    
    def test_parameter_validation_missing_required(self, template):
        """Test parameter validation with missing required fields."""
        invalid_params = {
            "user_id": "user_456"
            # Missing business_profile_id
        }
        
        is_valid, errors = template.validate_parameters(invalid_params)
        assert is_valid is False
        assert any("business_profile_id" in error for error in errors)
    
    @pytest.mark.asyncio
    async def test_workflow_creation_full_features(self, template):
        """Test workflow creation with all features enabled."""
        params = {
            "business_profile_id": "profile_123",
            "user_id": "user_456",
            "analysis_depth": "comprehensive",
            "include_competitive_analysis": True,
            "include_financial_projections": True,
            "include_strategic_recommendations": True
        }
        
        # Mock event bus
        with patch('backend.agents.langgraph.templates.base_template.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            workflow = await template.create_workflow(params)
            
            assert workflow is not None
            assert hasattr(workflow, '_template_params')
    
    @pytest.mark.asyncio
    async def test_workflow_creation_minimal_features(self, template):
        """Test workflow creation with minimal features."""
        params = {
            "business_profile_id": "profile_123",
            "user_id": "user_456",
            "analysis_depth": "basic",
            "include_competitive_analysis": False,
            "include_financial_projections": False,
            "include_strategic_recommendations": False
        }
        
        # Mock event bus
        with patch('backend.agents.langgraph.templates.base_template.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            workflow = await template.create_workflow(params)
            
            assert workflow is not None
    
    @pytest.mark.asyncio
    async def test_core_workflow_nodes(self, template):
        """Test core workflow nodes execution."""
        params = {
            "business_profile_id": "profile_123",
            "user_id": "user_456",
            "analysis_depth": "comprehensive"
        }
        
        # Create initial state
        initial_state = DatageniusAgentState()
        initial_state["template_params"] = params
        
        # Test core nodes
        state = await template._initialize_analysis_node(initial_state)
        assert state["workflow_status"] == "initialized"
        assert "analysis_context" in state
        
        state = await template._load_business_profile_node(state)
        assert state["workflow_status"] == "profile_loaded"
        assert "business_profile" in state
        
        state = await template._market_research_node(state)
        assert state["workflow_status"] == "market_research_completed"
        assert "market_research" in state
        
        state = await template._business_analysis_node(state)
        assert state["workflow_status"] == "business_analysis_completed"
        assert "business_analysis" in state


class TestDashboardGenerationTemplate:
    """Test Dashboard Generation Template implementation."""
    
    @pytest.fixture
    def template(self):
        """Create dashboard generation template."""
        return DashboardGenerationTemplate()
    
    def test_template_initialization(self, template):
        """Test template initialization."""
        assert template.metadata.name == "dashboard_generation"
        assert template.metadata.category == "dashboard"
        assert template.metadata.complexity == "medium"
        assert "dashboard-generator" in template.required_agents
        assert "data-analyst" in template.required_agents
    
    def test_parameter_schema(self, template):
        """Test parameter schema generation."""
        schema = template.get_parameter_schema()
        
        # Check required parameters
        assert "dashboard_name" in schema["parameters"]
        assert "user_id" in schema["parameters"]
        assert schema["parameters"]["dashboard_name"]["required"] is True
        assert schema["parameters"]["user_id"]["required"] is True
        
        # Check optional parameters with defaults
        assert "dashboard_type" in schema["parameters"]
        assert "layout_preference" in schema["parameters"]
        assert "update_frequency" in schema["parameters"]
        
        # Check validation rules
        dashboard_type_param = schema["parameters"]["dashboard_type"]
        assert "business_overview" in dashboard_type_param["validation_rules"]["allowed_values"]
    
    def test_parameter_validation_success(self, template):
        """Test successful parameter validation."""
        valid_params = {
            "dashboard_name": "My Business Dashboard",
            "user_id": "user_123",
            "dashboard_type": "business_overview",
            "data_sources": ["business_profile", "analytics_data"],
            "widget_types": ["chart", "metric", "table"]
        }
        
        is_valid, errors = template.validate_parameters(valid_params)
        assert is_valid is True
        assert len(errors) == 0
    
    def test_parameter_validation_invalid_dashboard_type(self, template):
        """Test parameter validation with invalid dashboard type."""
        invalid_params = {
            "dashboard_name": "Test Dashboard",
            "user_id": "user_123",
            "dashboard_type": "invalid_type"  # Not in allowed values
        }
        
        is_valid, errors = template.validate_parameters(invalid_params)
        assert is_valid is False
        assert len(errors) > 0
    
    @pytest.mark.asyncio
    async def test_workflow_creation(self, template):
        """Test workflow creation."""
        params = {
            "dashboard_name": "Business Overview Dashboard",
            "user_id": "user_123",
            "dashboard_type": "business_overview",
            "include_interactive_features": True
        }
        
        # Mock event bus
        with patch('backend.agents.langgraph.templates.base_template.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            workflow = await template.create_workflow(params)
            
            assert workflow is not None
            assert hasattr(workflow, '_template_params')
    
    @pytest.mark.asyncio
    async def test_workflow_creation_no_interactivity(self, template):
        """Test workflow creation without interactive features."""
        params = {
            "dashboard_name": "Simple Dashboard",
            "user_id": "user_123",
            "dashboard_type": "operational_dashboard",
            "include_interactive_features": False
        }
        
        # Mock event bus
        with patch('backend.agents.langgraph.templates.base_template.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            workflow = await template.create_workflow(params)
            
            assert workflow is not None
    
    @pytest.mark.asyncio
    async def test_dashboard_workflow_nodes(self, template):
        """Test dashboard workflow nodes execution."""
        params = {
            "dashboard_name": "Test Dashboard",
            "user_id": "user_123",
            "dashboard_type": "business_overview",
            "data_sources": ["business_profile"],
            "widget_types": ["metric", "chart"]
        }
        
        # Create initial state
        initial_state = DatageniusAgentState()
        initial_state["template_params"] = params
        
        # Test workflow nodes
        state = await template._analyze_requirements_node(initial_state)
        assert state["workflow_status"] == "requirements_analyzed"
        assert "dashboard_requirements" in state
        
        state = await template._design_layout_node(state)
        assert state["workflow_status"] == "layout_designed"
        assert "layout_design" in state
        
        state = await template._generate_data_queries_node(state)
        assert state["workflow_status"] == "queries_generated"
        assert "data_queries" in state
        
        state = await template._create_widgets_node(state)
        assert state["workflow_status"] == "widgets_created"
        assert "dashboard_widgets" in state
        assert len(state["dashboard_widgets"]) > 0
    
    def test_theme_color_generation(self, template):
        """Test theme color generation."""
        professional_colors = template._get_theme_colors("professional")
        assert "primary" in professional_colors
        assert "background" in professional_colors
        assert "text" in professional_colors
        
        dark_colors = template._get_theme_colors("dark")
        assert dark_colors["background"] != professional_colors["background"]
    
    def test_widget_size_optimization(self, template):
        """Test widget size optimization."""
        mock_widgets = [
            {"type": "metric_card"},
            {"type": "line_chart"},
            {"type": "data_table"}
        ]
        
        executive_sizes = template._optimize_widget_sizes(mock_widgets, "executives")
        analyst_sizes = template._optimize_widget_sizes(mock_widgets, "analysts")
        
        assert "metric_cards" in executive_sizes
        assert "charts" in executive_sizes
        assert "tables" in executive_sizes
        
        # Executives should have larger metric cards
        assert executive_sizes["metric_cards"]["width"] == "large"
        assert analyst_sizes["metric_cards"]["width"] == "medium"


class TestTemplateIntegration:
    """Test integration between templates and template manager."""
    
    @pytest.mark.asyncio
    async def test_all_templates_registration(self):
        """Test that all templates can be registered and used."""
        from ..templates.template_manager import TemplateManager
        
        manager = TemplateManager(enable_caching=False)
        
        # Register all templates
        manager.register_template(SimpleAnalysisTemplate, "simple_analysis", "1.0.0")
        manager.register_template(BusinessAnalysisTemplate, "business_analysis", "1.0.0")
        manager.register_template(DashboardGenerationTemplate, "dashboard_generation", "1.0.0")
        
        # Test that all templates are listed
        templates = manager.list_templates()
        template_names = [t["name"] for t in templates]
        
        assert "simple_analysis" in template_names
        assert "business_analysis" in template_names
        assert "dashboard_generation" in template_names
    
    @pytest.mark.asyncio
    async def test_template_workflow_creation_through_manager(self):
        """Test creating workflows through template manager."""
        from ..templates.template_manager import TemplateManager
        
        manager = TemplateManager(enable_caching=False)
        manager.register_template(SimpleAnalysisTemplate, "simple_analysis", "1.0.0")
        
        params = {
            "data_source": "business_profile",
            "user_id": "test_user",
            "business_profile_id": "profile_123"
        }
        
        # Mock event bus
        with patch('backend.agents.langgraph.templates.base_template.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            workflow = await manager.create_workflow("simple_analysis", params)
            
            assert workflow is not None
            
            # Check metrics
            metrics = manager.get_metrics()
            assert metrics["templates_created"] >= 1
