"""
Configuration settings for the Datagenius backend.

This module provides configuration settings using the new unified ConfigurationService.
It maintains backward compatibility by exposing configuration values as module-level variables.
"""

import os
import asyncio
import logging
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

# Import the new configuration system
from .services.configuration_service import get_configuration_service
from .config_migration import ensure_migration_complete, get_compat_config

# Initialize configuration service
_config_service = get_configuration_service()
_compat_config = get_compat_config()

# Ensure migration is complete on module import
try:
    # Run migration in a new event loop if needed
    loop = None
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

    if loop.is_running():
        # If loop is already running, schedule migration for later
        logger.info("Event loop is running, configuration migration will be handled by startup")
    else:
        # Run migration synchronously
        loop.run_until_complete(ensure_migration_complete())
        logger.info("Configuration migration completed during module import")

except Exception as e:
    logger.error(f"Failed to complete configuration migration: {e}")
    logger.info("Falling back to environment variables")

# Load configurations with fallback to environment variables
def _get_config_value(config_key: str, field_key: str, env_key: str, default_value, value_type=str):
    """Get configuration value with fallback to environment variables."""
    try:
        # Try to get from configuration service
        config = _config_service.get_config_sync(config_key)
        if config and field_key in config:
            value = config[field_key]
            if value_type == bool:
                return value if isinstance(value, bool) else str(value).lower() == "true"
            elif value_type == int:
                return int(value)
            elif value_type == float:
                return float(value)
            else:
                return str(value)
    except Exception as e:
        logger.debug(f"Failed to get {config_key}.{field_key} from config service: {e}")

    # Fallback to environment variable
    env_value = os.getenv(env_key, default_value)
    if value_type == bool:
        return str(env_value).lower() == "true"
    elif value_type == int:
        return int(env_value)
    elif value_type == float:
        return float(env_value)
    else:
        return str(env_value)

# Database configuration
DATABASE_URL = _get_config_value("database", "url", "DATABASE_URL", "sqlite:///./datagenius.db")
DATABASE_ECHO = _get_config_value("database", "echo", "DATABASE_ECHO", "false", bool)

# JWT configuration
JWT_SECRET_KEY = _get_config_value("security", "jwt_secret_key", "JWT_SECRET_KEY", "your-secret-key-here")
JWT_ALGORITHM = _get_config_value("security", "jwt_algorithm", "JWT_ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = _get_config_value("security", "access_token_expire_minutes", "ACCESS_TOKEN_EXPIRE_MINUTES", "30", int)
REFRESH_TOKEN_EXPIRE_DAYS = _get_config_value("security", "refresh_token_expire_days", "REFRESH_TOKEN_EXPIRE_DAYS", "7", int)

# Security configuration
MAX_REFRESH_COUNT = _get_config_value("security", "max_refresh_count", "MAX_REFRESH_COUNT", "5", int)
MAX_CONCURRENT_SESSIONS = _get_config_value("security", "max_concurrent_sessions", "MAX_CONCURRENT_SESSIONS", "3", int)
ENFORCE_IP_VALIDATION = _get_config_value("security", "enforce_ip_validation", "ENFORCE_IP_VALIDATION", "false", bool)
IP_CHANGE_LOCKOUT = _get_config_value("security", "ip_change_lockout", "IP_CHANGE_LOCKOUT", "300", int)

# Redis configuration (fallback to environment)
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")

# File upload configuration (fallback to environment)
UPLOAD_DIR = os.getenv("UPLOAD_DIR", "./uploads")
# Additional configuration values
MAX_UPLOAD_SIZE = _get_config_value("files", "max_upload_size", "MAX_UPLOAD_SIZE", "10485760", int)

# LLM provider configuration
def _get_llm_provider_config(provider: str, field: str, env_key: str, default_value: str) -> str:
    """Get LLM provider configuration with fallback."""
    try:
        llm_config = _config_service.get_config_sync("llm")
        if llm_config and "providers" in llm_config:
            provider_config = llm_config["providers"].get(provider, {})
            if field in provider_config:
                return provider_config[field]
    except Exception as e:
        logger.debug(f"Failed to get LLM config for {provider}.{field}: {e}")

    return os.getenv(env_key, default_value)

# LLM API Keys
GROQ_API_KEY = _get_llm_provider_config("groq", "api_key", "GROQ_API_KEY", "")
OPENAI_API_KEY = _get_llm_provider_config("openai", "api_key", "OPENAI_API_KEY", "")
GEMINI_API_KEY = _get_llm_provider_config("gemini", "api_key", "GEMINI_API_KEY", "")
OPENROUTER_API_KEY = _get_llm_provider_config("openrouter", "api_key", "OPENROUTER_API_KEY", "")
ANTHROPIC_API_KEY = _get_llm_provider_config("anthropic", "api_key", "ANTHROPIC_API_KEY", "")

# LLM Endpoints
GROQ_ENDPOINT = _get_llm_provider_config("groq", "base_url", "GROQ_ENDPOINT", "https://api.groq.com/openai/v1")
OPENAI_ENDPOINT = _get_llm_provider_config("openai", "base_url", "OPENAI_ENDPOINT", "https://api.openai.com/v1")
GEMINI_ENDPOINT = _get_llm_provider_config("gemini", "base_url", "GEMINI_ENDPOINT", "https://generativelanguage.googleapis.com")
OPENROUTER_ENDPOINT = _get_llm_provider_config("openrouter", "base_url", "OPENROUTER_ENDPOINT", "https://openrouter.ai/api/v1")
OLLAMA_ENDPOINT = _get_llm_provider_config("ollama", "base_url", "OLLAMA_ENDPOINT", "http://localhost:11434")
REQUESTY_ENDPOINT = _get_llm_provider_config("requesty", "base_url", "REQUESTY_ENDPOINT", "https://router.requesty.ai/v1")
ANTHROPIC_ENDPOINT = _get_llm_provider_config("anthropic", "base_url", "ANTHROPIC_ENDPOINT", "https://api.anthropic.com")

# Google OAuth configuration
GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID", "")
GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET", "")
GOOGLE_REDIRECT_URI = os.getenv("GOOGLE_REDIRECT_URI", "")

# MCP configuration
MCP_ENCRYPTION_KEY = os.getenv("MCP_ENCRYPTION_KEY", "")

# Vector database configuration
MEM0_API_KEY = os.getenv("MEM0_API_KEY", "")
MEM0_ENDPOINT = os.getenv("MEM0_ENDPOINT", "https://api.mem0.ai")
MEM0_SELF_HOSTED = os.getenv("MEM0_SELF_HOSTED", "false").lower() == "true"
MEM0_DEFAULT_TTL = int(os.getenv("MEM0_DEFAULT_TTL", "86400"))
MEM0_MAX_MEMORIES = int(os.getenv("MEM0_MAX_MEMORIES", "1000"))
MEM0_MEMORY_THRESHOLD = float(os.getenv("MEM0_MEMORY_THRESHOLD", "0.8"))

# Qdrant configuration
QDRANT_HOST = os.getenv("QDRANT_HOST", "localhost")
QDRANT_PORT = int(os.getenv("QDRANT_PORT", "6333"))

# Chunking configuration
CHUNKING_PERFORMANCE_PROFILE = os.getenv("CHUNKING_PERFORMANCE_PROFILE", "balanced")
CHUNKING_USE_ADAPTIVE = os.getenv("CHUNKING_USE_ADAPTIVE", "true").lower() == "true"
CHUNKING_ENABLE_CACHING = os.getenv("CHUNKING_ENABLE_CACHING", "true").lower() == "true"
CHUNKING_BATCH_SIZE = int(os.getenv("CHUNKING_BATCH_SIZE", "10"))
CHUNKING_PARALLEL_WORKERS = int(os.getenv("CHUNKING_PARALLEL_WORKERS", "4"))

# Frontend URL
FRONTEND_URL = os.getenv("FRONTEND_URL", "http://localhost:5173")

# Email configuration
EMAIL_ENABLED = os.getenv("EMAIL_ENABLED", "false").lower() == "true"
EMAIL_SENDER = os.getenv("EMAIL_SENDER", "<EMAIL>")
EMAIL_SMTP_SERVER = os.getenv("EMAIL_SMTP_SERVER", "smtp.gmail.com")
EMAIL_SMTP_PORT = int(os.getenv("EMAIL_SMTP_PORT", "587"))
EMAIL_SMTP_USER = os.getenv("EMAIL_SMTP_USER", "")
EMAIL_SMTP_PASSWORD = os.getenv("EMAIL_SMTP_PASSWORD", "")
EMAIL_USE_TLS = os.getenv("EMAIL_USE_TLS", "true").lower() == "true"

# CORS configuration
CORS_ORIGINS = os.getenv("CORS_ORIGINS", "*").split(",")

# Debug configuration
DEBUG = os.getenv("DEBUG", "false").lower() == "true"

# Application configuration
APP_NAME = os.getenv("APP_NAME", "Datagenius")
APP_VERSION = os.getenv("APP_VERSION", "1.0.0")
APP_DESCRIPTION = os.getenv("APP_DESCRIPTION", "AI-powered data analysis platform")
ENVIRONMENT = os.getenv("ENVIRONMENT", "development")

# Configuration service access functions
def get_config_service():
    """Get the configuration service instance."""
    return _config_service

def get_app_config():
    """Get application configuration."""
    try:
        return _config_service.get_config_sync("app")
    except Exception as e:
        logger.debug(f"Failed to get app config: {e}")
        return None

def get_database_config():
    """Get database configuration."""
    try:
        return _config_service.get_config_sync("database")
    except Exception as e:
        logger.debug(f"Failed to get database config: {e}")
        return None

def get_security_config():
    """Get security configuration."""
    try:
        return _config_service.get_config_sync("security")
    except Exception as e:
        logger.debug(f"Failed to get security config: {e}")
        return None

def get_llm_config():
    """Get LLM configuration."""
    try:
        return _config_service.get_config_sync("llm")
    except Exception as e:
        logger.debug(f"Failed to get LLM config: {e}")
        return None

# Log configuration settings
logger.info("Configuration system initialized using ConfigurationService")
logger.info(f"DATABASE_URL: {DATABASE_URL.split('://')[0] if DATABASE_URL else 'Not configured'}://*****")
logger.info(f"DATABASE_ECHO: {DATABASE_ECHO}")
logger.info(f"JWT_ALGORITHM: {JWT_ALGORITHM}")
logger.info(f"ACCESS_TOKEN_EXPIRE_MINUTES: {ACCESS_TOKEN_EXPIRE_MINUTES}")
logger.info(f"REFRESH_TOKEN_EXPIRE_DAYS: {REFRESH_TOKEN_EXPIRE_DAYS}")
logger.info(f"UPLOAD_DIR: {UPLOAD_DIR}")
logger.info(f"MAX_UPLOAD_SIZE: {MAX_UPLOAD_SIZE}")
logger.info(f"EMAIL_ENABLED: {EMAIL_ENABLED}")
logger.info(f"DEBUG: {DEBUG}")
logger.info(f"APP_NAME: {APP_NAME}")
logger.info(f"APP_VERSION: {APP_VERSION}")
logger.info(f"GOOGLE_CLIENT_ID: {'Configured' if GOOGLE_CLIENT_ID else 'Not configured'}")
logger.info(f"GOOGLE_REDIRECT_URI: {GOOGLE_REDIRECT_URI}")
logger.info(f"MEM0_API_KEY: {'Configured' if MEM0_API_KEY else 'Not configured'}")
logger.info(f"MEM0_SELF_HOSTED: {MEM0_SELF_HOSTED}")
logger.info(f"MEM0_DEFAULT_TTL: {MEM0_DEFAULT_TTL}")
logger.info(f"CHUNKING_PERFORMANCE_PROFILE: {CHUNKING_PERFORMANCE_PROFILE}")
logger.info(f"CHUNKING_USE_ADAPTIVE: {CHUNKING_USE_ADAPTIVE}")
logger.info(f"CHUNKING_ENABLE_CACHING: {CHUNKING_ENABLE_CACHING}")
logger.info(f"CHUNKING_BATCH_SIZE: {CHUNKING_BATCH_SIZE}")
logger.info(f"CHUNKING_PARALLEL_WORKERS: {CHUNKING_PARALLEL_WORKERS}")

# Export the configuration service for direct access
app_config = _config_service


def get_settings():
    """
    Get configuration settings for backward compatibility with tests.

    Returns:
        Configuration object with all settings as attributes
    """
    class Settings:
        def __init__(self):
            # Database configuration
            self.database_url = DATABASE_URL
            self.database_echo = DATABASE_ECHO

            # JWT configuration
            self.jwt_secret_key = JWT_SECRET_KEY
            self.jwt_algorithm = JWT_ALGORITHM
            self.access_token_expire_minutes = ACCESS_TOKEN_EXPIRE_MINUTES
            self.refresh_token_expire_days = REFRESH_TOKEN_EXPIRE_DAYS

            # Security configuration
            self.max_refresh_count = MAX_REFRESH_COUNT
            self.max_concurrent_sessions = MAX_CONCURRENT_SESSIONS
            self.enforce_ip_validation = ENFORCE_IP_VALIDATION
            self.ip_change_lockout = IP_CHANGE_LOCKOUT

            # Redis configuration
            self.redis_url = REDIS_URL

            # File upload configuration
            self.upload_dir = UPLOAD_DIR
            self.max_upload_size = MAX_UPLOAD_SIZE

            # LLM provider configuration
            self.groq_api_key = GROQ_API_KEY
            self.openai_api_key = OPENAI_API_KEY
            self.gemini_api_key = GEMINI_API_KEY
            self.openrouter_api_key = OPENROUTER_API_KEY

            # Google OAuth configuration
            self.google_client_id = GOOGLE_CLIENT_ID
            self.google_client_secret = GOOGLE_CLIENT_SECRET
            self.google_redirect_uri = GOOGLE_REDIRECT_URI

            # LLM endpoint configuration
            self.groq_endpoint = GROQ_ENDPOINT
            self.openai_endpoint = OPENAI_ENDPOINT
            self.gemini_endpoint = GEMINI_ENDPOINT
            self.openrouter_endpoint = OPENROUTER_ENDPOINT
            self.ollama_endpoint = OLLAMA_ENDPOINT
            self.requesty_endpoint = REQUESTY_ENDPOINT

            # mem0ai configuration
            self.mem0_api_key = MEM0_API_KEY
            self.mem0_endpoint = MEM0_ENDPOINT
            self.mem0_self_hosted = MEM0_SELF_HOSTED
            self.mem0_default_ttl = MEM0_DEFAULT_TTL
            self.mem0_max_memories = MEM0_MAX_MEMORIES
            self.mem0_memory_threshold = MEM0_MEMORY_THRESHOLD

            # Qdrant configuration
            self.qdrant_host = QDRANT_HOST
            self.qdrant_port = QDRANT_PORT

            # Chunking configuration
            self.chunking_performance_profile = CHUNKING_PERFORMANCE_PROFILE
            self.chunking_use_adaptive = CHUNKING_USE_ADAPTIVE
            self.chunking_enable_caching = CHUNKING_ENABLE_CACHING
            self.chunking_batch_size = CHUNKING_BATCH_SIZE
            self.chunking_parallel_workers = CHUNKING_PARALLEL_WORKERS

            # Frontend URL
            self.frontend_url = FRONTEND_URL

            # Email configuration
            self.email_enabled = EMAIL_ENABLED
            self.email_sender = EMAIL_SENDER
            self.email_smtp_server = EMAIL_SMTP_SERVER
            self.email_smtp_port = EMAIL_SMTP_PORT
            self.email_smtp_user = EMAIL_SMTP_USER
            self.email_smtp_password = EMAIL_SMTP_PASSWORD
            self.email_use_tls = EMAIL_USE_TLS

            # CORS configuration
            self.cors_origins = CORS_ORIGINS

            # Debug configuration
            self.debug = DEBUG

            # Application configuration
            self.app_name = APP_NAME
            self.app_version = APP_VERSION
            self.app_description = APP_DESCRIPTION

    return Settings()
