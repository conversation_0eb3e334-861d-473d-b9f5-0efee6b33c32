"""
Enhanced MCP Server API endpoints for Datagenius.

This module provides REST API endpoints for managing MCP servers with support
for JSON configurations, multiple transport types, and tool discovery.
"""

import logging
import uuid
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks, Request

from ..models.mcp_server import (
    MCPServerCreate, MCPServerUpdate, MCPServerResponse, MCPServerListResponse,
    MCPServerAction, MCPServerActionResponse, MCPDiscoveryResponse,
    MCPInputVariableCreate, MCPInputVariableResponse
)
from ..models.auth import User
from ..database import MCPServer, MCPTool, MCPResource, MCPPrompt, MCPInputVariable
from ..dependencies import get_db_service
from ..repositories.database_service import DatabaseService
from ..auth import get_current_active_user
from ..services.mcp_server_manager import MCPServerManager
from ..utils.validation import mcp_validator, request_validator
from ..utils.error_handlers import (
    error_handler, MCPValidationError, MCPConfigurationError,
    MCPConnectionError, ValidationErrorCollector
)

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/mcp-servers", tags=["MCP Servers"])

# Initialize MCP server manager
mcp_manager = MCPServerManager()


@router.post("", response_model=MCPServerResponse)
async def create_mcp_server(
    server_data: MCPServerCreate,
    background_tasks: BackgroundTasks,
    db_service: DatabaseService = Depends(get_db_service),
    current_user: User = Depends(get_current_active_user)
):
    """
    Create a new MCP server configuration.

    Supports both JSON configuration (VS Code compatible) and form-based configuration.
    """
    request_id = str(uuid.uuid4())
    logger.info(f"User {current_user.id} creating MCP server: {server_data.name} (Request: {request_id})")

    try:
        with db_service.get_session() as db:
            # Comprehensive validation
            is_valid, validation_errors = await mcp_validator.validate_server_creation(
                server_data, current_user.id
            )

            if not is_valid:
                error_collector = ValidationErrorCollector()
                for error in validation_errors:
                    error_collector.add_error(error)
                raise error_collector.to_exception()

            # Create server based on configuration type
            if server_data.config_type == "json" and server_data.json_config:
                server = await mcp_manager.create_server_from_json(db, current_user.id, server_data)
            elif server_data.config_type == "form" and server_data.server_config:
                server = await mcp_manager.create_server_from_form(db, current_user.id, server_data)
            else:
                raise MCPConfigurationError(
                    "Invalid configuration: provide either json_config or server_config"
                )

            # Test connection in background
            background_tasks.add_task(mcp_manager.test_server_connection, db, server.id)

            logger.info(f"Successfully created MCP server {server.id} for user {current_user.id}")
            return _convert_server_to_response(server)

    except (MCPValidationError, MCPConfigurationError, MCPConnectionError) as e:
        error_handler.log_error(e, {"user_id": current_user.id, "server_name": server_data.name},
                               current_user.id, request_id)
        raise error_handler.create_http_exception(e, request_id=request_id)
    except Exception as e:
        error_handler.log_error(e, {"user_id": current_user.id, "server_name": server_data.name},
                               current_user.id, request_id)
        logger.error(f"Failed to create MCP server: {e}")
        raise error_handler.create_http_exception(e, request_id=request_id)


@router.get("", response_model=MCPServerListResponse)
async def list_mcp_servers(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    business_profile_id: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    db_service: DatabaseService = Depends(get_db_service),
    current_user: User = Depends(get_current_active_user)
):
    """List MCP servers for the current user with comprehensive filtering and validation."""
    request_id = str(uuid.uuid4())

    try:
        # Validate pagination parameters
        pagination_errors = request_validator.validate_pagination(skip, limit)
        if pagination_errors:
            error_collector = ValidationErrorCollector()
            for error in pagination_errors:
                error_collector.add_error(error)
            raise error_collector.to_exception()

        with db_service.get_session() as db:
            # Build query with filters
            query = db.query(MCPServer).filter(MCPServer.user_id == current_user.id)

            if business_profile_id:
                # Validate business profile ID format
                if not business_profile_id.strip():
                    raise MCPValidationError("Business profile ID cannot be empty")
                query = query.filter(MCPServer.business_profile_id == business_profile_id)

            if status:
                # Validate status value
                valid_statuses = ["active", "inactive", "error", "connecting"]
                if status not in valid_statuses:
                    raise MCPValidationError(f"Invalid status: {status}. Valid values: {', '.join(valid_statuses)}")
                query = query.filter(MCPServer.status == status)

            # Execute query with error handling
            total = query.count()
            servers = query.offset(skip).limit(limit).all()

            logger.info(f"Listed {len(servers)} MCP servers for user {current_user.id} (total: {total})")

            return MCPServerListResponse(
                servers=[_convert_server_to_response(server) for server in servers],
                total=total,
                page=skip // limit + 1,
                per_page=limit
            )

    except MCPValidationError as e:
        error_handler.log_error(e, {"user_id": current_user.id}, current_user.id, request_id)
        raise error_handler.create_http_exception(e, request_id=request_id)
    except Exception as e:
        error_handler.log_error(e, {"user_id": current_user.id}, current_user.id, request_id)
        raise error_handler.create_http_exception(e, request_id=request_id)


@router.get("/{server_id}", response_model=MCPServerResponse)
async def get_mcp_server(
    server_id: str,
    db_service: DatabaseService = Depends(get_db_service),
    current_user: User = Depends(get_current_active_user)
):
    """Get a specific MCP server by ID."""
    with db_service.get_session() as db:
        server = db.query(MCPServer).filter(
            MCPServer.id == server_id,
            MCPServer.user_id == current_user.id
        ).first()

        if not server:
            raise HTTPException(status_code=404, detail="MCP server not found")

        return _convert_server_to_response(server)


@router.put("/{server_id}", response_model=MCPServerResponse)
async def update_mcp_server(
    server_id: str,
    update_data: MCPServerUpdate,
    db_service: DatabaseService = Depends(get_db_service),
    current_user: User = Depends(get_current_active_user)
):
    """Update an MCP server configuration."""
    with db_service.get_session() as db:
        # Verify ownership
        server = db.query(MCPServer).filter(
            MCPServer.id == server_id,
            MCPServer.user_id == current_user.id
        ).first()

        if not server:
            raise HTTPException(status_code=404, detail="MCP server not found")

        try:
            updated_server = await mcp_manager.update_server(db, server_id, update_data)
            if not updated_server:
                raise HTTPException(status_code=404, detail="Failed to update server")

            return _convert_server_to_response(updated_server)

        except Exception as e:
            logger.error(f"Failed to update MCP server {server_id}: {e}")
            raise HTTPException(status_code=500, detail="Failed to update MCP server")


@router.delete("/{server_id}")
async def delete_mcp_server(
    server_id: str,
    db_service: DatabaseService = Depends(get_db_service),
    current_user: User = Depends(get_current_active_user)
):
    """Delete an MCP server."""
    with db_service.get_session() as db:
        server = db.query(MCPServer).filter(
            MCPServer.id == server_id,
            MCPServer.user_id == current_user.id
        ).first()

        if not server:
            raise HTTPException(status_code=404, detail="MCP server not found")

        try:
            # Stop server if running
            await mcp_manager.stop_server(db, server_id)

            # Delete from database
            db.delete(server)
            db.commit()

            return {"message": "MCP server deleted successfully"}

        except Exception as e:
            logger.error(f"Failed to delete MCP server {server_id}: {e}")
            raise HTTPException(status_code=500, detail="Failed to delete MCP server")


@router.post("/{server_id}/actions", response_model=MCPServerActionResponse)
async def perform_server_action(
    server_id: str,
    action: MCPServerAction,
    db_service: DatabaseService = Depends(get_db_service),
    current_user: User = Depends(get_current_active_user)
):
    """Perform an action on an MCP server with comprehensive validation and error handling."""
    request_id = str(uuid.uuid4())

    try:
        # Validate server ID format
        server_id_errors = request_validator.validate_server_id(server_id)
        if server_id_errors:
            error_collector = ValidationErrorCollector()
            for error in server_id_errors:
                error_collector.add_error(error, "server_id")
            raise error_collector.to_exception()

        # Validate action
        action_errors = request_validator.validate_action(action.action)
        if action_errors:
            error_collector = ValidationErrorCollector()
            for error in action_errors:
                error_collector.add_error(error, "action")
            raise error_collector.to_exception()

        with db_service.get_session() as db:
            # Find server with ownership check
            server = db.query(MCPServer).filter(
                MCPServer.id == server_id,
                MCPServer.user_id == current_user.id
            ).first()

            if not server:
                raise HTTPException(status_code=404, detail="MCP server not found")

            logger.info(f"Performing action '{action.action}' on server {server_id} for user {current_user.id}")

            # Perform action with comprehensive error handling
            success = False
            message = ""

            try:
                if action.action == "start":
                    success = await mcp_manager.start_server(db, server_id)
                    message = "Server started successfully" if success else "Failed to start server"

                elif action.action == "stop":
                    success = await mcp_manager.stop_server(db, server_id)
                    message = "Server stopped successfully" if success else "Failed to stop server"

                elif action.action == "restart":
                    await mcp_manager.stop_server(db, server_id)
                    success = await mcp_manager.start_server(db, server_id)
                    message = "Server restarted successfully" if success else "Failed to restart server"

                elif action.action == "test":
                    success, error_msg = await mcp_manager.test_server_connection(db, server_id)
                    message = "Connection test successful" if success else f"Connection test failed: {error_msg}"

                elif action.action == "discover":
                    # Start server if not running, then discover capabilities
                    if server.status != "active":
                        start_success = await mcp_manager.start_server(db, server_id)
                        if not start_success:
                            raise MCPConnectionError("Failed to start server for capability discovery")

                    # Perform discovery
                    await mcp_manager.discover_server_capabilities(db, server_id)
                    success = True
                    message = "Capabilities discovered successfully"

            except Exception as action_error:
                logger.error(f"Action {action.action} failed for server {server_id}: {action_error}")
                success = False
                message = f"Action failed: {str(action_error)}"

            response = MCPServerActionResponse(
                success=success,
                message=message,
                data={"server_id": server_id, "action": action.action, "request_id": request_id}
            )

            if success:
                logger.info(f"Action '{action.action}' completed successfully for server {server_id}")
            else:
                logger.warning(f"Action '{action.action}' failed for server {server_id}: {message}")

            return response

    except MCPValidationError as e:
        error_handler.log_error(e, {"user_id": current_user.id, "server_id": server_id, "action": action.action},
                               current_user.id, request_id)
        raise error_handler.create_http_exception(e, request_id=request_id)
    except Exception as e:
        error_handler.log_error(e, {"user_id": current_user.id, "server_id": server_id, "action": action.action},
                               current_user.id, request_id)
        raise error_handler.create_http_exception(e, request_id=request_id)


@router.get("/{server_id}/discovery", response_model=MCPDiscoveryResponse)
async def get_server_capabilities(
    server_id: str,
    db_service: DatabaseService = Depends(get_db_service),
    current_user: User = Depends(get_current_active_user)
):
    """Get discovered capabilities (tools, resources, prompts) for an MCP server."""
    with db_service.get_session() as db:
        server = db.query(MCPServer).filter(
            MCPServer.id == server_id,
            MCPServer.user_id == current_user.id
        ).first()

        if not server:
            raise HTTPException(status_code=404, detail="MCP server not found")

        # Get tools
        tools = db.query(MCPTool).filter(MCPTool.server_id == server_id).all()

        # Get resources
        resources = db.query(MCPResource).filter(MCPResource.server_id == server_id).all()

        # Get prompts
        prompts = db.query(MCPPrompt).filter(MCPPrompt.server_id == server_id).all()

        return MCPDiscoveryResponse(
            tools=[_convert_tool_to_response(tool) for tool in tools],
            resources=[_convert_resource_to_response(resource) for resource in resources],
            prompts=[_convert_prompt_to_response(prompt) for prompt in prompts],
            server_info={
                "id": server.id,
                "name": server.name,
                "status": server.status,
                "last_connected_at": server.last_connected_at.isoformat() if server.last_connected_at else None
            }
        )


@router.get("/{server_id}/status")
async def get_server_status(
    server_id: str,
    db_service: DatabaseService = Depends(get_db_service),
    current_user: User = Depends(get_current_active_user)
):
    """Get the current runtime status of an MCP server."""
    with db_service.get_session() as db:
        server = db.query(MCPServer).filter(
            MCPServer.id == server_id,
            MCPServer.user_id == current_user.id
        ).first()

        if not server:
            raise HTTPException(status_code=404, detail="MCP server not found")

        status = await mcp_manager.get_server_status(server_id)
        return status


# Input Variables endpoints
@router.post("/input-variables", response_model=MCPInputVariableResponse)
async def create_input_variable(
    variable_data: MCPInputVariableCreate,
    db_service: DatabaseService = Depends(get_db_service),
    current_user: User = Depends(get_current_active_user)
):
    """Create or update an input variable for secure credential storage."""
    with db_service.get_session() as db:
        # Check if variable already exists
        existing_var = db.query(MCPInputVariable).filter(
            MCPInputVariable.user_id == current_user.id,
            MCPInputVariable.variable_id == variable_data.variable_id
        ).first()

        if existing_var:
            # Update existing variable
            existing_var.encrypted_value = mcp_manager._encrypt_value(variable_data.value)
            existing_var.description = variable_data.description
            existing_var.is_password = variable_data.is_password
            db.commit()
            db.refresh(existing_var)
            return _convert_input_variable_to_response(existing_var)
        else:
            # Create new variable
            import uuid
            variable = MCPInputVariable(
                id=str(uuid.uuid4()),
                user_id=current_user.id,
                variable_id=variable_data.variable_id,
                variable_type=variable_data.variable_type,
                description=variable_data.description,
                is_password=variable_data.is_password,
                encrypted_value=mcp_manager._encrypt_value(variable_data.value)
            )
            db.add(variable)
            db.commit()
            db.refresh(variable)
            return _convert_input_variable_to_response(variable)


@router.get("/input-variables", response_model=List[MCPInputVariableResponse])
async def list_input_variables(
    db_service: DatabaseService = Depends(get_db_service),
    current_user: User = Depends(get_current_active_user)
):
    """List input variables for the current user."""
    with db_service.get_session() as db:
        variables = db.query(MCPInputVariable).filter(
            MCPInputVariable.user_id == current_user.id
        ).all()

        return [_convert_input_variable_to_response(var) for var in variables]


# Helper functions
def _convert_server_to_response(server: MCPServer) -> MCPServerResponse:
    """Convert database server to response model."""
    from ..models.mcp_server import MCPServerResponse, MCPConfigType, MCPTransportType, MCPServerStatus
    
    return MCPServerResponse(
        id=server.id,
        user_id=server.user_id,
        business_profile_id=server.business_profile_id,
        name=server.name,
        description=server.description,
        config_type=MCPConfigType(server.config_type),
        transport_type=MCPTransportType(server.transport_type),
        configuration=server.configuration,
        status=MCPServerStatus(server.status),
        last_connected_at=server.last_connected_at,
        error_message=server.error_message,
        created_at=server.created_at,
        updated_at=server.updated_at,
        tools=[_convert_tool_to_response(tool) for tool in server.tools],
        resources=[_convert_resource_to_response(resource) for resource in server.resources],
        prompts=[_convert_prompt_to_response(prompt) for prompt in server.prompts]
    )


def _convert_tool_to_response(tool: MCPTool):
    """Convert database tool to response model."""
    from ..models.mcp_server import MCPToolResponse
    
    return MCPToolResponse(
        id=tool.id,
        server_id=tool.server_id,
        tool_name=tool.tool_name,
        tool_description=tool.tool_description,
        parameters=tool.parameters,
        capabilities=tool.capabilities,
        is_enabled=tool.is_enabled,
        usage_count=tool.usage_count,
        last_used_at=tool.last_used_at,
        created_at=tool.created_at
    )


def _convert_resource_to_response(resource: MCPResource):
    """Convert database resource to response model."""
    from ..models.mcp_server import MCPResourceResponse
    
    return MCPResourceResponse(
        id=resource.id,
        server_id=resource.server_id,
        resource_type=resource.resource_type,
        resource_name=resource.resource_name,
        resource_description=resource.resource_description,
        parameters=resource.parameters,
        created_at=resource.created_at
    )


def _convert_prompt_to_response(prompt: MCPPrompt):
    """Convert database prompt to response model."""
    from ..models.mcp_server import MCPPromptResponse
    
    return MCPPromptResponse(
        id=prompt.id,
        server_id=prompt.server_id,
        prompt_name=prompt.prompt_name,
        prompt_description=prompt.prompt_description,
        template=prompt.template,
        parameters=prompt.parameters,
        created_at=prompt.created_at
    )


def _convert_input_variable_to_response(variable: MCPInputVariable):
    """Convert database input variable to response model."""
    from ..models.mcp_server import MCPInputVariableResponse, MCPInputVariableType
    
    return MCPInputVariableResponse(
        id=variable.id,
        user_id=variable.user_id,
        variable_id=variable.variable_id,
        variable_type=MCPInputVariableType(variable.variable_type),
        description=variable.description,
        is_password=variable.is_password,
        created_at=variable.created_at
    )
