"""
Data Access API endpoints for the Unified Data Access Tool.
Provides consistent data access across all agents and dashboard widgets.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Dict, Any
from app.dependencies import get_db_service
from app.repositories.database_service import DatabaseService
from app.auth.admin import get_current_active_user
from app.tools.unified_data_access_tool import UnifiedDataAccessTool, DataQuery, DataResult
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/data-access", tags=["data-access"])


def get_data_access_tool(db_service: DatabaseService = Depends(get_db_service)) -> UnifiedDataAccessTool:
    """Dependency to get unified data access tool."""
    with db_service.get_session() as db:
        return UnifiedDataAccessTool(db)


@router.get("/sources", response_model=List[Dict[str, Any]])
async def get_data_sources(
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    tool: UnifiedDataAccessTool = Depends(get_data_access_tool)
):
    """Get available data sources for the current user."""
    try:
        sources = await tool.get_data_sources(current_user["id"])
        return sources
    except Exception as e:
        logger.error(f"Error getting data sources: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve data sources"
        )


@router.post("/query", response_model=DataResult)
async def execute_data_query(
    query: DataQuery,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    tool: UnifiedDataAccessTool = Depends(get_data_access_tool)
):
    """Execute a data query against a data source."""
    try:
        # Verify user has access to the data source
        user_sources = await tool.get_data_sources(current_user["id"])
        source_ids = [source["id"] for source in user_sources]
        
        if query.source_id not in source_ids:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to data source"
            )
        
        result = await tool.execute_query(query)
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error executing data query: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to execute data query"
        )


@router.get("/sources/{source_id}/test")
async def test_data_source_connection(
    source_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    tool: UnifiedDataAccessTool = Depends(get_data_access_tool)
):
    """Test connection to a data source."""
    try:
        # Verify user has access to the data source
        user_sources = await tool.get_data_sources(current_user["id"])
        source_ids = [source["id"] for source in user_sources]
        
        if source_id not in source_ids:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to data source"
            )
        
        result = await tool.test_connection(source_id)
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing data source connection: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to test data source connection"
        )


@router.get("/sources/{source_id}/schema")
async def get_data_source_schema(
    source_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    tool: UnifiedDataAccessTool = Depends(get_data_access_tool)
):
    """Get schema information for a data source."""
    try:
        # Verify user has access to the data source
        user_sources = await tool.get_data_sources(current_user["id"])
        source_ids = [source["id"] for source in user_sources]
        
        if source_id not in source_ids:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to data source"
            )
        
        schema_info = await tool.get_schema_info(source_id)
        return schema_info
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting data source schema: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get data source schema"
        )


@router.get("/sources/{source_id}/sample")
async def get_sample_data(
    source_id: str,
    limit: int = 10,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    tool: UnifiedDataAccessTool = Depends(get_data_access_tool)
):
    """Get sample data from a data source."""
    try:
        # Verify user has access to the data source
        user_sources = await tool.get_data_sources(current_user["id"])
        source_ids = [source["id"] for source in user_sources]
        
        if source_id not in source_ids:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to data source"
            )
        
        # Validate limit
        if limit < 1 or limit > 100:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Limit must be between 1 and 100"
            )
        
        result = await tool.get_sample_data(source_id, limit)
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting sample data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get sample data"
        )


@router.get("/sources/{source_id}/summary")
async def get_data_summary(
    source_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    tool: UnifiedDataAccessTool = Depends(get_data_access_tool)
):
    """Get summary statistics for a data source."""
    try:
        # Verify user has access to the data source
        user_sources = await tool.get_data_sources(current_user["id"])
        source_ids = [source["id"] for source in user_sources]
        
        if source_id not in source_ids:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to data source"
            )
        
        summary = await tool.get_data_summary(source_id)
        return summary
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting data summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get data summary"
        )


@router.post("/cache/clear")
async def clear_cache(
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    tool: UnifiedDataAccessTool = Depends(get_data_access_tool)
):
    """Clear the data access cache."""
    try:
        tool.clear_cache()
        return {"message": "Cache cleared successfully"}
    except Exception as e:
        logger.error(f"Error clearing cache: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clear cache"
        )


@router.get("/query-builder/templates")
async def get_query_templates():
    """Get predefined query templates for different data source types."""
    templates = {
        "file": {
            "filter_example": {
                "description": "Filter data by column values",
                "query_type": "filter",
                "query": '{"column_name": "value", "numeric_column": {"gt": 100, "lt": 200}}',
                "example": "Filter rows where column_name equals 'value' and numeric_column is between 100 and 200"
            },
            "search_example": {
                "description": "Search across all text columns",
                "query_type": "search",
                "query": "search_term",
                "example": "Search for 'search_term' across all text columns"
            },
            "aggregate_example": {
                "description": "Group and aggregate data",
                "query_type": "aggregate",
                "query": '{"group_by": ["category"], "aggregations": {"amount": "sum", "count": "count"}}',
                "example": "Group by category and sum amounts with count"
            }
        },
        "database": {
            "sql_example": {
                "description": "Execute SQL query",
                "query_type": "sql",
                "query": "SELECT * FROM table_name WHERE condition = ?",
                "parameters": {"condition": "value"},
                "example": "Execute parameterized SQL query"
            }
        }
    }
    return templates


@router.get("/health")
async def health_check():
    """Health check endpoint for the data access service."""
    return {
        "status": "healthy",
        "service": "unified-data-access",
        "version": "1.0.0"
    }
