"""
Message Repository Implementation.

Provides specialized repository operations for Message entities,
replacing the message-related CRUD functions from database.py.
"""

import uuid
import logging
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func

from .base_repository import BaseRepository, RepositoryError
from ..models.database_models import Message
from ..models.schemas import MessageCreate, MessageUpdate, MessageResponse
from ..errors.correlation_context import get_correlation_logger

logger = get_correlation_logger(__name__)


class MessageRepository(BaseRepository[Message]):
    """Repository for Message entity operations."""
    
    def __init__(self, session: Session):
        super().__init__(session, Message)
        self.logger = logger
    
    def create_message(
        self,
        conversation_id: str,
        sender: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
        parent_message_id: Optional[str] = None,
        thread_id: Optional[str] = None
    ) -> Message:
        """
        Create a new message with hierarchical threading support.
        
        Args:
            conversation_id: ID of the conversation
            sender: Message sender identifier
            content: Message content
            metadata: Optional message metadata
            parent_message_id: Optional parent message ID for threading
            thread_id: Optional thread ID
            
        Returns:
            Created message instance
            
        Raises:
            RepositoryError: If message creation fails
        """
        try:
            # Sanitize metadata to ensure it's JSON serializable
            sanitized_metadata = self._sanitize_json(metadata)
            
            # Generate hierarchical ID
            message_id = self._generate_hierarchical_message_id(conversation_id, parent_message_id)
            
            # Get next sequence numbers
            message_sequence = self._get_next_message_sequence(conversation_id)
            thread_sequence = 0
            
            if parent_message_id:
                thread_sequence = self._get_next_thread_sequence(parent_message_id)
                # If no thread_id provided, use parent's thread_id or parent's ID
                if not thread_id:
                    parent_message = self.get_by_id(parent_message_id)
                    if parent_message:
                        thread_id = parent_message.thread_id or parent_message_id
            
            # Create message instance
            message_data = {
                'id': message_id,
                'conversation_id': conversation_id,
                'sender': sender,
                'content': content,
                'message_metadata': sanitized_metadata,
                'parent_message_id': parent_message_id,
                'thread_id': thread_id,
                'message_sequence': message_sequence,
                'thread_sequence': thread_sequence
            }
            
            message = Message(**message_data)
            
            self.session.add(message)
            self.session.commit()
            self.session.refresh(message)
            
            self.logger.info(f"Created message {message_id} in conversation {conversation_id}")
            return message
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to create message: {e}")
            raise RepositoryError(
                f"Failed to create message: {str(e)}",
                entity_type="Message",
                operation="create"
            )
    
    def get_conversation_messages(
        self,
        conversation_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Message]:
        """
        Get all messages for a conversation ordered by sequence.
        
        Args:
            conversation_id: ID of the conversation
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of messages in the conversation
        """
        try:
            return self.session.query(Message).filter(
                Message.conversation_id == conversation_id
            ).order_by(Message.message_sequence.asc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get conversation messages: {e}")
            raise RepositoryError(
                f"Failed to get conversation messages: {str(e)}",
                entity_type="Message",
                operation="get_conversation_messages"
            )
    
    def get_message_thread(self, thread_id: str) -> List[Message]:
        """
        Get all messages in a thread ordered by thread sequence.
        
        Args:
            thread_id: ID of the thread
            
        Returns:
            List of messages in the thread
        """
        try:
            return self.session.query(Message).filter(
                Message.thread_id == thread_id
            ).order_by(Message.thread_sequence.asc()).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get message thread: {e}")
            raise RepositoryError(
                f"Failed to get message thread: {str(e)}",
                entity_type="Message",
                operation="get_message_thread"
            )
    
    def get_message_children(self, parent_message_id: str) -> List[Message]:
        """
        Get all direct child messages of a parent message.
        
        Args:
            parent_message_id: ID of the parent message
            
        Returns:
            List of child messages
        """
        try:
            return self.session.query(Message).filter(
                Message.parent_message_id == parent_message_id
            ).order_by(Message.thread_sequence.asc()).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get message children: {e}")
            raise RepositoryError(
                f"Failed to get message children: {str(e)}",
                entity_type="Message",
                operation="get_message_children"
            )
    
    def create_message_edit(
        self,
        original_message_id: str,
        new_content: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Message:
        """
        Create an edited version of a message.
        
        Args:
            original_message_id: ID of the original message
            new_content: New content for the message
            metadata: Optional metadata for the edit
            
        Returns:
            Created edited message
            
        Raises:
            RepositoryError: If original message not found or edit creation fails
        """
        try:
            original_message = self.get_by_id(original_message_id)
            if not original_message:
                raise RepositoryError(
                    f"Original message {original_message_id} not found",
                    entity_type="Message",
                    operation="create_message_edit"
                )
            
            # Sanitize metadata
            sanitized_metadata = self._sanitize_json(metadata or {})
            
            # Create the edited message as a child of the original
            edited_message = self.create_message(
                conversation_id=original_message.conversation_id,
                sender=original_message.sender,
                content=new_content,
                metadata=sanitized_metadata,
                parent_message_id=original_message_id,
                thread_id=original_message.thread_id
            )
            
            # Mark as edit in metadata
            if not edited_message.message_metadata:
                edited_message.message_metadata = {}
            edited_message.message_metadata['is_edit'] = True
            edited_message.message_metadata['original_message_id'] = original_message_id
            
            self.session.commit()
            self.session.refresh(edited_message)
            
            self.logger.info(f"Created edit {edited_message.id} for message {original_message_id}")
            return edited_message
            
        except RepositoryError:
            raise
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to create message edit: {e}")
            raise RepositoryError(
                f"Failed to create message edit: {str(e)}",
                entity_type="Message",
                operation="create_message_edit"
            )
    
    def get_message_edit_history(self, original_message_id: str) -> List[Message]:
        """
        Get the edit history for a message.
        
        Args:
            original_message_id: ID of the original message
            
        Returns:
            List of message edits ordered by creation time
        """
        try:
            return self.session.query(Message).filter(
                Message.message_metadata.op('->>')('original_message_id') == original_message_id
            ).order_by(Message.created_at.asc()).all()
            
        except Exception as e:
            self.logger.error(f"Failed to get message edit history: {e}")
            raise RepositoryError(
                f"Failed to get message edit history: {str(e)}",
                entity_type="Message",
                operation="get_message_edit_history"
            )
    
    def update_message_content(
        self,
        message_id: str,
        content: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[Message]:
        """
        Update a message's content and metadata.
        
        Args:
            message_id: ID of the message to update
            content: New content (optional)
            metadata: New metadata (optional)
            
        Returns:
            Updated message or None if not found
        """
        try:
            message = self.get_by_id(message_id)
            if not message:
                self.logger.warning(f"Message {message_id} not found for update")
                return None
            
            # Update fields if provided
            if content is not None:
                message.content = content
                self.logger.debug(f"Updated content for message {message_id}")
            
            if metadata is not None:
                # Merge with existing metadata
                existing_metadata = message.message_metadata or {}
                existing_metadata.update(self._sanitize_json(metadata))
                message.message_metadata = existing_metadata
                self.logger.debug(f"Updated metadata for message {message_id}")
            
            self.session.commit()
            self.session.refresh(message)
            
            self.logger.info(f"Successfully updated message {message_id}")
            return message
            
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"Failed to update message {message_id}: {e}")
            raise RepositoryError(
                f"Failed to update message: {str(e)}",
                entity_type="Message",
                operation="update"
            )

    # Helper methods

    def _sanitize_json(self, data: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Sanitize data to ensure it's JSON serializable."""
        if data is None:
            return None

        def sanitize_value(value):
            if isinstance(value, dict):
                return {k: sanitize_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [sanitize_value(item) for item in value]
            elif isinstance(value, (str, int, float, bool)) or value is None:
                return value
            else:
                return str(value)

        return sanitize_value(data)

    def _generate_hierarchical_message_id(
        self,
        conversation_id: str,
        parent_message_id: Optional[str] = None
    ) -> str:
        """Generate a hierarchical message ID."""
        if parent_message_id:
            # For child messages, append a UUID to the parent ID
            return f"{parent_message_id}.{str(uuid.uuid4())[:8]}"
        else:
            # For root messages, use conversation ID prefix
            return f"{conversation_id}.{str(uuid.uuid4())[:8]}"

    def _get_next_message_sequence(self, conversation_id: str) -> int:
        """Get the next message sequence number for a conversation."""
        try:
            max_sequence = self.session.query(func.max(Message.message_sequence)).filter(
                Message.conversation_id == conversation_id
            ).scalar()
            return (max_sequence or 0) + 1
        except Exception as e:
            self.logger.error(f"Error getting next message sequence: {e}")
            return 1

    def _get_next_thread_sequence(self, parent_message_id: str) -> int:
        """Get the next thread sequence number for a parent message."""
        try:
            max_sequence = self.session.query(func.max(Message.thread_sequence)).filter(
                Message.parent_message_id == parent_message_id
            ).scalar()
            return (max_sequence or 0) + 1
        except Exception as e:
            self.logger.error(f"Error getting next thread sequence: {e}")
            return 1

    # Convenience methods to match database.py function signatures
    def get_message(self, message_id: str) -> Optional[Message]:
        """Get a message by ID. Convenience method to match database.py signature."""
        return self.get_by_id(message_id)
