"""
Correlation Context Management.

Provides correlation ID tracking across request boundaries for distributed tracing
and error correlation.
"""

import uuid
import logging
import async<PERSON>
from contextvars import Context<PERSON><PERSON>
from typing import Optional, Dict, Any
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

# Context variable for correlation ID
_correlation_id: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)

# Context variable for request context
_request_context: ContextVar[Dict[str, Any]] = ContextVar('request_context', default={})


class CorrelationContext:
    """
    Context manager for correlation ID and request context management.
    
    Provides automatic correlation ID generation and context propagation
    across async boundaries.
    """
    
    def __init__(
        self,
        correlation_id: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        operation: Optional[str] = None,
        additional_context: Optional[Dict[str, Any]] = None
    ):
        self.correlation_id = correlation_id or str(uuid.uuid4())
        self.user_id = user_id
        self.session_id = session_id
        self.request_id = request_id or str(uuid.uuid4())
        self.operation = operation
        self.additional_context = additional_context or {}
        
        # Store previous context for restoration
        self._previous_correlation_id = None
        self._previous_request_context = None
        
        # Build request context
        self.request_context = {
            "correlation_id": self.correlation_id,
            "request_id": self.request_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "user_id": self.user_id,
            "session_id": self.session_id,
            "operation": self.operation,
            **self.additional_context
        }
    
    def __enter__(self):
        """Enter the correlation context."""
        # Store previous values
        self._previous_correlation_id = _correlation_id.get(None)
        self._previous_request_context = _request_context.get({})
        
        # Set new values
        _correlation_id.set(self.correlation_id)
        _request_context.set(self.request_context)
        
        logger.debug(f"Entered correlation context: {self.correlation_id}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the correlation context."""
        # Restore previous values
        _correlation_id.set(self._previous_correlation_id)
        _request_context.set(self._previous_request_context)
        
        logger.debug(f"Exited correlation context: {self.correlation_id}")
    
    async def __aenter__(self):
        """Async enter the correlation context."""
        return self.__enter__()
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async exit the correlation context."""
        return self.__exit__(exc_type, exc_val, exc_tb)
    
    def update_context(self, **kwargs):
        """Update the request context with additional information."""
        self.request_context.update(kwargs)
        _request_context.set(self.request_context)
        logger.debug(f"Updated correlation context: {self.correlation_id}")
    
    def get_context(self) -> Dict[str, Any]:
        """Get the current request context."""
        return self.request_context.copy()
    
    @classmethod
    def from_headers(cls, headers: Dict[str, str]) -> 'CorrelationContext':
        """Create correlation context from HTTP headers."""
        correlation_id = headers.get('X-Correlation-ID')
        request_id = headers.get('X-Request-ID')
        user_id = headers.get('X-User-ID')
        session_id = headers.get('X-Session-ID')
        
        return cls(
            correlation_id=correlation_id,
            request_id=request_id,
            user_id=user_id,
            session_id=session_id
        )
    
    def to_headers(self) -> Dict[str, str]:
        """Convert context to HTTP headers."""
        headers = {
            'X-Correlation-ID': self.correlation_id,
            'X-Request-ID': self.request_id
        }
        
        if self.user_id:
            headers['X-User-ID'] = self.user_id
        
        if self.session_id:
            headers['X-Session-ID'] = self.session_id
        
        return headers


def get_correlation_id() -> str:
    """
    Get the current correlation ID.
    
    Returns:
        Current correlation ID or generates a new one if none exists
    """
    correlation_id = _correlation_id.get()
    if correlation_id is None:
        correlation_id = str(uuid.uuid4())
        _correlation_id.set(correlation_id)
        logger.debug(f"Generated new correlation ID: {correlation_id}")
    
    return correlation_id


def set_correlation_id(correlation_id: str):
    """
    Set the correlation ID for the current context.
    
    Args:
        correlation_id: The correlation ID to set
    """
    _correlation_id.set(correlation_id)
    logger.debug(f"Set correlation ID: {correlation_id}")


def get_request_context() -> Dict[str, Any]:
    """
    Get the current request context.
    
    Returns:
        Current request context dictionary
    """
    return _request_context.get({}).copy()


def update_request_context(**kwargs):
    """
    Update the current request context.
    
    Args:
        **kwargs: Key-value pairs to add to the context
    """
    current_context = _request_context.get({})
    current_context.update(kwargs)
    _request_context.set(current_context)
    logger.debug(f"Updated request context: {kwargs}")


def clear_context():
    """Clear the current correlation context."""
    _correlation_id.set(None)
    _request_context.set({})
    logger.debug("Cleared correlation context")


class CorrelationLoggerAdapter(logging.LoggerAdapter):
    """
    Logger adapter that automatically includes correlation information.
    
    Enhances log records with correlation ID and request context.
    """
    
    def process(self, msg, kwargs):
        """Process log record to include correlation information."""
        correlation_id = get_correlation_id()
        request_context = get_request_context()
        
        # Add correlation info to extra
        extra = kwargs.get('extra', {})
        extra.update({
            'correlation_id': correlation_id,
            'request_id': request_context.get('request_id'),
            'user_id': request_context.get('user_id'),
            'session_id': request_context.get('session_id'),
            'operation': request_context.get('operation')
        })
        kwargs['extra'] = extra
        
        # Prepend correlation ID to message
        msg = f"[{correlation_id[:8]}] {msg}"
        
        return msg, kwargs


def get_correlation_logger(name: str) -> CorrelationLoggerAdapter:
    """
    Get a logger adapter with correlation information.
    
    Args:
        name: Logger name
        
    Returns:
        Logger adapter with correlation support
    """
    logger = logging.getLogger(name)
    return CorrelationLoggerAdapter(logger, {})


# Middleware helper functions for FastAPI integration

def extract_correlation_from_request(request) -> str:
    """
    Extract correlation ID from FastAPI request.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Correlation ID from headers or generates new one
    """
    correlation_id = request.headers.get('X-Correlation-ID')
    if not correlation_id:
        correlation_id = str(uuid.uuid4())
    
    return correlation_id


def create_correlation_context_from_request(request, operation: Optional[str] = None) -> CorrelationContext:
    """
    Create correlation context from FastAPI request.
    
    Args:
        request: FastAPI request object
        operation: Optional operation name
        
    Returns:
        Correlation context instance
    """
    headers = dict(request.headers)
    context = CorrelationContext.from_headers(headers)
    
    if operation:
        context.operation = operation
        context.request_context['operation'] = operation
    
    # Add request-specific information
    context.update_context(
        method=request.method,
        url=str(request.url),
        client_host=request.client.host if request.client else None,
        user_agent=request.headers.get('User-Agent')
    )
    
    return context


# Decorator for automatic correlation context management

def with_correlation_context(operation: Optional[str] = None):
    """
    Decorator to automatically manage correlation context for functions.
    
    Args:
        operation: Optional operation name for context
    """
    def decorator(func):
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                correlation_id = get_correlation_id()
                with CorrelationContext(correlation_id=correlation_id, operation=operation):
                    return await func(*args, **kwargs)
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                correlation_id = get_correlation_id()
                with CorrelationContext(correlation_id=correlation_id, operation=operation):
                    return func(*args, **kwargs)
            return sync_wrapper
    
    return decorator


# FastAPI Middleware for Correlation ID
class CorrelationMiddleware:
    """FastAPI middleware that manages correlation IDs for request tracking."""

    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        """Process requests through the correlation middleware."""
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return

        # Extract correlation ID from headers or generate new one
        headers = dict(scope.get("headers", []))
        correlation_id = None

        # Look for existing correlation ID in headers
        for header_name, header_value in headers.items():
            if header_name.decode().lower() == "x-correlation-id":
                correlation_id = header_value.decode()
                break

        # Generate new correlation ID if not provided
        if not correlation_id:
            correlation_id = CorrelationContext.generate_correlation_id()

        # Set correlation ID in context
        set_correlation_id(correlation_id)

        # Create a wrapper for send to add correlation ID to response headers
        async def send_wrapper(message):
            if message["type"] == "http.response.start":
                # Add correlation ID to response headers
                headers = list(message.get("headers", []))
                headers.append([b"x-correlation-id", correlation_id.encode()])
                message["headers"] = headers
            await send(message)

        try:
            await self.app(scope, receive, send_wrapper)
        finally:
            # Clear correlation ID after request
            clear_context()
