"""
Comprehensive tests for the LangGraph template system.

This module tests the workflow template system including base templates,
template manager, and all core template implementations.
"""

import pytest
import asyncio
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

from ..templates.base_template import (
    WorkflowTemplate, TemplateParameter, TemplateMetadata, ParameterType
)
from ..templates.template_manager import Temp<PERSON><PERSON>ana<PERSON>, TemplateRegistry, TemplateCache
from ..templates.simple_analysis_template import SimpleAnalysisTemplate
from ..templates.business_analysis_template import BusinessAnalysisTemplate
from ..templates.dashboard_generation_template import DashboardGenerationTemplate


class TestTemplateParameter:
    """Test template parameter validation."""
    
    def test_string_parameter_validation(self):
        """Test string parameter validation."""
        param = TemplateParameter(
            name="test_param",
            parameter_type=ParameterType.STRING,
            required=True,
            validation_rules={"min_length": 3, "max_length": 10}
        )
        
        # Valid string
        assert param.validate("hello") is True
        
        # Too short
        assert param.validate("hi") is False
        
        # Too long
        assert param.validate("this is too long") is False
        
        # Wrong type
        assert param.validate(123) is False
        
        # Required but None
        assert param.validate(None) is False
    
    def test_integer_parameter_validation(self):
        """Test integer parameter validation."""
        param = TemplateParameter(
            name="count",
            parameter_type=ParameterType.INTEGER,
            required=True,
            validation_rules={"min_value": 1, "max_value": 100}
        )
        
        # Valid integer
        assert param.validate(50) is True
        
        # Too small
        assert param.validate(0) is False
        
        # Too large
        assert param.validate(101) is False
        
        # Wrong type
        assert param.validate("50") is False
    
    def test_allowed_values_validation(self):
        """Test allowed values validation."""
        param = TemplateParameter(
            name="category",
            parameter_type=ParameterType.STRING,
            required=True,
            validation_rules={"allowed_values": ["A", "B", "C"]}
        )
        
        # Valid value
        assert param.validate("A") is True
        
        # Invalid value
        assert param.validate("D") is False
    
    def test_optional_parameter_with_default(self):
        """Test optional parameter with default value."""
        param = TemplateParameter(
            name="optional_param",
            parameter_type=ParameterType.STRING,
            required=False,
            default_value="default"
        )
        
        # None should be valid for optional parameter
        assert param.validate(None) is True
        
        # Valid value
        assert param.validate("custom") is True


class TestTemplateMetadata:
    """Test template metadata functionality."""
    
    def test_metadata_creation(self):
        """Test metadata creation and serialization."""
        metadata = TemplateMetadata(
            name="test_template",
            version="1.0.0",
            description="Test template",
            tags=["test", "example"],
            category="testing"
        )
        
        assert metadata.name == "test_template"
        assert metadata.version == "1.0.0"
        assert "test" in metadata.tags
        
        # Test serialization
        metadata_dict = metadata.to_dict()
        assert metadata_dict["name"] == "test_template"
        assert metadata_dict["version"] == "1.0.0"
        assert isinstance(metadata_dict["created_at"], str)


class MockWorkflowTemplate(WorkflowTemplate):
    """Mock template for testing base functionality."""
    
    def _initialize_parameters(self):
        self.add_parameter(TemplateParameter(
            name="required_param",
            parameter_type=ParameterType.STRING,
            required=True
        ))
        self.add_parameter(TemplateParameter(
            name="optional_param",
            parameter_type=ParameterType.STRING,
            required=False,
            default_value="default"
        ))
    
    async def create_workflow(self, params):
        # Mock workflow creation
        from langgraph import StateGraph, END
        from ..states.agent_state import DatageniusAgentState
        
        workflow = StateGraph(DatageniusAgentState)
        workflow.add_node("mock_node", lambda state: state)
        workflow.set_entry_point("mock_node")
        workflow.add_edge("mock_node", END)
        
        compiled = workflow.compile()
        compiled._template_params = params
        return compiled


class TestWorkflowTemplate:
    """Test base workflow template functionality."""
    
    @pytest.fixture
    def mock_template(self):
        """Create mock template for testing."""
        return MockWorkflowTemplate("mock_template", "1.0.0")
    
    def test_template_initialization(self, mock_template):
        """Test template initialization."""
        assert mock_template.metadata.name == "mock_template"
        assert mock_template.metadata.version == "1.0.0"
        assert len(mock_template.parameters) == 2
        assert "required_param" in mock_template.parameters
        assert "optional_param" in mock_template.parameters
    
    def test_parameter_validation_success(self, mock_template):
        """Test successful parameter validation."""
        params = {
            "required_param": "test_value",
            "optional_param": "custom_value"
        }
        
        is_valid, errors = mock_template.validate_parameters(params)
        assert is_valid is True
        assert len(errors) == 0
    
    def test_parameter_validation_missing_required(self, mock_template):
        """Test validation with missing required parameter."""
        params = {
            "optional_param": "custom_value"
        }
        
        is_valid, errors = mock_template.validate_parameters(params)
        assert is_valid is False
        assert len(errors) == 1
        assert "required_param" in errors[0]
    
    def test_parameter_validation_unexpected_param(self, mock_template):
        """Test validation with unexpected parameter."""
        params = {
            "required_param": "test_value",
            "unexpected_param": "value"
        }
        
        is_valid, errors = mock_template.validate_parameters(params)
        assert is_valid is False
        assert len(errors) == 1
        assert "unexpected_param" in errors[0]
    
    def test_parameter_schema_generation(self, mock_template):
        """Test parameter schema generation."""
        schema = mock_template.get_parameter_schema()
        
        assert schema["template_name"] == "mock_template"
        assert schema["version"] == "1.0.0"
        assert "parameters" in schema
        assert "required_param" in schema["parameters"]
        assert "optional_param" in schema["parameters"]
        
        required_param = schema["parameters"]["required_param"]
        assert required_param["required"] is True
        assert required_param["type"] == "string"
    
    @pytest.mark.asyncio
    async def test_workflow_creation_with_validation(self, mock_template):
        """Test workflow creation with parameter validation."""
        params = {
            "required_param": "test_value"
        }
        
        # Mock event bus to avoid actual event publishing
        with patch('backend.agents.langgraph.templates.base_template.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            workflow = await mock_template.create_workflow_with_validation(params)
            
            assert workflow is not None
            assert hasattr(workflow, '_template_params')
            
            # Verify event was published
            mock_event_bus.publish.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_workflow_creation_validation_failure(self, mock_template):
        """Test workflow creation with validation failure."""
        params = {}  # Missing required parameter
        
        with pytest.raises(ValueError) as exc_info:
            await mock_template.create_workflow_with_validation(params)
        
        assert "Parameter validation failed" in str(exc_info.value)
    
    def test_cache_key_generation(self, mock_template):
        """Test cache key generation."""
        params1 = {"required_param": "value1", "optional_param": "value2"}
        params2 = {"required_param": "value1", "optional_param": "value2"}
        params3 = {"required_param": "value3", "optional_param": "value2"}
        
        key1 = mock_template.get_cache_key(params1)
        key2 = mock_template.get_cache_key(params2)
        key3 = mock_template.get_cache_key(params3)
        
        # Same parameters should generate same key
        assert key1 == key2
        
        # Different parameters should generate different keys
        assert key1 != key3


class TestTemplateRegistry:
    """Test template registry functionality."""
    
    @pytest.fixture
    def registry(self):
        """Create fresh registry for each test."""
        return TemplateRegistry()
    
    def test_template_registration(self, registry):
        """Test template registration."""
        registry.register(MockWorkflowTemplate, "mock_template", "1.0.0")
        
        # Test retrieval
        template_class = registry.get_template_class("mock_template", "1.0.0")
        assert template_class == MockWorkflowTemplate
        
        # Test default version
        template_class = registry.get_template_class("mock_template")
        assert template_class == MockWorkflowTemplate
    
    def test_multiple_versions(self, registry):
        """Test multiple template versions."""
        registry.register(MockWorkflowTemplate, "mock_template", "1.0.0")
        registry.register(MockWorkflowTemplate, "mock_template", "2.0.0", set_as_default=True)
        
        # Test specific version retrieval
        v1_class = registry.get_template_class("mock_template", "1.0.0")
        v2_class = registry.get_template_class("mock_template", "2.0.0")
        assert v1_class == MockWorkflowTemplate
        assert v2_class == MockWorkflowTemplate
        
        # Test default version (should be 2.0.0)
        default_class = registry.get_template_class("mock_template")
        assert default_class == MockWorkflowTemplate
    
    def test_list_templates(self, registry):
        """Test template listing."""
        registry.register(MockWorkflowTemplate, "mock_template", "1.0.0")
        
        templates = registry.list_templates()
        assert len(templates) == 1
        assert templates[0]["name"] == "mock_template"
        assert templates[0]["version"] == "1.0.0"
        assert templates[0]["is_default"] is True
    
    def test_get_template_versions(self, registry):
        """Test getting template versions."""
        registry.register(MockWorkflowTemplate, "mock_template", "1.0.0")
        registry.register(MockWorkflowTemplate, "mock_template", "2.0.0")
        
        versions = registry.get_template_versions("mock_template")
        assert "1.0.0" in versions
        assert "2.0.0" in versions
        assert len(versions) == 2


class TestTemplateCache:
    """Test template cache functionality."""
    
    @pytest.fixture
    def cache(self):
        """Create fresh cache for each test."""
        return TemplateCache(default_ttl=3600)
    
    def test_cache_set_and_get(self, cache):
        """Test cache set and get operations."""
        mock_workflow = MagicMock()
        cache_key = "test_key"
        
        # Set cache
        cache.set(cache_key, mock_workflow)
        
        # Get from cache
        retrieved = cache.get(cache_key)
        assert retrieved == mock_workflow
    
    def test_cache_miss(self, cache):
        """Test cache miss."""
        result = cache.get("nonexistent_key")
        assert result is None
    
    def test_cache_expiration(self, cache):
        """Test cache expiration."""
        mock_workflow = MagicMock()
        cache_key = "test_key"
        
        # Set cache with very short TTL
        cache.set(cache_key, mock_workflow, ttl=0)
        
        # Should be expired immediately
        result = cache.get(cache_key)
        assert result is None
    
    def test_cache_stats(self, cache):
        """Test cache statistics."""
        mock_workflow = MagicMock()
        
        # Add some entries
        cache.set("key1", mock_workflow)
        cache.set("key2", mock_workflow)
        
        # Access one entry
        cache.get("key1")
        
        stats = cache.get_stats()
        assert stats["total_entries"] == 2
        assert stats["total_access_count"] == 1


class TestTemplateManager:
    """Test template manager functionality."""
    
    @pytest.fixture
    def manager(self):
        """Create fresh manager for each test."""
        return TemplateManager(enable_caching=True, cache_ttl=3600)
    
    def test_template_registration(self, manager):
        """Test template registration through manager."""
        manager.register_template(MockWorkflowTemplate, "mock_template", "1.0.0")
        
        # Test that template is registered
        templates = manager.list_templates()
        assert len(templates) >= 1
        
        template_names = [t["name"] for t in templates]
        assert "mock_template" in template_names
    
    @pytest.mark.asyncio
    async def test_workflow_creation(self, manager):
        """Test workflow creation through manager."""
        manager.register_template(MockWorkflowTemplate, "mock_template", "1.0.0")
        
        params = {"required_param": "test_value"}
        
        # Mock event bus
        with patch('backend.agents.langgraph.templates.base_template.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            workflow = await manager.create_workflow("mock_template", params)
            
            assert workflow is not None
            assert hasattr(workflow, '_template_params')
    
    @pytest.mark.asyncio
    async def test_workflow_caching(self, manager):
        """Test workflow caching."""
        manager.register_template(MockWorkflowTemplate, "mock_template", "1.0.0")
        
        params = {"required_param": "test_value"}
        
        # Mock event bus
        with patch('backend.agents.langgraph.templates.base_template.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            # Create workflow twice with same parameters
            workflow1 = await manager.create_workflow("mock_template", params, use_cache=True)
            workflow2 = await manager.create_workflow("mock_template", params, use_cache=True)
            
            # Second call should be from cache
            metrics = manager.get_metrics()
            assert metrics["cache_hits"] >= 1
    
    def test_template_schema_retrieval(self, manager):
        """Test template schema retrieval."""
        manager.register_template(MockWorkflowTemplate, "mock_template", "1.0.0")
        
        schema = manager.get_template_schema("mock_template")
        
        assert schema["template_name"] == "mock_template"
        assert "parameters" in schema
        assert "required_param" in schema["parameters"]
    
    @pytest.mark.asyncio
    async def test_parameter_validation_only(self, manager):
        """Test parameter validation without workflow creation."""
        manager.register_template(MockWorkflowTemplate, "mock_template", "1.0.0")
        
        # Valid parameters
        valid_params = {"required_param": "test_value"}
        is_valid, errors = await manager.validate_template_parameters("mock_template", valid_params)
        assert is_valid is True
        assert len(errors) == 0
        
        # Invalid parameters
        invalid_params = {}
        is_valid, errors = await manager.validate_template_parameters("mock_template", invalid_params)
        assert is_valid is False
        assert len(errors) > 0
    
    def test_metrics_collection(self, manager):
        """Test metrics collection."""
        metrics = manager.get_metrics()
        
        assert "templates_created" in metrics
        assert "cache_hits" in metrics
        assert "cache_misses" in metrics
        assert "creation_errors" in metrics
        assert "cache_enabled" in metrics
