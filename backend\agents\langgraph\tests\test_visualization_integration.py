"""
Tests for Visualization Integration with Event System.

This module tests the integration between the visualization components
and the existing event-driven architecture.
"""

import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock

from ..visualization.workflow_visualizer import WorkflowVisualizer
from ..events.event_bus import LangGraphEventBus, LangGraphEvent, EventPriority
from ..events.types import WorkflowCompletedEvent, DashboardUpdateEvent
from ..monitoring.metrics import WorkflowMetrics


class TestVisualizationIntegration:
    """Test suite for visualization integration with event system."""

    @pytest.fixture
    def visualizer(self):
        """Create a WorkflowVisualizer instance for testing."""
        return WorkflowVisualizer()

    @pytest.fixture
    def event_bus(self):
        """Create an event bus instance for testing."""
        return LangGraphEventBus()

    @pytest.fixture
    def metrics_system(self):
        """Create a metrics system instance for testing."""
        return WorkflowMetrics(database_url="sqlite:///:memory:")

    @pytest.fixture
    def sample_workflow_event(self):
        """Sample workflow completion event."""
        return WorkflowCompletedEvent(
            workflow_id="test-workflow-123",
            execution_time=5.5,
            success=True,
            results={
                "agent_history": ["concierge", "analysis", "marketing"],
                "execution_timeline": [
                    {
                        "agent_id": "concierge",
                        "start_time": "2024-01-01T10:00:00Z",
                        "end_time": "2024-01-01T10:00:02Z",
                        "status": "completed"
                    },
                    {
                        "agent_id": "analysis", 
                        "start_time": "2024-01-01T10:00:02Z",
                        "end_time": "2024-01-01T10:00:07Z",
                        "status": "completed"
                    },
                    {
                        "agent_id": "marketing",
                        "start_time": "2024-01-01T10:00:07Z", 
                        "end_time": "2024-01-01T10:00:10Z",
                        "status": "completed"
                    }
                ]
            }
        )

    @pytest.mark.asyncio
    async def test_event_driven_visualization_update(self, visualizer, event_bus, sample_workflow_event):
        """Test that visualization updates are triggered by workflow events."""
        visualization_updates = []
        
        async def visualization_handler(event: LangGraphEvent):
            """Handler that captures visualization updates."""
            if event.event_type == "workflow.completed":
                # Generate visualization based on event data
                timeline_data = event.data.get("results", {}).get("execution_timeline", [])
                if timeline_data:
                    diagram = visualizer.generate_execution_timeline(
                        timeline_data,
                        title=f"Workflow {event.data['workflow_id']} Timeline"
                    )
                    visualization_updates.append({
                        "workflow_id": event.data["workflow_id"],
                        "diagram_type": "timeline",
                        "diagram": diagram,
                        "timestamp": event.timestamp
                    })

        # Subscribe to workflow completion events
        event_bus.subscribe("workflow.completed", visualization_handler)
        
        # Start event bus
        await event_bus.start()
        
        # Publish workflow completion event
        await event_bus.publish(sample_workflow_event)
        
        # Wait for event processing
        await asyncio.sleep(0.1)
        
        # Verify visualization was generated
        assert len(visualization_updates) == 1
        update = visualization_updates[0]
        assert update["workflow_id"] == "test-workflow-123"
        assert update["diagram_type"] == "timeline"
        assert "gantt" in update["diagram"]
        assert "concierge" in update["diagram"]
        
        await event_bus.stop()

    @pytest.mark.asyncio
    async def test_dashboard_update_event_generation(self, visualizer, event_bus):
        """Test that dashboard update events are generated from visualizations."""
        dashboard_updates = []
        
        async def dashboard_handler(event: LangGraphEvent):
            """Handler that captures dashboard updates."""
            if event.event_type == "dashboard.update_required":
                dashboard_updates.append(event.data)

        # Subscribe to dashboard update events
        event_bus.subscribe("dashboard.update_required", dashboard_handler)
        
        # Start event bus
        await event_bus.start()
        
        # Create a workflow visualization
        workflow_definition = {
            "nodes": {
                "start": {"label": "Start", "type": "start"},
                "agent1": {"label": "Agent", "type": "agent"},
                "end": {"label": "End", "type": "end"}
            },
            "edges": [
                {"source": "start", "target": "agent1"},
                {"source": "agent1", "target": "end"}
            ]
        }
        
        # Generate visualization
        diagram = visualizer.generate_mermaid_flowchart(workflow_definition)
        
        # Create dashboard update event
        dashboard_event = DashboardUpdateEvent(
            dashboard_id="main-dashboard",
            widget_id="workflow-diagram-widget",
            data={
                "diagram_type": "flowchart",
                "diagram_content": diagram,
                "workflow_id": "test-workflow"
            }
        )
        
        # Publish dashboard update event
        await event_bus.publish(dashboard_event)
        
        # Wait for event processing
        await asyncio.sleep(0.1)
        
        # Verify dashboard update was captured
        assert len(dashboard_updates) == 1
        update = dashboard_updates[0]
        assert update["dashboard_id"] == "main-dashboard"
        assert update["widget_id"] == "workflow-diagram-widget"
        assert "diagram_content" in update["update_data"]
        
        await event_bus.stop()

    @pytest.mark.asyncio
    async def test_metrics_integration_with_visualization(self, visualizer, metrics_system):
        """Test integration between metrics collection and visualization."""
        # Mock metrics data
        mock_metrics = {
            "workflow_id": "test-workflow",
            "execution_time": 5.5,
            "success_rate": 0.95,
            "agent_utilization": {
                "concierge": {"total_time": 2.0, "task_count": 1, "avg_time": 2.0},
                "analysis": {"total_time": 5.0, "task_count": 1, "avg_time": 5.0},
                "marketing": {"total_time": 3.0, "task_count": 1, "avg_time": 3.0}
            }
        }
        
        # Create execution history from metrics
        execution_history = [
            {
                "agent_id": "concierge",
                "task_name": "Initialize",
                "start_time": "2024-01-01T10:00:00Z",
                "end_time": "2024-01-01T10:00:02Z",
                "status": "completed",
                "execution_time": 2.0
            },
            {
                "agent_id": "analysis",
                "task_name": "Analyze",
                "start_time": "2024-01-01T10:00:02Z",
                "end_time": "2024-01-01T10:00:07Z",
                "status": "completed",
                "execution_time": 5.0
            },
            {
                "agent_id": "marketing",
                "task_name": "Generate",
                "start_time": "2024-01-01T10:00:07Z",
                "end_time": "2024-01-01T10:00:10Z",
                "status": "completed",
                "execution_time": 3.0
            }
        ]
        
        # Generate performance visualization
        timeline = visualizer.generate_execution_timeline(execution_history)
        
        # Verify timeline contains performance data
        assert "gantt" in timeline
        assert "concierge" in timeline
        assert "analysis" in timeline
        assert "marketing" in timeline
        
        # Test bottleneck identification in visualization
        bottlenecks = visualizer._identify_bottlenecks(execution_history)
        assert len(bottlenecks) == 1  # Analysis agent is bottleneck (5s > 5s threshold)
        assert bottlenecks[0]["agent_id"] == "analysis"

    @pytest.mark.asyncio
    async def test_real_time_visualization_updates(self, visualizer, event_bus):
        """Test real-time visualization updates through event system."""
        visualization_cache = {}
        
        async def real_time_handler(event: LangGraphEvent):
            """Handler for real-time visualization updates."""
            if event.event_type == "workflow.completed":
                workflow_id = event.data["workflow_id"]
                
                # Generate multiple visualization types
                execution_data = event.data.get("results", {})
                timeline_data = execution_data.get("execution_timeline", [])
                
                if timeline_data:
                    # Generate timeline
                    timeline = visualizer.generate_execution_timeline(timeline_data)
                    
                    # Generate sequence diagram for agent interactions
                    interactions = visualizer._extract_agent_interactions(timeline_data)
                    sequence = visualizer.generate_sequence_diagram(interactions)
                    
                    # Cache visualizations
                    visualization_cache[workflow_id] = {
                        "timeline": timeline,
                        "sequence": sequence,
                        "last_updated": event.timestamp
                    }

        # Subscribe to events
        event_bus.subscribe("workflow.completed", real_time_handler)
        
        # Start event bus
        await event_bus.start()
        
        # Simulate multiple workflow completions
        for i in range(3):
            workflow_event = WorkflowCompletedEvent(
                workflow_id=f"workflow-{i}",
                execution_time=2.0 + i,
                success=True,
                results={
                    "execution_timeline": [
                        {
                            "agent_id": "agent1",
                            "start_time": f"2024-01-01T10:0{i}:00Z",
                            "end_time": f"2024-01-01T10:0{i}:02Z",
                            "status": "completed"
                        },
                        {
                            "agent_id": "agent2",
                            "start_time": f"2024-01-01T10:0{i}:02Z",
                            "end_time": f"2024-01-01T10:0{i}:04Z",
                            "status": "completed"
                        }
                    ]
                }
            )
            await event_bus.publish(workflow_event)
        
        # Wait for processing
        await asyncio.sleep(0.2)
        
        # Verify all workflows were processed
        assert len(visualization_cache) == 3
        for i in range(3):
            workflow_id = f"workflow-{i}"
            assert workflow_id in visualization_cache
            assert "timeline" in visualization_cache[workflow_id]
            assert "sequence" in visualization_cache[workflow_id]
            assert "gantt" in visualization_cache[workflow_id]["timeline"]
            assert "sequenceDiagram" in visualization_cache[workflow_id]["sequence"]
        
        await event_bus.stop()

    @pytest.mark.asyncio
    async def test_performance_monitoring_integration(self, visualizer, event_bus, metrics_system):
        """Test integration between performance monitoring and visualization."""
        performance_data = []
        
        async def performance_handler(event: LangGraphEvent):
            """Handler for performance monitoring."""
            if event.event_type == "workflow.completed":
                workflow_id = event.data["workflow_id"]
                execution_time = event.data["execution_time"]
                success = event.data["success"]
                
                # Record metrics
                success_rate = 1.0 if success else 0.0
                
                # Mock recording metrics (would normally use database)
                performance_data.append({
                    "workflow_id": workflow_id,
                    "execution_time": execution_time,
                    "success_rate": success_rate,
                    "timestamp": event.timestamp
                })
                
                # Generate performance visualization if we have enough data
                if len(performance_data) >= 3:
                    # Create mock performance heatmap data
                    heatmap_data = {
                        "agent1": {"execution_time": 2.0, "success_rate": 1.0},
                        "agent2": {"execution_time": 3.0, "success_rate": 0.9},
                        "agent3": {"execution_time": 1.5, "success_rate": 1.0}
                    }
                    
                    # This would normally generate an actual heatmap
                    # For testing, we just verify the data structure
                    assert isinstance(heatmap_data, dict)
                    assert all(isinstance(v, dict) for v in heatmap_data.values())

        # Subscribe to events
        event_bus.subscribe("workflow.completed", performance_handler)
        
        # Start event bus
        await event_bus.start()
        
        # Publish multiple workflow events
        for i in range(5):
            workflow_event = WorkflowCompletedEvent(
                workflow_id=f"perf-workflow-{i}",
                execution_time=2.0 + (i * 0.5),
                success=i < 4,  # Last one fails
                results={}
            )
            await event_bus.publish(workflow_event)
        
        # Wait for processing
        await asyncio.sleep(0.2)
        
        # Verify performance data was collected
        assert len(performance_data) == 5
        
        # Verify success rates
        success_rates = [data["success_rate"] for data in performance_data]
        assert success_rates.count(1.0) == 4
        assert success_rates.count(0.0) == 1
        
        # Verify execution times
        execution_times = [data["execution_time"] for data in performance_data]
        assert min(execution_times) == 2.0
        assert max(execution_times) == 4.0
        
        await event_bus.stop()

    def test_visualization_error_handling_with_events(self, visualizer):
        """Test error handling in visualization when processing event data."""
        # Test with malformed event data
        malformed_timeline = [
            {"invalid": "data"},
            {"agent_id": "test", "missing_timestamps": True}
        ]
        
        # Should not raise exception, should return valid diagram
        result = visualizer.generate_execution_timeline(malformed_timeline)
        assert isinstance(result, str)
        assert "gantt" in result
        
        # Test with empty workflow definition
        empty_workflow = {}
        result = visualizer.generate_mermaid_flowchart(empty_workflow)
        assert isinstance(result, str)
        assert "flowchart TD" in result
