"""
Integration tests for template system and event system.

This module tests the integration between the workflow template system
and the event-driven architecture.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch

from ..templates.template_manager import template_manager
from ..templates.simple_analysis_template import SimpleAnalysisTemplate
from ..events.event_bus import LangGraphEventBus
from ..events.types import WorkflowStartedEvent


class TestTemplateEventIntegration:
    """Test integration between templates and events."""
    
    @pytest.fixture
    def event_bus(self):
        """Create event bus for testing."""
        return LangGraphEventBus()
    
    @pytest.fixture
    def manager_with_events(self, event_bus):
        """Create template manager with event integration."""
        # Patch the global event bus
        with patch('backend.agents.langgraph.templates.base_template.event_bus', event_bus):
            manager = template_manager
            manager.register_template(SimpleAnalysisTemplate, "simple_analysis", "1.0.0")
            yield manager
    
    @pytest.mark.asyncio
    async def test_workflow_creation_publishes_event(self, manager_with_events, event_bus):
        """Test that workflow creation publishes events."""
        # Subscribe to workflow events
        received_events = []
        
        async def event_handler(event):
            received_events.append(event)
        
        await event_bus.subscribe("workflow.started", event_handler)
        
        # Create workflow
        params = {
            "data_source": "business_profile",
            "user_id": "test_user",
            "business_profile_id": "profile_123"
        }
        
        workflow = await manager_with_events.create_workflow("simple_analysis", params)
        
        # Allow event processing
        await asyncio.sleep(0.1)
        
        assert workflow is not None
        assert len(received_events) >= 1
        
        # Check event details
        event = received_events[0]
        assert isinstance(event, WorkflowStartedEvent)
        assert event.workflow_type == "simple_analysis"
        assert event.user_id == "test_user"
    
    @pytest.mark.asyncio
    async def test_multiple_workflow_events(self, manager_with_events, event_bus):
        """Test multiple workflow creation events."""
        received_events = []
        
        async def event_handler(event):
            received_events.append(event)
        
        await event_bus.subscribe("workflow.started", event_handler)
        
        # Create multiple workflows
        workflows = []
        for i in range(3):
            params = {
                "data_source": "business_profile",
                "user_id": f"test_user_{i}",
                "business_profile_id": f"profile_{i}"
            }
            
            workflow = await manager_with_events.create_workflow("simple_analysis", params)
            workflows.append(workflow)
        
        # Allow event processing
        await asyncio.sleep(0.1)
        
        assert len(workflows) == 3
        assert all(w is not None for w in workflows)
        assert len(received_events) >= 3
        
        # Check that each event has unique user_id
        user_ids = [event.user_id for event in received_events]
        assert len(set(user_ids)) == 3
    
    @pytest.mark.asyncio
    async def test_event_bus_performance_with_templates(self, manager_with_events, event_bus):
        """Test event bus performance when integrated with templates."""
        import time
        
        received_events = []
        
        async def event_handler(event):
            received_events.append(event)
        
        await event_bus.subscribe("workflow.started", event_handler)
        
        # Create workflows and measure time
        num_workflows = 10
        start_time = time.time()
        
        tasks = []
        for i in range(num_workflows):
            params = {
                "data_source": "business_profile",
                "user_id": f"perf_test_user_{i}",
                "business_profile_id": f"profile_{i}"
            }
            
            task = manager_with_events.create_workflow("simple_analysis", params)
            tasks.append(task)
        
        workflows = await asyncio.gather(*tasks)
        creation_time = time.time() - start_time
        
        # Allow event processing
        await asyncio.sleep(0.2)
        
        assert len(workflows) == num_workflows
        assert all(w is not None for w in workflows)
        assert len(received_events) >= num_workflows
        
        # Performance should still be good with events
        avg_time_per_workflow = creation_time / num_workflows
        assert avg_time_per_workflow < 0.5  # Less than 500ms per workflow
        
        print(f"Created {num_workflows} workflows with events in {creation_time:.3f} seconds")
        print(f"Average time per workflow: {avg_time_per_workflow:.3f} seconds")
    
    @pytest.mark.asyncio
    async def test_event_error_handling(self, manager_with_events, event_bus):
        """Test event error handling during workflow creation."""
        # Subscribe with a handler that raises an exception
        async def failing_handler(event):
            raise Exception("Handler error")
        
        await event_bus.subscribe("workflow.started", failing_handler)
        
        # Workflow creation should still succeed even if event handler fails
        params = {
            "data_source": "business_profile",
            "user_id": "error_test_user",
            "business_profile_id": "profile_123"
        }
        
        workflow = await manager_with_events.create_workflow("simple_analysis", params)
        
        # Allow event processing
        await asyncio.sleep(0.1)
        
        # Workflow should be created successfully despite handler error
        assert workflow is not None
    
    @pytest.mark.asyncio
    async def test_template_metrics_with_events(self, manager_with_events, event_bus):
        """Test template metrics collection with event integration."""
        # Create some workflows
        for i in range(5):
            params = {
                "data_source": "business_profile",
                "user_id": f"metrics_test_user_{i}",
                "business_profile_id": f"profile_{i}"
            }
            
            await manager_with_events.create_workflow("simple_analysis", params)
        
        # Allow event processing
        await asyncio.sleep(0.1)
        
        # Check metrics
        metrics = manager_with_events.get_metrics()
        
        assert metrics["templates_created"] >= 5
        assert metrics["creation_errors"] == 0
        
        # Check event bus metrics
        event_metrics = event_bus.get_metrics()
        
        assert event_metrics["events_published"] >= 5
        assert event_metrics["processing_errors"] == 0


class TestTemplateEventScenarios:
    """Test real-world scenarios with templates and events."""
    
    @pytest.mark.asyncio
    async def test_business_analysis_workflow_events(self):
        """Test business analysis workflow with event integration."""
        from ..templates.business_analysis_template import BusinessAnalysisTemplate
        
        event_bus = LangGraphEventBus()
        received_events = []
        
        async def event_handler(event):
            received_events.append(event)
        
        await event_bus.subscribe("workflow.started", event_handler)
        
        # Create business analysis template
        with patch('backend.agents.langgraph.templates.base_template.event_bus', event_bus):
            template = BusinessAnalysisTemplate()
            
            params = {
                "business_profile_id": "profile_123",
                "user_id": "business_test_user",
                "analysis_depth": "comprehensive",
                "include_competitive_analysis": True,
                "include_financial_projections": True
            }
            
            workflow = await template.create_workflow_with_validation(params)
            
            # Allow event processing
            await asyncio.sleep(0.1)
            
            assert workflow is not None
            assert len(received_events) >= 1
            
            event = received_events[0]
            assert event.workflow_type == "business_analysis"
            assert event.user_id == "business_test_user"
            assert len(event.agents_involved) > 0
    
    @pytest.mark.asyncio
    async def test_dashboard_generation_workflow_events(self):
        """Test dashboard generation workflow with event integration."""
        from ..templates.dashboard_generation_template import DashboardGenerationTemplate
        
        event_bus = LangGraphEventBus()
        received_events = []
        
        async def event_handler(event):
            received_events.append(event)
        
        await event_bus.subscribe("workflow.started", event_handler)
        
        # Create dashboard generation template
        with patch('backend.agents.langgraph.templates.base_template.event_bus', event_bus):
            template = DashboardGenerationTemplate()
            
            params = {
                "dashboard_name": "Test Dashboard",
                "user_id": "dashboard_test_user",
                "dashboard_type": "business_overview",
                "include_interactive_features": True
            }
            
            workflow = await template.create_workflow_with_validation(params)
            
            # Allow event processing
            await asyncio.sleep(0.1)
            
            assert workflow is not None
            assert len(received_events) >= 1
            
            event = received_events[0]
            assert event.workflow_type == "dashboard_generation"
            assert event.user_id == "dashboard_test_user"
    
    @pytest.mark.asyncio
    async def test_concurrent_template_events(self):
        """Test concurrent template creation with events."""
        event_bus = LangGraphEventBus()
        received_events = []
        
        async def event_handler(event):
            received_events.append(event)
        
        await event_bus.subscribe("workflow.started", event_handler)
        
        # Create multiple templates concurrently
        with patch('backend.agents.langgraph.templates.base_template.event_bus', event_bus):
            simple_template = SimpleAnalysisTemplate()
            
            async def create_workflow(template, params):
                return await template.create_workflow_with_validation(params)
            
            # Create concurrent workflows
            tasks = []
            for i in range(5):
                params = {
                    "data_source": "business_profile",
                    "user_id": f"concurrent_user_{i}",
                    "business_profile_id": f"profile_{i}"
                }
                task = create_workflow(simple_template, params)
                tasks.append(task)
            
            workflows = await asyncio.gather(*tasks)
            
            # Allow event processing
            await asyncio.sleep(0.2)
            
            assert len(workflows) == 5
            assert all(w is not None for w in workflows)
            assert len(received_events) >= 5
            
            # Check that all events have unique user IDs
            user_ids = [event.user_id for event in received_events]
            assert len(set(user_ids)) == 5


if __name__ == "__main__":
    # Simple test runner for development
    async def run_basic_test():
        """Run a basic integration test."""
        print("Running basic template-event integration test...")
        
        event_bus = LangGraphEventBus()
        received_events = []
        
        async def event_handler(event):
            received_events.append(event)
            print(f"Received event: {event.event_type} for workflow {event.workflow_id}")
        
        await event_bus.subscribe("workflow.started", event_handler)
        
        with patch('backend.agents.langgraph.templates.base_template.event_bus', event_bus):
            template = SimpleAnalysisTemplate()
            
            params = {
                "data_source": "business_profile",
                "user_id": "test_user",
                "business_profile_id": "profile_123"
            }
            
            workflow = await template.create_workflow_with_validation(params)
            
            # Allow event processing
            await asyncio.sleep(0.1)
            
            print(f"Workflow created: {workflow is not None}")
            print(f"Events received: {len(received_events)}")
            
            if received_events:
                event = received_events[0]
                print(f"Event details: {event.workflow_type}, {event.user_id}")
        
        print("Basic integration test completed successfully!")
    
    # Run the test
    asyncio.run(run_basic_test())
