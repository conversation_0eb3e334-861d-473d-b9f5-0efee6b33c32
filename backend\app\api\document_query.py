"""
Document query API for the Datagenius backend.

This module provides API endpoints for querying document data stored in vector databases.
"""

import logging
import os
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from sqlalchemy.orm import Session

from ..dependencies import get_file_repository
from ..repositories.file_repository import FileRepository
from ..models.database_models import User
from ..auth import get_current_active_user

# Import yaml_utils using centralized import utility
from ..utils.import_utils import import_yaml_utils

load_yaml, _ = import_yaml_utils()


from langchain_groq import ChatGroq



logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/document-query",
    tags=["document-query"],
    responses={404: {"description": "Not found"}},
)


class DocumentQueryRequest(BaseModel):
    """Model for document query request."""
    file_id: str
    query_type: str = "marketing_fields"
    specific_fields: Optional[List[str]] = None


class DocumentQueryResponse(BaseModel):
    """Model for document query response."""
    results: Dict[str, Any]


@router.post("", response_model=DocumentQueryResponse)
async def query_document(
    request: DocumentQueryRequest,
    file_repo: FileRepository = Depends(get_file_repository),
    current_user: User = Depends(get_current_active_user)
):
    """
    Query a document.

    Args:
        request: Query request
        file_repo: File repository
        current_user: Current authenticated user

    Returns:
        Query results
    """
    logger.info(f"User {current_user.id} querying document {request.file_id}")

    # Get file from database
    file = file_repo.get_file(request.file_id)
    if not file:
        logger.warning(f"File not found in database: {request.file_id}")
        raise HTTPException(status_code=404, detail="File not found in database. It may have been deleted.")

    # Check if file exists on disk
    file_path = file.file_path
    # Normalize path separators for consistency
    file_path = file_path.replace("\\", "/")
    if not os.path.exists(file_path):
        logger.warning(f"File not found on disk: {file_path}")
        raise HTTPException(status_code=404, detail="File exists in database but not on disk. Please contact support.")

    # Use mem0ai for vector operations - no local vector_db directory needed
    from agents.utils.vector_service import VectorService

    # Get AI model configuration from concierge agent (same as business profile autofill)
    try:
        from agents.utils.vector_service import VectorService
        from agents.utils.memory_service import MemoryService
        from app.services.ai_field_mapping_service import AIFieldMappingService

        # Create AI field mapping service to get concierge agent model configuration
        mapping_service = AIFieldMappingService()

        # Load concierge agent model configuration
        await mapping_service._load_concierge_model_config(user_settings=None)

        # Create user settings dict with concierge agent's provider/model for memory service
        user_settings = {
            "memory_service_provider": mapping_service.analysis_config.provider,
            "memory_service_model": mapping_service.analysis_config.model
        }

        logger.info(f"Using concierge agent settings for embedding: {user_settings}")

        # Get memory service instance with concierge agent's provider/model
        memory_service = MemoryService.get_instance_with_user_settings(user_settings)

        # Get vector service instance with the configured memory service
        vector_service = VectorService()
        vector_service.memory_service = memory_service
        vector_service.memory = memory_service.memory
        vector_service.initialized = memory_service.initialized

        logger.info(f"Vector service configured with concierge agent settings: provider={mapping_service.analysis_config.provider}, model={mapping_service.analysis_config.model}")
    except Exception as e:
        logger.error(f"Error getting concierge agent configuration: {str(e)}")
        # Fall back to default vector service
        from agents.utils.vector_service import VectorService
        vector_service = VectorService()

    # Embed the document using adaptive chunking (this will handle existing embeddings automatically)
    try:
        vector_store_id, file_info = await vector_service.embed_document(
            file_path=file_path,
            use_adaptive_chunking=True  # Enable adaptive chunking for optimal performance
        )
        logger.info(f"Document embedded successfully with adaptive chunking. Vector store ID: {vector_store_id}")
        logger.info(f"Chunking strategy: {file_info.get('chunking_strategy', 'adaptive')}")
        if 'content_type' in file_info:
            logger.info(f"Detected content type: {file_info['content_type']}")
    except Exception as e:
        logger.error(f"Error embedding document: {e}")
        raise HTTPException(status_code=500, detail=f"Error embedding document: {str(e)}")

    # Process query based on query type
    if request.query_type == "marketing_fields":
        # Define marketing field queries
        marketing_field_queries = {
            "brand_description": "Based on the provided context, write a concise brand description. Extract information about the company's mission, values, and unique selling points.",
            "target_audience": "Based on the provided context, identify and describe the target audience or customer segments for this business. Include demographics, psychographics, and key characteristics.",
            "products_services": "Based on the provided context, list and briefly describe the main products and/or services offered by the business.",
            "marketing_goals": "Based on the provided context, identify the key marketing goals or objectives for this business. If not explicitly stated, suggest reasonable goals based on the business type and information provided.",
            "existing_content": "Based on the provided context, summarize any existing marketing content, campaigns, or channels mentioned in the document.",
            "keywords": "Based on the provided context, generate a list of 10-15 relevant keywords for this business that could be used for marketing purposes. Format as a comma-separated list.",
            "suggested_topics": "Based on the provided context, suggest 5-7 content topics that would be relevant for this business's marketing strategy. Present as a numbered list.",
            "competitive_landscape": "Based on the provided context, describe the competitive landscape for this business. Include information about competitors, market position, industry trends, and competitive advantages or challenges.",
            "budget": "Based on the provided context, identify any budget constraints, financial considerations, or resource allocations mentioned for marketing activities. If not explicitly stated, suggest reasonable budget considerations based on the business type.",
            "timeline": "Based on the provided context, identify any timeline constraints, deadlines, or scheduling requirements mentioned for marketing activities. Include project timelines, campaign schedules, or launch dates.",
            "platforms": "Based on the provided context, identify the specific platforms, channels, or mediums mentioned for marketing and content distribution. Include social media platforms, advertising channels, or communication mediums."
        }

        # Filter fields if specific fields are requested
        if request.specific_fields:
            marketing_field_queries = {k: v for k, v in marketing_field_queries.items() if k in request.specific_fields}

        # Query the vector store for each marketing field
        results = {}

        # Initialize LLM for processing using concierge agent configuration
        try:
            from agents.utils.model_providers.utils import get_model

            # Use the same AI model configuration as loaded for vector service
            # If mapping_service is not available from the vector service setup, create a new one
            if 'mapping_service' not in locals():
                from app.services.ai_field_mapping_service import AIFieldMappingService
                mapping_service = AIFieldMappingService()
                await mapping_service._load_concierge_model_config(user_settings=None)

            provider = mapping_service.analysis_config.provider
            model = mapping_service.analysis_config.model
            temperature = mapping_service.analysis_config.temperature

            logger.info(f"Document query using concierge agent configuration: provider={provider}, model={model}, temperature={temperature}")

            llm = await get_model(
                provider_id=provider,
                model_id=model,
                config={"temperature": temperature}
            )
            logger.info(f"Using concierge agent configuration: {provider} provider with model {model} for document query")
        except Exception as e:
            logger.error(f"Error initializing LLM with concierge agent settings: {e}")
            # Fall back to Groq with a known working model
            try:
                llm = await get_model(
                    provider_id="groq",
                    model_id="llama3-70b-8192",
                    config={"temperature": 0.1}
                )
                logger.info("Fell back to Groq llama3-70b-8192 model")
            except Exception as fallback_error:
                logger.error(f"Fallback to Groq also failed: {fallback_error}")
                # Last resort: direct ChatGroq initialization
                llm = ChatGroq(
                    temperature=0.1,
                    model_name="llama3-70b-8192",
                    groq_api_key=os.getenv("GROQ_API_KEY", "")
                )
                logger.info("Using direct ChatGroq initialization as last resort")

        # Create the prompt template
        prompt_template = """You are an AI assistant that helps extract information from documents.

Context from document:
{context}

Task: {query}

Provide a concise, well-formatted response based only on the information in the context.
If the context doesn't contain relevant information, provide a reasonable response based on the type of business or organization mentioned."""

        for field, query in marketing_field_queries.items():
            try:
                logger.info(f"Processing field '{field}' with query: {query[:100]}...")
                # Query the vector store using mem0ai
                search_results = vector_service.search_document(vector_store_id, query, limit=3)
                logger.info(f"Search results for '{field}': {len(search_results) if search_results else 0} results found")

                if search_results:
                    # Combine the results
                    context = "\n\n".join([result.get("content", "") for result in search_results])

                    # Log the context being used
                    logger.info(f"Field '{field}' - Context length: {len(context)}")
                    logger.info(f"Field '{field}' - Context preview: '{context[:300]}...'")
                    logger.info(f"Field '{field}' - Search results count: {len(search_results)}")
                    for idx, result in enumerate(search_results):
                        content = result.get("content", "")
                        logger.info(f"Field '{field}' - Result {idx} content length: {len(content)}")
                        logger.info(f"Field '{field}' - Result {idx} content preview: '{content[:100]}...'")

                    # Format the prompt with context and query
                    formatted_prompt = prompt_template.format(context=context, query=query)

                    # Execute the model directly using ainvoke
                    logger.info(f"Querying field '{field}' with context length: {len(context)}")
                    response = await llm.ainvoke(formatted_prompt)
                    logger.info(f"Response for field '{field}': {str(response)[:200]}...")

                    # Extract content from response (handle both string and object responses)
                    if hasattr(response, 'content'):
                        results[field] = response.content
                        logger.info(f"Extracted content for '{field}': {response.content[:100]}...")
                    elif isinstance(response, str):
                        results[field] = response
                        logger.info(f"String response for '{field}': {response[:100]}...")
                    else:
                        results[field] = str(response)
                        logger.info(f"Converted response for '{field}': {str(response)[:100]}...")
                else:
                    logger.warning(f"No search results found for field '{field}', setting empty string")
                    results[field] = ""
            except Exception as e:
                logger.error(f"Error querying field {field}: {str(e)}", exc_info=True)
                results[field] = ""

        logger.info(f"Final results for document query: {results}")
        return DocumentQueryResponse(results=results)
    else:
        raise HTTPException(status_code=400, detail=f"Unsupported query type: {request.query_type}")
