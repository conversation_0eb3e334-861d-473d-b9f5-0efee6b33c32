"""
Tests for the Workflow Visualizer with Mermaid diagram generation.

This module tests all visualization capabilities including:
- Mermaid flowchart generation
- Execution timeline visualization
- State diagram creation
- Interactive workflow exploration
"""

import pytest
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from ..visualization.workflow_visualizer import (
    WorkflowVisualizer, 
    DiagramType, 
    WorkflowNode, 
    WorkflowEdge
)


class TestWorkflowVisualizer:
    """Test suite for WorkflowVisualizer class."""

    @pytest.fixture
    def visualizer(self):
        """Create a WorkflowVisualizer instance for testing."""
        return WorkflowVisualizer()

    @pytest.fixture
    def sample_workflow_definition(self):
        """Sample workflow definition for testing."""
        return {
            "id": "test-workflow",
            "nodes": {
                "start": {
                    "label": "Start",
                    "type": "start"
                },
                "agent1": {
                    "label": "Analysis Agent",
                    "type": "agent"
                },
                "decision1": {
                    "label": "Route Decision",
                    "type": "decision"
                },
                "tool1": {
                    "label": "Data Tool",
                    "type": "tool"
                },
                "end": {
                    "label": "End",
                    "type": "end"
                }
            },
            "edges": [
                {"source": "start", "target": "agent1", "label": "initialize"},
                {"source": "agent1", "target": "decision1", "label": "analyze"},
                {"source": "decision1", "target": "tool1", "label": "use_tool"},
                {"source": "tool1", "target": "end", "label": "complete"}
            ]
        }

    @pytest.fixture
    def sample_execution_data(self):
        """Sample execution data for testing."""
        return {
            "start": {"status": "completed"},
            "agent1": {"status": "completed"},
            "decision1": {"status": "running"},
            "tool1": {"status": "pending"},
            "end": {"status": "pending"}
        }

    @pytest.fixture
    def sample_execution_history(self):
        """Sample execution history for testing."""
        base_time = datetime.now()
        return [
            {
                "agent_id": "concierge",
                "task_name": "Initialize Request",
                "start_time": base_time.isoformat(),
                "end_time": (base_time + timedelta(seconds=2)).isoformat(),
                "status": "completed",
                "execution_time": 2.0
            },
            {
                "agent_id": "analysis",
                "task_name": "Analyze Data",
                "start_time": (base_time + timedelta(seconds=2)).isoformat(),
                "end_time": (base_time + timedelta(seconds=8)).isoformat(),
                "status": "completed",
                "execution_time": 6.0
            },
            {
                "agent_id": "marketing",
                "task_name": "Generate Content",
                "start_time": (base_time + timedelta(seconds=8)).isoformat(),
                "end_time": (base_time + timedelta(seconds=12)).isoformat(),
                "status": "running",
                "execution_time": 4.0
            }
        ]

    def test_generate_mermaid_flowchart_basic(self, visualizer, sample_workflow_definition):
        """Test basic Mermaid flowchart generation."""
        result = visualizer.generate_mermaid_flowchart(sample_workflow_definition)
        
        assert isinstance(result, str)
        assert "flowchart TD" in result
        assert "start" in result
        assert "agent1" in result
        assert "decision1" in result
        assert "tool1" in result
        assert "end" in result
        assert "-->" in result

    def test_generate_mermaid_flowchart_with_execution_data(
        self, visualizer, sample_workflow_definition, sample_execution_data
    ):
        """Test Mermaid flowchart generation with execution data."""
        result = visualizer.generate_mermaid_flowchart(
            sample_workflow_definition, 
            sample_execution_data,
            "Test Workflow with Status"
        )
        
        assert isinstance(result, str)
        assert "Test Workflow with Status" in result
        assert "flowchart TD" in result
        assert "style" in result  # Should include styling for status
        assert "#4CAF50" in result or "#FF9800" in result  # Should have status colors

    def test_generate_mermaid_flowchart_empty_workflow(self, visualizer):
        """Test flowchart generation with empty workflow."""
        empty_workflow = {"nodes": {}, "edges": []}
        result = visualizer.generate_mermaid_flowchart(empty_workflow)
        
        assert isinstance(result, str)
        assert "flowchart TD" in result

    def test_generate_execution_timeline(self, visualizer, sample_execution_history):
        """Test execution timeline generation."""
        result = visualizer.generate_execution_timeline(sample_execution_history)
        
        assert isinstance(result, str)
        assert "gantt" in result
        assert "concierge" in result
        assert "analysis" in result
        assert "marketing" in result
        assert "Initialize Request" in result

    def test_generate_execution_timeline_empty_history(self, visualizer):
        """Test timeline generation with empty history."""
        result = visualizer.generate_execution_timeline([])
        
        assert isinstance(result, str)
        assert "gantt" in result
        assert "No execution data available" in result

    def test_generate_state_diagram(self, visualizer):
        """Test state diagram generation."""
        workflow_states = {
            "states": {
                "idle": {
                    "label": "Idle",
                    "is_start": True,
                    "description": "Waiting for input"
                },
                "processing": {
                    "label": "Processing",
                    "description": "Analyzing data"
                },
                "complete": {
                    "label": "Complete",
                    "is_end": True,
                    "description": "Task finished"
                }
            },
            "transitions": [
                {"source": "idle", "target": "processing", "condition": "start_task"},
                {"source": "processing", "target": "complete", "condition": "task_done"}
            ]
        }
        
        result = visualizer.generate_state_diagram(workflow_states)
        
        assert isinstance(result, str)
        assert "stateDiagram-v2" in result
        assert "idle" in result
        assert "processing" in result
        assert "complete" in result
        assert "[*] --> idle" in result
        assert "complete --> [*]" in result

    def test_generate_sequence_diagram(self, visualizer):
        """Test sequence diagram generation."""
        agent_interactions = [
            {
                "source": "user",
                "target": "concierge",
                "message": "Submit request",
                "type": "sync"
            },
            {
                "source": "concierge",
                "target": "analysis",
                "message": "Analyze data",
                "type": "async"
            },
            {
                "source": "analysis",
                "target": "concierge",
                "message": "Results ready",
                "type": "response"
            }
        ]
        
        result = visualizer.generate_sequence_diagram(agent_interactions)
        
        assert isinstance(result, str)
        assert "sequenceDiagram" in result
        assert "participant user" in result
        assert "participant concierge" in result
        assert "participant analysis" in result
        assert "user -> concierge" in result
        assert "concierge ->> analysis" in result
        assert "analysis -->> concierge" in result

    def test_generate_user_journey(self, visualizer):
        """Test user journey diagram generation."""
        journey_steps = [
            {
                "section": "Discovery",
                "name": "Find Platform",
                "satisfaction": 4,
                "actors": ["User"]
            },
            {
                "section": "Onboarding",
                "name": "Create Account",
                "satisfaction": 3,
                "actors": ["User", "System"]
            },
            {
                "section": "Usage",
                "name": "Run Analysis",
                "satisfaction": 5,
                "actors": ["User", "AI Agent"]
            }
        ]
        
        result = visualizer.generate_user_journey(journey_steps)
        
        assert isinstance(result, str)
        assert "journey" in result
        assert "section Discovery" in result
        assert "section Onboarding" in result
        assert "section Usage" in result
        assert "Find Platform: 4: User" in result

    def test_create_interactive_workflow_explorer(
        self, visualizer, sample_workflow_definition, sample_execution_history
    ):
        """Test interactive workflow explorer creation."""
        result = visualizer.create_interactive_workflow_explorer(
            sample_workflow_definition,
            sample_execution_history
        )
        
        assert isinstance(result, dict)
        assert "metadata" in result
        assert "diagrams" in result
        assert "analysis" in result
        
        # Check metadata
        metadata = result["metadata"]
        assert metadata["workflow_id"] == "test-workflow"
        assert metadata["node_count"] == 5
        assert metadata["edge_count"] == 4
        assert metadata["has_execution_data"] is True
        
        # Check diagrams
        diagrams = result["diagrams"]
        assert "flowchart" in diagrams
        assert "timeline" in diagrams
        assert "sequence" in diagrams
        
        # Check analysis
        analysis = result["analysis"]
        assert "structure" in analysis
        assert "performance" in analysis

    def test_extract_agent_interactions(self, visualizer, sample_execution_history):
        """Test agent interaction extraction."""
        interactions = visualizer._extract_agent_interactions(sample_execution_history)
        
        assert isinstance(interactions, list)
        assert len(interactions) == 2  # 3 agents = 2 transitions
        
        first_interaction = interactions[0]
        assert first_interaction["source"] == "concierge"
        assert first_interaction["target"] == "analysis"
        assert first_interaction["type"] == "sync"

    def test_analyze_workflow_performance(
        self, visualizer, sample_workflow_definition, sample_execution_history
    ):
        """Test workflow performance analysis."""
        analysis = visualizer._analyze_workflow_performance(
            sample_workflow_definition,
            sample_execution_history
        )
        
        assert isinstance(analysis, dict)
        assert "structure" in analysis
        assert "performance" in analysis
        
        # Check structure analysis
        structure = analysis["structure"]
        assert "complexity_score" in structure
        assert "node_types" in structure
        assert "critical_path" in structure
        
        # Check performance analysis
        performance = analysis["performance"]
        assert "total_execution_time" in performance
        assert "bottlenecks" in performance
        assert "success_rate" in performance
        assert "agent_utilization" in performance

    def test_calculate_complexity_score(self, visualizer, sample_workflow_definition):
        """Test complexity score calculation."""
        score = visualizer._calculate_complexity_score(sample_workflow_definition)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 10.0

    def test_analyze_node_types(self, visualizer, sample_workflow_definition):
        """Test node type analysis."""
        node_types = visualizer._analyze_node_types(sample_workflow_definition)
        
        assert isinstance(node_types, dict)
        assert "start" in node_types
        assert "agent" in node_types
        assert "decision" in node_types
        assert "tool" in node_types
        assert "end" in node_types
        assert node_types["start"] == 1
        assert node_types["agent"] == 1

    def test_identify_critical_path(self, visualizer, sample_workflow_definition):
        """Test critical path identification."""
        critical_path = visualizer._identify_critical_path(sample_workflow_definition)
        
        assert isinstance(critical_path, list)
        assert len(critical_path) >= 1

    def test_calculate_total_execution_time(self, visualizer, sample_execution_history):
        """Test total execution time calculation."""
        total_time = visualizer._calculate_total_execution_time(sample_execution_history)
        
        assert isinstance(total_time, float)
        assert total_time == 12.0  # 2 + 6 + 4

    def test_identify_bottlenecks(self, visualizer, sample_execution_history):
        """Test bottleneck identification."""
        bottlenecks = visualizer._identify_bottlenecks(sample_execution_history)
        
        assert isinstance(bottlenecks, list)
        # Analysis agent should be identified as bottleneck (6 seconds > 5 second threshold)
        assert len(bottlenecks) == 1
        assert bottlenecks[0]["agent_id"] == "analysis"
        assert bottlenecks[0]["execution_time"] == 6.0

    def test_calculate_success_rate(self, visualizer, sample_execution_history):
        """Test success rate calculation."""
        success_rate = visualizer._calculate_success_rate(sample_execution_history)
        
        assert isinstance(success_rate, float)
        assert 0.0 <= success_rate <= 100.0
        # 2 completed out of 3 total = 66.67%
        assert abs(success_rate - 66.67) < 0.1

    def test_analyze_agent_utilization(self, visualizer, sample_execution_history):
        """Test agent utilization analysis."""
        utilization = visualizer._analyze_agent_utilization(sample_execution_history)
        
        assert isinstance(utilization, dict)
        assert "concierge" in utilization
        assert "analysis" in utilization
        assert "marketing" in utilization
        
        concierge_stats = utilization["concierge"]
        assert concierge_stats["total_time"] == 2.0
        assert concierge_stats["task_count"] == 1
        assert concierge_stats["avg_time"] == 2.0

    def test_error_handling_invalid_workflow(self, visualizer):
        """Test error handling with invalid workflow definition."""
        invalid_workflow = {"invalid": "structure"}
        result = visualizer.generate_mermaid_flowchart(invalid_workflow)
        
        assert isinstance(result, str)
        assert "flowchart TD" in result  # Should still generate basic structure

    def test_error_handling_invalid_execution_history(self, visualizer):
        """Test error handling with invalid execution history."""
        invalid_history = [{"invalid": "data"}]
        result = visualizer.generate_execution_timeline(invalid_history)
        
        assert isinstance(result, str)
        assert "gantt" in result  # Should still generate basic structure
