"""
Component for advanced routing and fallback mechanisms.
"""

import logging
import json
import time
import random
import sys
from typing import Dict, Any, List, Optional, Tuple, Set
from pathlib import Path

# Add backend root to path for import_utils
backend_root = Path(__file__).parent.parent.parent
if str(backend_root) not in sys.path:
    sys.path.insert(0, str(backend_root))

# Import AgentProcessingContext using centralized import
from app.utils.import_utils import import_agent_schemas
AgentProcessingContext, _ = import_agent_schemas()

from .base import AgentComponent
from ..langgraph.core.agent_factory import agent_factory

logger = logging.getLogger(__name__)


class RoutingStrategy:
    """Defines routing strategies for the advanced router."""

    DIRECT = "direct"  # Direct routing to a specific persona
    CAPABILITY = "capability"  # Routing based on required capabilities
    LOAD_BALANCED = "load_balanced"  # Distribute requests across personas
    HIERARCHICAL = "hierarchical"  # Route through a hierarchy
    FALLBACK = "fallback"  # Use fallback personas if primary is unavailable


class AdvancedRouterComponent(AgentComponent):
    """
    Provides advanced routing and fallback mechanisms for directing requests
    to the most appropriate personas, with error recovery and load balancing.
    """

    def __init__(self):
        """Initialize the AdvancedRouterComponent."""
        super().__init__()
        self.routing_history = {}  # Track routing decisions
        self.persona_loads = {}  # Track load on each persona
        self.fallback_chains = {}  # Define fallback chains
        self.capability_index = {}  # Index personas by capability
        self.error_counts = {}  # Track errors by persona

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the component.

        Args:
            config: Configuration dictionary for the component.
        """
        logger.info(f"AdvancedRouterComponent '{self.name}' initialized.")
        self.agent_factory = agent_factory
        self.routing_ttl = config.get("routing_ttl", 3600)  # Default TTL: 1 hour
        self.max_routing_history = config.get("max_routing_history", 100)
        self.load_threshold = config.get("load_threshold", 10)  # Max requests per persona
        self.error_threshold = config.get("error_threshold", 3)  # Max errors before fallback
        self.enable_auto_fallback = config.get("enable_auto_fallback", True)

        # Load fallback chains from configuration or generate dynamically
        self.fallback_chains = config.get("fallback_chains", {})
        if not self.fallback_chains:
            # Generate dynamic fallback chains based on available agents
            available_personas = self.agent_factory.get_available_agents()
            self.fallback_chains = self._generate_dynamic_fallback_chains(available_personas)

        # Initialize capability index
        await self._initialize_capability_index()

        # Initialize persona loads
        self.persona_loads = {persona: 0 for persona in self.agent_factory.get_available_agents()}

    async def process(self, context: "AgentProcessingContext") -> "AgentProcessingContext":
        """
        Process the context to provide advanced routing and fallback.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object with routing information.
        """
        user_message = context.message or ""
        conversation_id = str(context.conversation_id) # Ensure string
        current_persona = context.agent_config.id if context.agent_config else "unknown"

        logger.debug(f"AdvancedRouterComponent processing for conversation {conversation_id}")

        # Initialize routing context in component_data if not present
        component_data = context.component_data.setdefault(self.name, {})
        routing_data = component_data.setdefault("routing", {
            "history": [],
            "current_strategy": None,
            "fallback_chain": [],
            "error_count": 0
        })

        # Check if this is a routing request
        if self._is_routing_request(user_message):
            # Determine routing strategy
            # Pass the AgentProcessingContext to _determine_routing_strategy
            strategy, target_persona, reason = self._determine_routing_strategy(user_message, context)


            if strategy == RoutingStrategy.DIRECT:
                # Direct routing to a specific persona
                # Pass AgentProcessingContext to helper
                success, actual_target = await self._route_to_persona(conversation_id, current_persona, target_persona, context)

                # Add routing information to component_data
                routing_data["current_strategy"] = strategy
                routing_data["history"].append({
                    "timestamp": time.time(),
                    "strategy": strategy,
                    "source": current_persona,
                    "target": actual_target,
                    "success": success
                })

                # Add routing information to metadata
                context.metadata["routing_result"] = {
                    "success": success,
                    "strategy": strategy,
                    "target_persona": actual_target,
                    "message": f"I'll route your request to the {actual_target} persona." if success else f"I couldn't route your request to the {target_persona} persona."
                }
                if success:
                    context.response = f"I'll route your request to the {actual_target} persona."
                else:
                    context.response = f"I couldn't route your request to the {target_persona} persona."


                logger.info(f"Routed request from {current_persona} to {actual_target} using {strategy} strategy for conversation {conversation_id}")

            elif strategy == RoutingStrategy.CAPABILITY:
                # Capability-based routing
                required_capabilities = self._extract_required_capabilities(user_message)
                success, actual_target = await self._route_by_capability(conversation_id, current_persona, required_capabilities, context)

                # Add routing information to component_data
                routing_data["current_strategy"] = strategy
                routing_data["history"].append({
                    "timestamp": time.time(),
                    "strategy": strategy,
                    "source": current_persona,
                    "target": actual_target,
                    "capabilities": required_capabilities,
                    "success": success
                })

                # Add routing information to metadata
                context.metadata["routing_result"] = {
                    "success": success,
                    "strategy": strategy,
                    "target_persona": actual_target,
                    "capabilities": required_capabilities,
                    "message": f"I'll route your request to the {actual_target} persona, which has the required capabilities." if success else f"I couldn't find a persona with the required capabilities: {', '.join(required_capabilities)}."
                }
                if success:
                    context.response = f"I'll route your request to the {actual_target} persona, which has the required capabilities."
                else:
                    context.response = f"I couldn't find a persona with the required capabilities: {', '.join(required_capabilities)}."

                logger.info(f"Routed request from {current_persona} to {actual_target} based on capabilities {required_capabilities} for conversation {conversation_id}")

            elif strategy == RoutingStrategy.LOAD_BALANCED:
                # Load-balanced routing
                success, actual_target = await self._route_load_balanced(conversation_id, current_persona, context)

                # Add routing information to component_data
                routing_data["current_strategy"] = strategy
                routing_data["history"].append({
                    "timestamp": time.time(),
                    "strategy": strategy,
                    "source": current_persona,
                    "target": actual_target,
                    "success": success
                })

                # Add routing information to metadata
                context.metadata["routing_result"] = {
                    "success": success,
                    "strategy": strategy,
                    "target_persona": actual_target,
                    "message": f"I'll route your request to the {actual_target} persona based on current load." if success else f"I couldn't route your request due to high load on all personas."
                }
                if success:
                    context.response = f"I'll route your request to the {actual_target} persona based on current load."
                else:
                    context.response = "I couldn't route your request due to high load on all personas."


                logger.info(f"Routed request from {current_persona} to {actual_target} using load balancing for conversation {conversation_id}")

            elif strategy == RoutingStrategy.HIERARCHICAL:
                # Hierarchical routing
                hierarchy_path = self._determine_hierarchy_path(current_persona, target_persona)
                success, actual_path = await self._route_hierarchical(conversation_id, current_persona, hierarchy_path, context)

                # Add routing information to component_data
                routing_data["current_strategy"] = strategy
                routing_data["history"].append({
                    "timestamp": time.time(),
                    "strategy": strategy,
                    "source": current_persona,
                    "path": actual_path,
                    "success": success
                })

                # Add routing information to metadata
                context.metadata["routing_result"] = {
                    "success": success,
                    "strategy": strategy,
                    "path": actual_path,
                    "message": f"I'll route your request through the hierarchy: {' -> '.join(actual_path)}." if success else f"I couldn't route your request through the hierarchy."
                }
                if success:
                    context.response = f"I'll route your request through the hierarchy: {' -> '.join(actual_path)}."
                else:
                    context.response = "I couldn't route your request through the hierarchy."

                logger.info(f"Routed request from {current_persona} through hierarchy {actual_path} for conversation {conversation_id}")

            elif strategy == RoutingStrategy.FALLBACK:
                # Fallback routing
                fallback_chain = self.fallback_chains.get(target_persona, [])
                success, actual_target = await self._route_with_fallback(conversation_id, current_persona, target_persona, fallback_chain, context)

                # Add routing information to component_data
                routing_data["current_strategy"] = strategy
                routing_data["fallback_chain"] = fallback_chain
                routing_data["history"].append({
                    "timestamp": time.time(),
                    "strategy": strategy,
                    "source": current_persona,
                    "primary_target": target_persona,
                    "actual_target": actual_target,
                    "fallback_chain": fallback_chain,
                    "success": success
                })

                # Add routing information to metadata
                context.metadata["routing_result"] = {
                    "success": success,
                    "strategy": strategy,
                    "primary_target": target_persona,
                    "actual_target": actual_target,
                    "message": f"I'll route your request to {actual_target} as a fallback for {target_persona}." if success else f"I couldn't route your request to {target_persona} or any fallback personas."
                }
                if success:
                    context.response = f"I'll route your request to {actual_target} as a fallback for {target_persona}."
                else:
                    context.response = f"I couldn't route your request to {target_persona} or any fallback personas."

                logger.info(f"Routed request from {current_persona} to {actual_target} as fallback for {target_persona} in conversation {conversation_id}")

            else:
                # No suitable routing strategy found
                context.metadata["routing_result"] = {
                    "success": False,
                    "message": "I couldn't determine how to route your request. Could you provide more details?"
                }
                context.response = "I couldn't determine how to route your request. Could you provide more details?"
                logger.warning(f"No routing strategy found for request in conversation {conversation_id}")

        # Check for routing errors
        if "error" in context.metadata: # context.get("metadata", {})
            # Increment error count for the current persona
            self._increment_error_count(current_persona)

            # Check if we need to trigger fallback
            if self.enable_auto_fallback and self._should_trigger_fallback(current_persona):
                # Get fallback chain for the current persona
                fallback_chain = self.fallback_chains.get(current_persona, [])

                # Add fallback suggestion to metadata
                context.metadata["fallback_suggestion"] = {
                    "primary_persona": current_persona,
                    "fallback_chain": fallback_chain,
                    "message": f"I'm having trouble processing your request. Would you like me to try with a different persona?"
                }
                # Potentially set context.response here as well if this is a direct user-facing message

                logger.info(f"Suggested fallback for {current_persona} due to errors in conversation {conversation_id}")

        # Limit routing history size
        if len(routing_data["history"]) > self.max_routing_history:
            routing_data["history"] = routing_data["history"][-self.max_routing_history:]

        return context

    def _is_routing_request(self, message: str) -> bool:
        """
        Determine if the message is a request to route to another persona.

        Args:
            message: The user message.

        Returns:
            True if this is a routing request, False otherwise.
        """
        # Simple keyword-based detection
        routing_keywords = [
            "route to", "send to", "forward to", "transfer to",
            "redirect to", "pass to", "hand off to", "switch to"
        ]

        return any(keyword in message.lower() for keyword in routing_keywords)

    def _determine_routing_strategy(self, message: str, context: "AgentProcessingContext") -> Tuple[str, Optional[str], Optional[str]]:
        """
        Determine the appropriate routing strategy for a request.

        Args:
            message: The user message.
            context: The current AgentProcessingContext.

        Returns:
            A tuple containing the routing strategy, target persona, and reason.
        """
        message_lower = message.lower()
        current_persona = context.agent_config.id if context.agent_config else "unknown"

        # Check for direct routing to a specific persona
        # Map keywords to unified persona IDs
        persona_mapping = {
            "analyst": "analysis",
            "analysis": "analysis",
            "marketer": "marketing",
            "marketing": "marketing",
            "classifier": "classification",
            "classification": "classification",
            "concierge": "concierge"
        }
        target_persona = None

        for keyword, persona_id_val in persona_mapping.items(): # Renamed persona_id to avoid conflict
            if keyword in message_lower:
                target_persona = persona_id_val
                break

        if target_persona:
            # Check if the target persona is available
            if self._is_persona_available(target_persona):
                return RoutingStrategy.DIRECT, target_persona, f"explicit request to route to {target_persona}"
            else:
                # Target persona is unavailable, use fallback
                return RoutingStrategy.FALLBACK, target_persona, f"fallback for unavailable {target_persona}"

        # Check for capability-based routing
        if "can" in message_lower or "able to" in message_lower or "capability" in message_lower:
            return RoutingStrategy.CAPABILITY, None, "routing based on required capabilities"

        # Check for load-balanced routing
        if "any" in message_lower or "available" in message_lower or "whoever" in message_lower:
            return RoutingStrategy.LOAD_BALANCED, None, "load-balanced routing"

        # Check for hierarchical routing
        if "through" in message_lower or "via" in message_lower or "hierarchy" in message_lower:
            # Determine target based on message content
            for keyword, persona_id_val in persona_mapping.items(): # Renamed persona_id to avoid conflict
                if keyword in message_lower:
                    target_persona = persona_id_val
                    break

            return RoutingStrategy.HIERARCHICAL, target_persona, "hierarchical routing"

        # Default to capability-based routing
        return RoutingStrategy.CAPABILITY, None, "default to capability-based routing"

    async def _route_to_persona(self, conversation_id: str, source_persona: str, target_persona: str, context: "AgentProcessingContext") -> Tuple[bool, str]:
        """
        Route a request directly to a specific persona.

        Args:
            conversation_id: The ID of the conversation.
            source_persona: The source persona.
            target_persona: The target persona.
            context: The current AgentProcessingContext.

        Returns:
            A tuple containing a success flag and the actual target persona.
        """
        # Check if the target persona is available
        if not self._is_persona_available(target_persona):
            logger.warning(f"Target persona {target_persona} is not available for routing")

            # Try fallback if enabled
            if self.enable_auto_fallback:
                fallback_chain = self.fallback_chains.get(target_persona, [])
                for fallback in fallback_chain:
                    if self._is_persona_available(fallback):
                        # Update load for the fallback persona
                        self._update_persona_load(fallback, 1)

                        logger.info(f"Using fallback {fallback} for unavailable {target_persona}")
                        return True, fallback

            return False, target_persona

        # Update load for the target persona
        self._update_persona_load(target_persona, 1)

        return True, target_persona

    def _extract_required_capabilities(self, message: str) -> List[str]:
        """
        Extract required capabilities from a message.

        Args:
            message: The user message.

        Returns:
            A list of required capabilities.
        """
        # Simple rule-based extraction - could be enhanced with NLP
        message_lower = message.lower()
        capabilities = []

        # Check for data analysis capabilities
        analysis_keywords = ["analyze", "analysis", "data", "chart", "graph", "visualization"]
        if any(keyword in message_lower for keyword in analysis_keywords):
            capabilities.append("data_analysis")

        # Check for marketing capabilities
        marketing_keywords = ["marketing", "campaign", "content", "social media", "advertisement"]
        if any(keyword in message_lower for keyword in marketing_keywords):
            capabilities.append("marketing")

        # Check for classification capabilities
        classification_keywords = ["classify", "classification", "categorize", "sort", "group"]
        if any(keyword in message_lower for keyword in classification_keywords):
            capabilities.append("classification")

        # Check for coordination capabilities
        coordination_keywords = ["coordinate", "coordination", "manage", "oversee", "supervise"]
        if any(keyword in message_lower for keyword in coordination_keywords):
            capabilities.append("coordination")

        return capabilities

    async def _route_by_capability(self, conversation_id: str, source_persona: str, required_capabilities: List[str], context: "AgentProcessingContext") -> Tuple[bool, str]:
        """
        Route a request based on required capabilities.

        Args:
            conversation_id: The ID of the conversation.
            source_persona: The source persona.
            required_capabilities: The list of required capabilities.
            context: The current AgentProcessingContext.

        Returns:
            A tuple containing a success flag and the target persona.
        """
        if not required_capabilities:
            logger.warning(f"No required capabilities specified for capability-based routing")
            return False, ""

        # Find personas with the required capabilities
        matching_personas = set()
        for capability in required_capabilities:
            if capability in self.capability_index:
                matching_personas.update(self.capability_index[capability])

        # Remove the source persona from the matches
        if source_persona in matching_personas:
            matching_personas.remove(source_persona)

        # Filter for available personas
        available_matches = [p for p in matching_personas if self._is_persona_available(p)]

        if not available_matches:
            logger.warning(f"No available personas found with capabilities: {required_capabilities}")
            return False, ""

        # Sort by load (ascending)
        available_matches.sort(key=lambda p: self.persona_loads.get(p, 0))

        # Select the persona with the lowest load
        target_persona = available_matches[0]

        # Update load for the target persona
        self._update_persona_load(target_persona, 1)

        return True, target_persona

    async def _route_load_balanced(self, conversation_id: str, source_persona: str, context: "AgentProcessingContext") -> Tuple[bool, str]:
        """
        Route a request using load balancing.

        Args:
            conversation_id: The ID of the conversation.
            source_persona: The source persona.
            context: The current AgentProcessingContext.

        Returns:
            A tuple containing a success flag and the target persona.
        """
        # Get all available personas
        available_personas = [p for p in self.persona_loads.keys() if p != source_persona and self._is_persona_available(p)]

        if not available_personas:
            logger.warning(f"No available personas found for load-balanced routing")
            return False, ""

        # Sort by load (ascending)
        available_personas.sort(key=lambda p: self.persona_loads.get(p, 0))

        # Select the persona with the lowest load
        target_persona = available_personas[0]

        # Update load for the target persona
        self._update_persona_load(target_persona, 1)

        return True, target_persona

    def _determine_hierarchy_path(self, source_persona: str, target_persona: str) -> List[str]:
        """
        Determine the hierarchy path from source to target persona.

        Args:
            source_persona: The source persona.
            target_persona: The target persona.

        Returns:
            A list representing the hierarchy path.
        """
        # Simple hierarchy: concierge -> specialist -> assistant
        hierarchy = {
            "concierge": ["composable-analysis-ai", "composable-marketing-ai", "composable-classifier-ai"],
            "composable-analysis-ai": ["assistant_analyst"],
            "composable-marketing-ai": ["assistant_marketer"],
            "composable-classifier-ai": ["assistant_classifier"]
        }

        # If target is not specified, use a default path
        if not target_persona:
            if source_persona == "concierge":
                return ["concierge", "composable-analysis-ai"]
            elif source_persona in hierarchy:
                return [source_persona, hierarchy[source_persona][0]]
            else:
                return ["concierge"]

        # If source and target are directly connected
        if source_persona in hierarchy and target_persona in hierarchy[source_persona]:
            return [source_persona, target_persona]

        # If target is the parent of source
        for parent, children in hierarchy.items():
            if source_persona in children and parent == target_persona:
                return [source_persona, parent]

        # If source and target are siblings
        for parent, children in hierarchy.items():
            if source_persona in children and target_persona in children:
                return [source_persona, parent, target_persona]

        # Default path through concierge
        return [source_persona, "concierge", target_persona]

    async def _route_hierarchical(self, conversation_id: str, source_persona: str, hierarchy_path: List[str], context: "AgentProcessingContext") -> Tuple[bool, List[str]]:
        """
        Route a request through a hierarchy.

        Args:
            conversation_id: The ID of the conversation.
            source_persona: The source persona.
            hierarchy_path: The hierarchy path to follow.
            context: The current AgentProcessingContext.

        Returns:
            A tuple containing a success flag and the actual path followed.
        """
        if not hierarchy_path:
            logger.warning(f"No hierarchy path specified for hierarchical routing")
            return False, []

        # Validate the path
        valid_path = []
        current = source_persona

        for persona_val in hierarchy_path: # Renamed persona to avoid conflict
            if persona_val == current:
                continue

            if not self._is_persona_available(persona_val):
                logger.warning(f"Persona {persona_val} in hierarchy path is not available")
                break

            valid_path.append(persona_val)
            current = persona_val

            # Update load for this persona
            self._update_persona_load(persona_val, 1)

        if not valid_path:
            logger.warning(f"No valid hierarchy path found")
            return False, []

        # Prepend the source persona to the path
        full_path = [source_persona] + valid_path

        return True, full_path

    async def _route_with_fallback(self, conversation_id: str, source_persona: str, primary_target: str, fallback_chain: List[str], context: "AgentProcessingContext") -> Tuple[bool, str]:
        """
        Route a request with fallback options.

        Args:
            conversation_id: The ID of the conversation.
            source_persona: The source persona.
            primary_target: The primary target persona.
            fallback_chain: The chain of fallback personas.
            context: The current context.

        Returns:
            A tuple containing a success flag and the actual target persona.
        """
        # Try the primary target first
        if self._is_persona_available(primary_target):
            # Update load for the primary target
            self._update_persona_load(primary_target, 1)

            return True, primary_target

        # Try each fallback in order
        for fallback in fallback_chain:
            if fallback != source_persona and self._is_persona_available(fallback):
                # Update load for the fallback persona
                self._update_persona_load(fallback, 1)

                logger.info(f"Using fallback {fallback} for unavailable {primary_target}")
                return True, fallback

        logger.warning(f"No available fallbacks found for {primary_target}")
        return False, primary_target

    def _is_persona_available(self, persona_id: str) -> bool:
        """
        Check if a persona is available for routing.

        Args:
            persona_id: The ID of the persona.

        Returns:
            True if the persona is available, False otherwise.
        """
        # Check if the persona exists
        if persona_id not in self.persona_loads:
            return False

        # Check if the persona is overloaded
        if self.persona_loads[persona_id] >= self.load_threshold:
            return False

        # Check if the persona has too many errors
        if self.error_counts.get(persona_id, 0) >= self.error_threshold:
            return False

        return True

    def _update_persona_load(self, persona_id: str, delta: int) -> None:
        """
        Update the load counter for a persona.

        Args:
            persona_id: The ID of the persona.
            delta: The change in load (positive or negative).
        """
        if persona_id not in self.persona_loads:
            self.persona_loads[persona_id] = 0

        self.persona_loads[persona_id] += delta

        # Ensure load doesn't go below zero
        if self.persona_loads[persona_id] < 0:
            self.persona_loads[persona_id] = 0

    def _increment_error_count(self, persona_id: str) -> None:
        """
        Increment the error count for a persona.

        Args:
            persona_id: The ID of the persona.
        """
        if persona_id not in self.error_counts:
            self.error_counts[persona_id] = 0

        self.error_counts[persona_id] += 1

    def _should_trigger_fallback(self, persona_id: str) -> bool:
        """
        Determine if fallback should be triggered for a persona.

        Args:
            persona_id: The ID of the persona.

        Returns:
            True if fallback should be triggered, False otherwise.
        """
        return self.error_counts.get(persona_id, 0) >= self.error_threshold

    async def _initialize_capability_index(self) -> None:
        """Initialize the capability index by querying personas for their capabilities."""
        # Get all available agents
        personas = self.agent_factory.get_available_agents()

        # Try to get capabilities from configuration first
        config_capabilities = self.config.get("persona_capabilities", {})

        if config_capabilities:
            # Use configured capabilities
            for persona_id, capabilities in config_capabilities.items():
                for capability in capabilities:
                    if capability not in self.capability_index:
                        self.capability_index[capability] = set()
                    self.capability_index[capability].add(persona_id)
        else:
            # Dynamically discover capabilities from persona configurations
            for persona_id in personas:
                persona_config = self.agent_factory.agent_configs.get(persona_id)
                if persona_config and "capabilities" in persona_config:
                    capabilities = persona_config["capabilities"]
                else:
                    # Infer capabilities from persona name/type
                    capabilities = self._infer_capabilities_from_name(persona_id)

                for capability in capabilities:
                    if capability not in self.capability_index:
                        self.capability_index[capability] = set()
                    self.capability_index[capability].add(persona_id)

        logger.info(f"Initialized capability index with {len(self.capability_index)} capabilities")

    def _generate_dynamic_fallback_chains(self, available_personas: List[str]) -> Dict[str, List[str]]:
        """Generate dynamic fallback chains based on available personas."""
        fallback_chains = {}

        # Ensure concierge-agent is always available as final fallback
        concierge = "concierge-agent"

        for persona in available_personas:
            # Create fallback chain: other personas + concierge
            fallback_list = [p for p in available_personas if p != persona]

            # Ensure concierge is last if it exists
            if concierge in fallback_list:
                fallback_list.remove(concierge)
                fallback_list.append(concierge)
            elif concierge not in available_personas and persona != concierge:
                fallback_list.append(concierge)

            fallback_chains[persona] = fallback_list

        logger.info(f"Generated dynamic fallback chains for {len(fallback_chains)} personas")
        return fallback_chains

    def _infer_capabilities_from_name(self, persona_id: str) -> List[str]:
        """Infer capabilities from persona name/ID."""
        capabilities = []

        # Basic inference based on common patterns
        if "analyst" in persona_id or "analysis" in persona_id:
            capabilities.extend(["data_analysis", "visualization", "statistics"])
        if "market" in persona_id:
            capabilities.extend(["marketing", "content_creation", "campaign_planning"])
        if "classif" in persona_id:
            capabilities.extend(["classification", "categorization", "organization"])
        if "concierge" in persona_id:
            capabilities.extend(["coordination", "guidance", "recommendation"])
        if "data" in persona_id:
            capabilities.extend(["data_processing", "file_handling"])

        # Default capabilities if none inferred
        if not capabilities:
            capabilities = ["general_assistance"]

        return capabilities

    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities provided by this component.
        """
        return self.config.get("capabilities", [
            "advanced_routing",
            "fallback_mechanisms",
            "load_balancing",
            "hierarchical_routing",
            "capability_based_routing"
        ])
