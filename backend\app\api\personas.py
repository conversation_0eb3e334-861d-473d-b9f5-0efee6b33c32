"""
Persona API endpoints for the Datagenius backend.

This module provides API endpoints for AI personas.
"""

import logging
from typing import Optional, Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

from ..models.persona import PersonaBase, PersonaListResponse, PersonaResponse, UserPersonaAccessResponse
from ..models.auth import User
from ..dependencies import get_persona_repository, get_persona_version_repository
from ..repositories.persona_repository import PersonaRepository
from ..repositories.persona_version_repository import PersonaVersionRepository
from ..auth import get_current_active_user
from ..services import persona_service

# Define purchase request model
class PurchasePersonaRequest(BaseModel):
    payment_method: str = "credit_card"

# Import the agent registry
import sys
import os

# Import agent factory using centralized import utility
from ..utils.import_utils import import_agent_factory

agent_factory = import_agent_factory()

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/personas", tags=["Personas"])


@router.get("", response_model=PersonaListResponse)
async def list_personas(
    industry: Optional[str] = None,
    persona_repo: PersonaRepository = Depends(get_persona_repository),
    current_user: User = Depends(get_current_active_user)
):
    """
    List all available personas.

    Args:
        industry: Optional filter by industry
        persona_repo: Persona repository
        current_user: Current authenticated user

    Returns:
        List of available personas
    """
    logger.info(f"User {current_user.id} requested list of personas")

    # Get personas from database
    logger.info("Getting personas from database")
    db_personas = persona_repo.get_personas(skip=0, limit=100, industry=industry)
    logger.info(f"Found {len(db_personas)} personas in database")

    # Get purchased persona IDs
    from ..database import get_db
    db = next(get_db())
    purchased_persona_ids = persona_service.get_user_purchased_personas(db, current_user.id)
    logger.info(f"User {current_user.id} has purchased {len(purchased_persona_ids)} personas: {purchased_persona_ids}")

    # Convert database personas to PersonaBase objects
    personas = []
    for db_persona in db_personas:
        # Get capabilities from agent factory if available
        capabilities = []
        agent_class = agent_factory.agent_classes.get(db_persona.id)
        if agent_class:
            try:
                agent = agent_class()
                capabilities = await agent.get_capabilities()
            except Exception as e:
                logger.error(f"Error getting capabilities for persona {db_persona.id}: {e}")

        # Check if the persona is purchased
        is_purchased = db_persona.id in purchased_persona_ids

        # Add persona to list
        personas.append(PersonaBase(
            id=db_persona.id,
            name=db_persona.name,
            description=db_persona.description,
            industry=db_persona.industry,
            skills=db_persona.skills,
            rating=db_persona.rating,
            review_count=db_persona.review_count,
            image_url=db_persona.image_url,
            is_available=db_persona.is_active and (is_purchased or db_persona.id in ["concierge", "concierge-agent"]),  # Available if active and (purchased or concierge)
            capabilities=capabilities,
            price=db_persona.price,
            provider=db_persona.provider,
            model=db_persona.model,
            is_active=db_persona.is_active,
            age_restriction=db_persona.age_restriction,
            is_purchased=is_purchased  # Add purchased status
        ))

    # If no personas found in database, fall back to registry
    if not personas:
        logger.info("No personas found in database, falling back to registry")

        # Get all registered persona IDs from the agent factory
        persona_ids = agent_factory.get_available_agents()
        logger.info(f"Found {len(persona_ids)} personas in agent factory")

        # Create a list of PersonaBase objects
        for persona_id in persona_ids:
            # Get configuration for the persona
            config = agent_factory.agent_configs.get(persona_id)

            # Check if the persona is purchased
            is_purchased = persona_id in purchased_persona_ids

            if config:
                # Use configuration data if available
                personas.append(PersonaBase(
                    id=persona_id,
                    name=config.get("name", persona_id),
                    description=config.get("description", "No description available"),
                    industry=config.get("industry", "Technology"),
                    skills=config.get("skills", []),
                    rating=config.get("rating", 4.7),
                    review_count=config.get("review_count", 100),
                    image_url=config.get("image_url", "/placeholder.svg"),
                    is_available=config.get("is_active", True) and (is_purchased or persona_id in ["concierge", "concierge-agent"]),  # Available if active and (purchased or concierge)
                    capabilities=config.get("capabilities", []),
                    price=config.get("price", 10.0),
                    provider=config.get("provider", "groq"),
                    model=config.get("model"),
                    is_active=config.get("is_active", True),
                    age_restriction=config.get("age_restriction", 0),
                    is_purchased=is_purchased  # Add purchased status
                ))
            else:
                # Fall back to creating a temporary instance if no configuration is available
                agent_class = agent_factory.agent_classes.get(persona_id)
                if agent_class:
                    # Create a temporary instance to get capabilities
                    agent = agent_class()
                    capabilities = await agent.get_capabilities()

                    # Map persona IDs to more user-friendly names and industries
                    name = agent_class.__name__.replace("Agent", " AI")

                    # Determine industry based on capabilities or agent type
                    industry_mapping = {
                        "marketing-ai": "Marketing",
                        "classifier-ai": "Technology",
                        "analysis-ai": "Data Science"
                    }

                    # Get skills based on capabilities
                    skills = []
                    if "marketing-ai" in persona_id:
                        skills = ["Marketing Strategy", "Campaign Planning", "Content Creation"]
                    elif "classifier-ai" in persona_id:
                        skills = ["Text Classification", "Document Organization", "Content Analysis"]
                    elif "analysis-ai" in persona_id:
                        skills = ["Data Cleaning", "Data Visualization", "Statistical Analysis"]

                    # Add persona info to the list
                    personas.append(PersonaBase(
                        id=persona_id,
                        name=name,
                        description=agent_class.__doc__ or "No description available",
                        industry=industry_mapping.get(persona_id, "Technology"),
                        skills=skills,
                        rating=4.7,  # Default rating
                        review_count=100,  # Default review count
                        image_url="/placeholder.svg",
                        is_available=is_purchased or persona_id in ["concierge", "concierge-agent"],  # Available if purchased or concierge
                        capabilities=capabilities,
                        is_purchased=is_purchased  # Add purchased status
                    ))

    # Filter by industry if specified
    if industry and personas:
        personas = [p for p in personas if p.industry == industry]

    logger.info(f"Returning {len(personas)} personas")
    return PersonaListResponse(personas=personas)


# Note: The /personas/purchased endpoint is now defined in main.py to avoid routing conflicts


@router.get("/access/{persona_id}", response_model=UserPersonaAccessResponse)
async def check_persona_access(
    persona_id: str,
    persona_repo: PersonaRepository = Depends(get_persona_repository),
    current_user: User = Depends(get_current_active_user)
):
    """
    Check if a user has access to a specific persona.

    Args:
        persona_id: ID of the persona
        persona_repo: Persona repository
        current_user: Current authenticated user

    Returns:
        Access information
    """
    logger.info(f"User {current_user.id} checking access to persona {persona_id}")

    # Check if the persona exists in the agent factory
    config = agent_factory.agent_configs.get(persona_id)
    agent_class = agent_factory.agent_classes.get(persona_id)

    # Also check if the persona exists in the database
    from ..database import get_db
    db = next(get_db())
    db_persona = persona_service.get_persona_by_id(db, persona_id)

    if not config and not agent_class and not db_persona:
        logger.error(f"Persona {persona_id} not found")
        raise HTTPException(status_code=404, detail=f"Persona {persona_id} not found")

    # Check if the user has purchased this persona using the service
    is_purchased = persona_service.has_user_purchased_persona(db, current_user.id, persona_id)

    # For now, we'll consider a persona available if it's either purchased or globally available
    # In a real app, you might have more complex logic here
    has_access = is_purchased

    return {
        "has_access": has_access,
        "is_purchased": is_purchased
    }


@router.get("/{persona_id}", response_model=PersonaResponse)
async def get_persona(
    persona_id: str,
    persona_repo: PersonaRepository = Depends(get_persona_repository),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get a specific persona.

    Args:
        persona_id: ID of the persona
        persona_repo: Persona repository
        current_user: Current authenticated user

    Returns:
        Persona information
    """
    logger.info(f"User {current_user.id} requested persona {persona_id}")

    # First, try to get the persona from the database
    db_persona = persona_repo.get_persona(persona_id)

    if db_persona:
        logger.info(f"Found persona {persona_id} in database")

        # Get capabilities from agent factory if available
        capabilities = []
        agent_class = agent_factory.agent_classes.get(persona_id)
        if agent_class:
            try:
                agent = agent_class()
                capabilities = await agent.get_capabilities()
            except Exception as e:
                logger.error(f"Error getting capabilities for persona {persona_id}: {e}")

        # Create persona object from database
        persona = PersonaBase(
            id=db_persona.id,
            name=db_persona.name,
            description=db_persona.description,
            industry=db_persona.industry,
            skills=db_persona.skills,
            rating=db_persona.rating,
            review_count=db_persona.review_count,
            image_url=db_persona.image_url,
            is_available=db_persona.is_active,
            capabilities=capabilities,
            price=db_persona.price,
            provider=db_persona.provider,
            model=db_persona.model,
            is_active=db_persona.is_active,
            age_restriction=db_persona.age_restriction
        )
    else:
        logger.info(f"Persona {persona_id} not found in database, checking registry")

        # Get configuration for the persona from agent factory
        config = agent_factory.agent_configs.get(persona_id)

        if config:
            # Use configuration data if available
            logger.info(f"Found persona {persona_id} in registry configuration")
            persona = PersonaBase(
                id=persona_id,
                name=config.get("name", persona_id),
                description=config.get("description", "No description available"),
                industry=config.get("industry", "Technology"),
                skills=config.get("skills", []),
                rating=config.get("rating", 4.7),
                review_count=config.get("review_count", 100),
                image_url=config.get("image_url", "/placeholder.svg"),
                is_available=config.get("is_active", True),
                capabilities=config.get("capabilities", []),
                price=config.get("price", 10.0),
                provider=config.get("provider", "groq"),
                model=config.get("model"),
                is_active=config.get("is_active", True),
                age_restriction=config.get("age_restriction", 0)
            )
        else:
            # Fall back to creating a temporary instance if no configuration is available
            agent_class = agent_factory.agent_classes.get(persona_id)
            if not agent_class:
                logger.error(f"Persona {persona_id} not found in database or agent factory")
                raise HTTPException(status_code=404, detail=f"Persona {persona_id} not found")

            logger.info(f"Creating temporary persona for {persona_id} from agent class")
            # Create a temporary instance to get capabilities
            agent = agent_class()
            capabilities = await agent.get_capabilities()

            # Map persona IDs to more user-friendly names and industries
            name = agent_class.__name__.replace("Agent", " AI")

            # Determine industry based on capabilities or agent type
            industry_mapping = {
                "marketing-ai": "Marketing",
                "classifier-ai": "Technology",
                "analysis-ai": "Data Science"
            }

            # Get skills based on capabilities
            skills = []
            if "marketing-ai" in persona_id:
                skills = ["Marketing Strategy", "Campaign Planning", "Content Creation"]
            elif "classifier-ai" in persona_id:
                skills = ["Text Classification", "Document Organization", "Content Analysis"]
            elif "analysis-ai" in persona_id:
                skills = ["Data Cleaning", "Data Visualization", "Statistical Analysis"]

            # Create persona object
            persona = PersonaBase(
                id=persona_id,
                name=name,
                description=agent_class.__doc__ or "No description available",
                industry=industry_mapping.get(persona_id, "Technology"),
                skills=skills,
                rating=4.7,  # Default rating
                review_count=100,  # Default review count
                image_url="/placeholder.svg",
                is_available=True,
                capabilities=capabilities
            )

    return PersonaResponse(persona=persona)


@router.post("/{persona_id}/purchase", response_model=Dict[str, Any])
async def purchase_persona(
    persona_id: str,
    request: PurchasePersonaRequest,
    persona_repo: PersonaRepository = Depends(get_persona_repository),
    current_user: User = Depends(get_current_active_user)
):
    """
    Purchase a persona.

    Args:
        persona_id: ID of the persona to purchase
        request: Purchase request
        persona_repo: Persona repository
        current_user: Current authenticated user

    Returns:
        Purchase information
    """
    logger.info(f"User {current_user.id} purchasing persona {persona_id}")

    try:
        # Purchase the persona using the service
        from ..database import get_db
        db = next(get_db())
        result = persona_service.purchase_persona(
            db,
            current_user.id,
            persona_id,
            request.payment_method
        )
        logger.info(f"Purchase result for user {current_user.id}, persona {persona_id}: {result}")
        return result
    except ValueError as e:
        logger.error(f"Error purchasing persona {persona_id} for user {current_user.id}: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error purchasing persona {persona_id} for user {current_user.id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="An error occurred while processing your purchase")
