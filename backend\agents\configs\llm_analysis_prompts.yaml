# LLM Analysis Prompts Configuration
# This file contains structured prompts for the concierge agent's LLM-based analysis system

# Main analysis prompt template
analysis_prompt:
  system_message: |
    You are the Datagenius Concierge AI, an intelligent assistant that analyzes user requests
    and recommends the most suitable AI personas for their needs.

    Your role is to:
    1. Understand what the user wants to accomplish
    2. Extract relevant entities and context
    3. Recommend the most appropriate AI personas
    4. Assess task complexity and data requirements
    5. Provide clear reasoning for your recommendations
    6. When intent is unclear, provide reasoning that supports conversational follow-up

    For general questions or unclear intents, focus on understanding the user's underlying needs
    and provide reasoning that can help generate natural, conversational responses.

    Always respond with valid JSON that matches the expected schema.

    Intent Classification Guidelines:
    - "persona_request": User explicitly wants recommendations for specific tasks (e.g., "recommend a persona for marketing", "which agent should I use for data analysis", "help me find the right persona")
    - "persona_query": User wants to browse/list available personas (e.g., "what personas are available", "show me all agents", "what can you do")
    - "general_question": Everything else - regular conversations, task requests, domain-specific words without explicit persona requests
    - Use "persona_request" ONLY when user explicitly asks for persona recommendations or agent selection help
    - Use "persona_query" when user wants to explore available options
    - Use "general_question" for all other interactions, including domain-specific requests like "analyze this data" or "create marketing content"

  user_prompt_template: |
    Available AI Personas:
    {personas_description}

    User's Request: "{message}"

    {conversation_context}

    Analyze the user's request and respond with a JSON object containing:
    {{
        "intent_type": "one of: persona_request, persona_query, general_question",
        "confidence": 0.0-1.0,
        "entities": {{
            "file_type": "if mentioned (csv, excel, pdf, etc.) or null",
            "industry": "if mentioned (finance, healthcare, etc.) or null",
            "task_type": "if mentioned (analysis, visualization, etc.) or null",
            "urgency": "detected urgency level (low, medium, high) or null",
            "data_size": "estimated data size (small, medium, large) or null"
        }},
        "suggested_personas": ["list of 1-3 most relevant persona IDs"],
        "requires_data": true/false,
        "complexity_score": 0.0-1.0,
        "reasoning": "detailed explanation of your analysis and recommendations",
        "follow_up_questions": ["optional list of clarifying questions"],
        "estimated_time": "optional time estimate (e.g., '5-10 minutes', '1-2 hours')"
    }}

    Guidelines:
    - Focus on understanding the user's actual goals, not just keywords
    - Consider conversation context when making recommendations
    - Be specific in your reasoning, especially for general questions
    - For unclear intents, provide reasoning that explains what the user might be looking for
    - Suggest follow-up questions if the request is unclear
    - Estimate realistic complexity and time requirements
    - When classifying as "general_question", include detailed reasoning about potential user needs

# Persona descriptions for context
persona_descriptions:
  concierge:
    name: "Datagenius Concierge"
    description: "Your helpful guide to Datagenius. Helps users find the right AI persona and navigate the platform."
    capabilities:
      - "Persona recommendation"
      - "Platform guidance"
      - "Workflow coordination"
      - "General assistance"
    best_for:
      - "Getting started with Datagenius"
      - "Finding the right AI persona"
      - "General questions and guidance"

  composable-analyst:
    name: "Composable Analyst"
    description: "Expert in data analysis, visualization, and statistical insights. Handles CSV, Excel files and generates comprehensive reports."
    capabilities:
      - "Data analysis and visualization"
      - "Statistical analysis"
      - "Report generation"
      - "Data cleaning and preprocessing"
      - "Trend analysis and forecasting"
    best_for:
      - "Analyzing CSV/Excel data"
      - "Creating charts and visualizations"
      - "Statistical analysis"
      - "Data insights and reporting"

  composable-marketer:
    name: "Composable Marketer"
    description: "Specialist in marketing content creation, campaign strategies, and brand development."
    capabilities:
      - "Marketing strategy development"
      - "Content creation"
      - "Campaign planning"
      - "SEO optimization"
      - "Social media content"
      - "Brand messaging"
    best_for:
      - "Creating marketing campaigns"
      - "Writing marketing content"
      - "SEO optimization"
      - "Brand strategy development"

  composable-classifier:
    name: "Composable Classifier"
    description: "Expert in categorizing, organizing, and classifying content and data."
    capabilities:
      - "Text classification"
      - "Content categorization"
      - "Data organization"
      - "Tagging and labeling"
      - "Pattern recognition"
    best_for:
      - "Classifying documents or data"
      - "Organizing content"
      - "Categorizing information"
      - "Pattern identification"

  data-assistant:
    name: "Data Assistant"
    description: "Helpful assistant for data-related tasks, file processing, and data management."
    capabilities:
      - "File processing"
      - "Data management"
      - "Data format conversion"
      - "Basic data analysis"
    best_for:
      - "File upload and processing"
      - "Data format conversion"
      - "Basic data tasks"

# Intent-specific prompt variations
intent_prompts:
  persona_request:
    focus: "The user is explicitly asking for persona recommendations based on their specific task or need. Focus on understanding their requirements and matching them to the most suitable personas."

  persona_query:
    focus: "The user wants to see available personas or is asking general questions about what personas exist. They want to browse or list personas rather than get specific recommendations."

  general_question:
    focus: "The user has a general question, task request, or conversation that doesn't explicitly ask for persona recommendations. Focus on understanding what they might need and provide reasoning that can be used for conversational follow-up. Do NOT automatically recommend personas unless they explicitly ask for agent/persona help."

  data_help:
    focus: "The user is asking for help with data-related tasks, file processing, or data management. Focus on understanding their data needs and provide guidance on data handling, analysis, or processing."

  analysis_request:
    focus: "The user is requesting data analysis, statistical insights, or visualization. Focus on understanding the type of analysis needed and the data they want to work with."

  marketing_request:
    focus: "The user is asking for marketing-related assistance, content creation, or campaign development. Focus on understanding their marketing goals and target audience."

  classification_request:
    focus: "The user wants to classify, categorize, or organize content or data. Focus on understanding what they want to classify and how they want it organized."

# Fallback responses for different scenarios
fallback_responses:
  llm_error:
    intent_type: "general_question"
    confidence: 0.5
    reasoning: "LLM analysis failed, using fallback response"

  parsing_error:
    intent_type: "general_question"
    confidence: 0.3
    reasoning: "Could not parse LLM response, using basic fallback"

  timeout_error:
    intent_type: "general_question"
    confidence: 0.4
    reasoning: "LLM request timed out, using fallback response"

# Configuration for different analysis modes
analysis_modes:
  quick:
    temperature: 0.1
    max_tokens: 500
    timeout: 15
    description: "Fast analysis for simple requests"

  detailed:
    temperature: 0.2
    max_tokens: 1000
    timeout: 30
    description: "Comprehensive analysis for complex requests"

  creative:
    temperature: 0.5
    max_tokens: 800
    timeout: 25
    description: "More creative analysis for open-ended requests"

# Validation rules
validation_rules:
  min_confidence: 0.1
  max_personas: 3
  required_reasoning_length: 20
  max_follow_up_questions: 5
