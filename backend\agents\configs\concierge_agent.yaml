# Consolidated Configuration for the Concierge Agent

name: "concierge"
description: "Advanced Datagenius Concierge with LLM-based understanding, robust error handling, and intelligent routing"
system_prompt_template: "concierge_advanced.prompt"
version: "3.0.0"

# Core Configuration
recommendation_threshold: 0.7
max_recommendations: 5
consider_user_history: true
enable_context_compression: true
enable_performance_monitoring: true

# LLM Configuration
llm_config:
  provider: "groq"
  model: "llama-3.1-8b-instant"
  temperature: 0.1
  max_tokens: 2000
  timeout: 30
  retry_attempts: 3
  retry_delay: 1.0

# Conversation Management
conversation:
  max_context_length: 4000
  context_ttl: 3600  # 1 hour
  max_conversation_states: 1000
  cleanup_interval: 300  # 5 minutes
  enable_context_compression: true
  auto_summarize_threshold: 10  # messages

# Security Configuration
security:
  enable_input_validation: true
  max_message_length: 10000
  rate_limit_per_user: 100  # per hour
  enable_content_filtering: true
  blocked_keywords: []
  enable_audit_logging: true

# Error Handling
error_handling:
  max_retries: 3
  retry_delay: 1.0
  fallback_responses:
    llm_error: "I'm having trouble understanding right now. Could you rephrase your request?"
    validation_error: "I need some clarification. Could you provide more details?"
    persona_not_available: "That specialist isn't available right now. Let me suggest some alternatives."
    timeout_error: "I'm taking longer than usual. Please try again."
  enable_graceful_degradation: true

# Persona Mapping and Routing
persona_routing:
  enable_intelligent_routing: true
  routing_confidence_threshold: 0.8
  fallback_persona: "concierge"

  # Keyword-based routing rules
  routing_rules:
    marketing:
      keywords: ["marketing", "campaign", "promotion", "brand", "advertising", "social media", "content", "seo"]
      target_persona: "composable-marketer"
      confidence_boost: 0.2

    analysis:
      keywords: ["analyze", "analysis", "data", "chart", "graph", "statistics", "report", "insights", "trends"]
      target_persona: "composable-analyst"
      confidence_boost: 0.2

    classification:
      keywords: ["classify", "categorize", "label", "tag", "organize", "sort", "group"]
      target_persona: "composable-classifier"
      confidence_boost: 0.15

    general_data:
      keywords: ["data", "file", "upload", "csv", "excel", "help"]
      target_persona: "data-assistant"
      confidence_boost: 0.1

# Persona Descriptions for LLM Context
personas:
  concierge:
    name: "Datagenius Concierge"
    description: "Your intelligent guide to the Datagenius platform"
    capabilities: ["guidance", "persona_recommendation", "platform_navigation", "general_assistance"]
    keywords: ["help", "guide", "concierge", "assistant"]
    priority: 1

  composable-marketer:
    name: "Marketing Specialist"
    description: "Expert in marketing strategy, campaign planning, and content creation"
    capabilities: ["marketing_strategy", "campaign_planning", "content_creation", "seo_optimization"]
    keywords: ["marketing", "campaign", "promotion", "brand", "advertising"]
    priority: 2

  composable-analyst:
    name: "Data Analyst"
    description: "Specialist in data analysis, visualization, and statistical insights"
    capabilities: ["data_analysis", "visualization", "statistical_analysis", "reporting"]
    keywords: ["analyze", "data", "chart", "graph", "statistics", "insights"]
    priority: 2

  composable-classifier:
    name: "Classification Specialist"
    description: "Expert in data classification and content categorization"
    capabilities: ["data_classification", "content_categorization", "tagging", "organization"]
    keywords: ["classify", "categorize", "label", "tag", "organize"]
    priority: 3

  data-assistant:
    name: "Data Assistant"
    description: "General data helper for file management and basic analysis"
    capabilities: ["file_management", "data_help", "basic_analysis", "general_assistance"]
    keywords: ["data", "file", "upload", "help", "assistant"]
    priority: 4

# Validation Rules
validation:
  min_confidence: 0.1
  max_personas: 5
  required_reasoning_length: 10
  max_follow_up_questions: 3
  allowed_intent_types: ["persona_request", "persona_query", "general_question", "data_help"]

# Performance Monitoring
monitoring:
  enable_metrics: true
  track_response_times: true
  track_error_rates: true
  track_user_satisfaction: true
  metrics_retention_days: 30

# Components Configuration
components:
  - type: "enhanced_context_manager"
    name: "EnhancedContextManager"
    config:
      context_ttl: 3600
      max_history_size: 15
      sync_interval: 300
      enable_entity_tracking: true
      enable_context_versioning: true
      enable_compression: true

  - type: "persona_recommender"
    name: "PersonaRecommender"
    config:
      recommendation_threshold: 0.7
      max_recommendations: 5
      consider_user_history: true
      enable_confidence_boosting: true
      cache_ttl: 300

  - type: "data_attachment_assistant"
    name: "DataAttachmentAssistant"
    config:
      supported_file_types: ["csv", "xlsx", "pdf", "docx", "txt", "json"]
      max_file_size: 100MB
      enable_preview: true
      auto_detect_format: true

  - type: "persona_routing"
    name: "PersonaRouter"
    config:
      routing_threshold: 0.8
      enable_fallback: true
      max_routing_attempts: 3
      enable_load_balancing: false

  - type: "conversation_state_manager"
    name: "ConversationStateManager"
    config:
      max_states: 1000
      state_ttl: 3600
      enable_persistence: true
      auto_cleanup: true

  - type: "error_handler"
    name: "ErrorHandler"
    config:
      enable_graceful_degradation: true
      max_retries: 3
      retry_delay: 1.0
      enable_fallback_responses: true

  - type: "performance_monitor"
    name: "PerformanceMonitor"
    config:
      enable_metrics: true
      track_response_times: true
      track_memory_usage: true
      alert_thresholds:
        response_time: 5000  # ms
        error_rate: 0.1  # 10%
        memory_usage: 0.8  # 80%

  - type: "mcp_server"
    name: "MCPServer"
    config:
      enable_auto_tools: true
      tool_selection_threshold: 0.5
      max_tool_calls: 10
      tool_timeout: 30
      available_tools:
        - "persona_marketplace"
        - "data_access"
        - "conversation_management"
        - "user_feedback"

# Capabilities provided by this agent
capabilities:
  - "intelligent_guidance"
  - "llm_based_understanding"
  - "persona_recommendation"
  - "intelligent_routing"
  - "context_management"
  - "error_handling"
  - "performance_monitoring"
  - "conversation_persistence"
  - "user_preference_learning"
  - "multi_modal_support"
  - "real_time_adaptation"

# Feature Flags
features:
  enable_advanced_nlp: true
  enable_sentiment_analysis: true
  enable_user_profiling: true
  enable_predictive_routing: true
  enable_conversation_summarization: true
  enable_multi_language_support: false
  enable_voice_interaction: false
  enable_visual_elements: true

# Integration Settings
integrations:
  database:
    enable_conversation_storage: true
    enable_user_preferences: true
    enable_analytics: true

  external_apis:
    enable_marketplace_integration: true
    enable_user_management: true
    enable_notification_service: true

  monitoring:
    enable_logging: true
    enable_metrics: true
    enable_alerting: true

# Deployment Configuration
deployment:
  environment: "production"
  scaling:
    min_instances: 1
    max_instances: 5
    target_cpu_utilization: 70

  health_checks:
    enabled: true
    interval: 30  # seconds
    timeout: 10   # seconds
    retries: 3

  logging:
    level: "INFO"
    format: "json"
    enable_structured_logging: true
  - type: "enhanced_context_manager"
    name: "EnhancedContextManager"
    config:
      context_ttl: 3600
      max_history_size: 15
      sync_interval: 300
      enable_entity_tracking: true
      enable_context_versioning: true
      enable_compression: true

  - type: "persona_recommender"
    name: "PersonaRecommender"
    config:
      recommendation_threshold: 0.7
      max_recommendations: 5
      consider_user_history: true
      enable_confidence_boosting: true
      cache_ttl: 300

  - type: "data_attachment_assistant"
    name: "DataAttachmentAssistant"
    config:
      supported_file_types: ["csv", "xlsx", "pdf", "docx", "txt", "json"]
      max_file_size: 100MB
      enable_preview: true
      auto_detect_format: true

  - type: "persona_routing"
    name: "PersonaRouter"
    config:
      routing_threshold: 0.7

  - type: "concierge_state_tracker"
    name: "StateTracker"
    config:
      state_ttl: 3600  # 1 hour TTL for states
      max_states: 1000
      cleanup_interval: 300  # 5 minutes

  - type: "persona_coordinator"
    name: "PersonaCoordinator"
    config:
      enable_auto_coordination: true
      coordination_threshold: 0.7
      max_coordination_depth: 3
      coordination_ttl: 3600  # 1 hour TTL for coordination data

  - type: "bidirectional_communication"
    name: "BidirectionalCommunication"
    config:
      enable_auto_callbacks: true
      callback_threshold: 0.7
      message_ttl: 3600  # 1 hour TTL for messages

  - type: "team_manager"
    name: "TeamManager"
    config:
      team_ttl: 3600  # 1 hour TTL for teams
      max_team_size: 5
      enable_auto_team_formation: true
      team_formation_threshold: 0.7
      team_templates:
        analysis_team:
          roles:
            manager: 1
            specialist: 2
            assistant: 1
          hierarchy:
            manager: [specialist, assistant]
            specialist: []
            assistant: []
        marketing_team:
          roles:
            manager: 1
            strategist: 1
            executor: 2
          hierarchy:
            manager: [strategist, executor]
            strategist: [executor]
            executor: []

  - type: "advanced_router"
    name: "AdvancedRouter"
    config:
      routing_ttl: 3600  # 1 hour TTL for routing
      max_routing_history: 100
      load_threshold: 10
      error_threshold: 3
      enable_auto_fallback: true
      fallback_chains:
        composable-analysis-ai: [composable-marketing-ai, composable-classifier-ai, concierge]
        composable-marketing-ai: [composable-analysis-ai, composable-classifier-ai, concierge]
        composable-classifier-ai: [composable-analysis-ai, composable-marketing-ai, concierge]
        concierge: [composable-analysis-ai, composable-marketing-ai, composable-classifier-ai]

  - type: "role_assignment"
    name: "RoleAssignment"
    config:
      assignment_ttl: 3600  # 1 hour TTL for role assignments
      enable_auto_assignment: true
      assignment_threshold: 0.7
      role_capabilities:
        analyst: [data_analysis, pattern_recognition, insight_generation]
        strategist: [planning, goal_setting, strategic_thinking]
        executor: [implementation, task_execution, action_taking]
        reviewer: [quality_control, verification, feedback]
        coordinator: [coordination, communication, delegation]
        specialist: [domain_expertise, specialized_knowledge, deep_focus]
        generalist: [broad_knowledge, adaptability, versatility]
      persona_affinities:
        composable-analysis-ai: [analyst, specialist]
        composable-marketing-ai: [strategist, executor]
        composable-classifier-ai: [specialist, analyst]
        concierge: [coordinator, generalist]

  - type: "mcp_server"
    name: "MCPServer"
    config:
      enable_auto_tools: true
      tool_selection_threshold: 0.5  # Lowered to allow more autonomous tool usage
      max_tool_calls: 10
      tool_timeout: 30  # 30 seconds timeout for tool calls

# Capabilities provided by this agent
capabilities:
  - "guidance"
  - "persona_recommendation"
  - "data_assistance"
  - "persona_routing"
  - "enhanced_context_management"
  - "persona_coordination"
  - "bidirectional_communication"
  - "handoff_management"
  - "collaboration_management"
  - "team_management"
  - "hierarchical_teams"
  - "role_assignment"
  - "advanced_routing"
  - "fallback_mechanisms"
  - "specialized_roles"
  - "mcp_tools"

# Default model configuration (fallback only - database configuration takes priority)
# These values are used only if database configuration is not available
model_config:
  provider: "groq" # Fallback provider
  model: "llama-3.1-8b-instant" # Fallback model
  temperature: 0.7
  max_tokens: 1500

# Agent-specific settings (if any)
settings:
  greeting_message: "Hello! I'm the Datagenius Concierge. How can I help you today? I can answer questions, provide information, help you find the right AI persona for your task, or assist you with your data."
