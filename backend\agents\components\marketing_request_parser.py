"""
Marketing request parser component for the Datagenius agent system.

This module provides a component for parsing marketing-related requests,
extracting key information, and determining the appropriate marketing task.
"""

import logging
import re
import sys
from typing import Dict, Any, Union
from pathlib import Path

# Add backend root to path for import_utils
backend_root = Path(__file__).parent.parent.parent
if str(backend_root) not in sys.path:
    sys.path.insert(0, str(backend_root))

# Import AgentProcessingContext using centralized import
from app.utils.import_utils import import_agent_schemas
AgentProcessingContext, _ = import_agent_schemas()

from .base import AgentComponent

logger = logging.getLogger(__name__)


class MarketingRequestParserComponent(AgentComponent):
    """Component for parsing marketing-related requests."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the marketing request parser component.

        Args:
            config: Configuration dictionary for the component
        """
        # Define task types
        self.task_types = config.get("task_types", [
            "marketing_strategy",
            "campaign_strategy",
            "social_media_content",
            "seo_optimization",
            "post_composer"
        ])

        # Task types (kept for backward compatibility but not used for keyword matching)
        self.task_types = config.get("task_types", [
            "marketing_strategy", "campaign_strategy", "social_media_content",
            "seo_optimization", "post_composer"
        ])

        # Define information extraction patterns
        self.extraction_patterns = config.get("extraction_patterns", {
            "brand_description": [
                r"brand(?:\s+is|\s*:\s*|\s+description\s*:?\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"company(?:\s+is|\s*:\s*|\s+description\s*:?\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"business(?:\s+is|\s*:\s*|\s+description\s*:?\s*)(.*?)(?=\n\n|\n[A-Z]|$)"
            ],
            "target_audience": [
                r"target\s+audience(?:\s+is|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"customers?(?:\s+are|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"demographic(?:\s+is|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)"
            ],
            "products_services": [
                r"products?(?:\s+are|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"services?(?:\s+are|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"offerings?(?:\s+are|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)"
            ],
            "marketing_goals": [
                r"goals?(?:\s+are|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"objectives?(?:\s+are|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"aim(?:\s+to|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)"
            ],
            "tone": [
                r"tone(?:\s+should\s+be|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"voice(?:\s+should\s+be|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)",
                r"style(?:\s+should\s+be|\s*:\s*)(.*?)(?=\n\n|\n[A-Z]|$)"
            ]
        })

    async def process(self, context):
        """
        Process a request using this component.

        Args:
            context: AgentProcessingContext object containing request data and state.

        Returns:
            Updated AgentProcessingContext object.
        """
        # Handle both dict and object contexts
        if hasattr(context, 'metadata'):
            # AgentProcessingContext object
            metadata = context.metadata
            message = getattr(context, 'message', "")
        else:
            # Dictionary context (from composable agents)
            metadata = context.get("metadata", {})
            message = context.get("message", "")

        # Check if this component should be skipped
        if metadata.get("skip_marketing_parser", False):
            logger.debug(f"Skipping marketing request parser component: {self.name}")
            return context

        if not message:
            logger.warning("No message found in context")
            # Optionally set a response if this component is expected to always have a message
            # context.response = "No message provided for marketing request parsing."
            return context

        try:
            # Extract information from the message using regex patterns
            extracted_info = self._extract_information(message)
            logger.info(f"Extracted information: {', '.join(extracted_info.keys())}")

            # Update context metadata with extracted information
            # Use generic task type since we're not using keyword-based detection
            if hasattr(context, 'metadata'):
                # AgentProcessingContext object
                context.metadata["task_type"] = "general_marketing"
                context.metadata.update(extracted_info) # Merge extracted info into metadata
                context.metadata["prompt_template_name"] = "general_marketing"
                context.metadata["extracted_info_keys"] = list(extracted_info.keys())
            else:
                # Dictionary context
                if "metadata" not in context:
                    context["metadata"] = {}
                context["metadata"]["task_type"] = "general_marketing"
                context["metadata"].update(extracted_info)
                context["metadata"]["prompt_template_name"] = "general_marketing"
                context["metadata"]["extracted_info_keys"] = list(extracted_info.keys())

            return context

        except Exception as e:
            logger.error(f"Error parsing marketing request: {str(e)}", exc_info=True)

            # Handle error reporting for both context types
            if hasattr(context, 'add_error'):
                context.add_error(self.name, f"marketing_parse_error: {str(e)}")
            else:
                # Dictionary context - add error to metadata
                if "metadata" not in context:
                    context["metadata"] = {}
                if "errors" not in context["metadata"]:
                    context["metadata"]["errors"] = []
                context["metadata"]["errors"].append({
                    "component": self.name,
                    "error": f"marketing_parse_error: {str(e)}"
                })

            return context



    def _extract_information(self, message: str) -> Dict[str, str]:
        """
        Extract marketing-related information from the message.

        Args:
            message: User message

        Returns:
            Dictionary of extracted information
        """
        extracted_info = {}

        # Apply extraction patterns
        for info_type, patterns in self.extraction_patterns.items():
            for pattern in patterns:
                matches = re.search(pattern, message, re.IGNORECASE | re.DOTALL)
                if matches:
                    extracted_text = matches.group(1).strip()
                    if extracted_text:
                        extracted_info[info_type] = extracted_text
                        break

        # Set default tone if not extracted
        if "tone" not in extracted_info:
            extracted_info["tone"] = "Professional"

        return extracted_info
