"""
Business Profile Security Configuration and Utilities.

This module provides security utilities and configurations specifically
for the business profile system to ensure secure operations.
"""

import logging
import re
import uuid
from typing import Dict, Any, List, Optional
from functools import wraps
from fastapi import HTTPException, status
from sqlalchemy.orm import Session

logger = logging.getLogger(__name__)


class BusinessProfileSecurityConfig:
    """Security configuration for business profiles."""
    
    # Input validation limits
    MAX_PROFILE_NAME_LENGTH = 255
    MAX_DESCRIPTION_LENGTH = 2000
    MAX_TEXT_FIELD_LENGTH = 2000
    MAX_SHORT_TEXT_LENGTH = 1000
    MAX_METADATA_SIZE = 10240  # 10KB
    
    # Rate limiting
    MAX_PROFILES_PER_USER = 50
    MAX_DATA_SOURCES_PER_PROFILE = 100
    
    # Allowed characters for text fields
    FORBIDDEN_CHARS = ['<', '>', '{', '}', '[', ']', '`', '\\', '|']
    
    # Business type and size validation
    VALID_BUSINESS_TYPES = ['B2B', 'B2C', 'B2B2C', 'marketplace', 'saas', 'ecommerce']
    VALID_BUSINESS_SIZES = ['startup', 'small', 'medium', 'large', 'enterprise']
    VALID_BUSINESS_STAGES = ['idea', 'startup', 'growth', 'mature', 'enterprise']
    VALID_DATA_SOURCE_ROLES = [
        'sales_data', 'business_description', 'marketing_materials', 
        'financial_data', 'customer_data', 'product_data', 
        'competitor_analysis', 'website_content', 'social_media', 'other'
    ]


def validate_uuid_format(uuid_string: str, field_name: str = "UUID") -> str:
    """
    Validate UUID format with security checks.
    
    Args:
        uuid_string: String to validate
        field_name: Field name for error messages
        
    Returns:
        Validated UUID string
        
    Raises:
        HTTPException: If UUID is invalid
    """
    if not uuid_string or not isinstance(uuid_string, str):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid {field_name}: must be a non-empty string"
        )
    
    # Check length to prevent extremely long strings
    if len(uuid_string) > 50:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid {field_name}: too long"
        )
    
    try:
        # Validate UUID format
        parsed_uuid = uuid.UUID(uuid_string)
        return str(parsed_uuid)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid {field_name} format. Must be a valid UUID."
        )


def validate_text_input(text: str, field_name: str, max_length: int = None, required: bool = False) -> Optional[str]:
    """
    Validate text input with security checks.
    
    Args:
        text: Text to validate
        field_name: Field name for error messages
        max_length: Maximum allowed length
        required: Whether the field is required
        
    Returns:
        Cleaned text or None
        
    Raises:
        HTTPException: If validation fails
    """
    if not text:
        if required:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{field_name} is required"
            )
        return None
    
    if not isinstance(text, str):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{field_name} must be a string"
        )
    
    # Remove excessive whitespace
    cleaned_text = ' '.join(text.strip().split())
    
    if not cleaned_text and required:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{field_name} cannot be empty"
        )
    
    # Check for forbidden characters
    if any(char in cleaned_text for char in BusinessProfileSecurityConfig.FORBIDDEN_CHARS):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{field_name} contains invalid characters"
        )
    
    # Check length
    if max_length and len(cleaned_text) > max_length:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{field_name} is too long (max {max_length} characters)"
        )
    
    return cleaned_text


def validate_metadata(metadata: Dict[str, Any], field_name: str = "metadata") -> Dict[str, Any]:
    """
    Validate metadata dictionary with security checks.
    
    Args:
        metadata: Metadata to validate
        field_name: Field name for error messages
        
    Returns:
        Validated metadata
        
    Raises:
        HTTPException: If validation fails
    """
    if metadata is None:
        return {}
    
    if not isinstance(metadata, dict):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{field_name} must be a dictionary"
        )
    
    # Check size to prevent abuse
    metadata_size = len(str(metadata))
    if metadata_size > BusinessProfileSecurityConfig.MAX_METADATA_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{field_name} is too large (max {BusinessProfileSecurityConfig.MAX_METADATA_SIZE} bytes)"
        )
    
    # Validate keys and values
    validated_metadata = {}
    for key, value in metadata.items():
        if not isinstance(key, str) or len(key) > 100:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid {field_name} key: {key}"
            )
        
        # Recursively validate nested dictionaries
        if isinstance(value, dict):
            validated_metadata[key] = validate_metadata(value, f"{field_name}.{key}")
        elif isinstance(value, (str, int, float, bool, type(None))):
            validated_metadata[key] = value
        elif isinstance(value, list):
            # Allow simple lists of basic types
            if all(isinstance(item, (str, int, float, bool, type(None))) for item in value):
                validated_metadata[key] = value
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid {field_name} value type in list for key: {key}"
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid {field_name} value type for key: {key}"
            )
    
    return validated_metadata


def check_user_profile_limit(db: Session, user_id: int) -> None:
    """
    Check if user has reached the maximum number of profiles.
    
    Args:
        db: Database session
        user_id: User ID
        
    Raises:
        HTTPException: If limit is exceeded
    """
    from ..database import BusinessProfile
    
    profile_count = db.query(BusinessProfile).filter(
        BusinessProfile.user_id == user_id
    ).count()
    
    if profile_count >= BusinessProfileSecurityConfig.MAX_PROFILES_PER_USER:
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=f"Maximum number of business profiles ({BusinessProfileSecurityConfig.MAX_PROFILES_PER_USER}) reached"
        )


def check_profile_data_source_limit(db: Session, profile_id: str) -> None:
    """
    Check if profile has reached the maximum number of data sources.
    
    Args:
        db: Database session
        profile_id: Profile ID
        
    Raises:
        HTTPException: If limit is exceeded
    """
    from ..database import BusinessProfileDataSource
    
    data_source_count = db.query(BusinessProfileDataSource).filter(
        BusinessProfileDataSource.business_profile_id == profile_id
    ).count()
    
    if data_source_count >= BusinessProfileSecurityConfig.MAX_DATA_SOURCES_PER_PROFILE:
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=f"Maximum number of data sources ({BusinessProfileSecurityConfig.MAX_DATA_SOURCES_PER_PROFILE}) reached for this profile"
        )


def sanitize_log_data(data: Any) -> str:
    """
    Sanitize data for logging to prevent sensitive information exposure.

    Args:
        data: Data to sanitize

    Returns:
        Sanitized string representation
    """
    if isinstance(data, dict):
        # Comprehensive list of sensitive fields including business-specific data
        sensitive_fields = [
            'password', 'token', 'secret', 'key', 'email', 'phone', 'ssn', 'social_security',
            'credit_card', 'card_number', 'cvv', 'pin', 'api_key', 'access_token', 'refresh_token',
            'private_key', 'certificate', 'passport', 'license', 'tax_id', 'ein', 'vat_number',
            'bank_account', 'routing_number', 'iban', 'swift', 'address', 'street', 'zip_code',
            'postal_code', 'date_of_birth', 'dob', 'birth_date', 'salary', 'income', 'revenue',
            'profit', 'financial_data', 'medical_record', 'health_info', 'diagnosis', 'treatment',
            'prescription', 'biometric', 'fingerprint', 'facial_recognition', 'voice_print',
            'ip_address', 'mac_address', 'device_id', 'session_id', 'user_agent', 'location',
            'coordinates', 'latitude', 'longitude', 'gps', 'tracking', 'cookie', 'browser_id'
        ]

        sanitized = {}
        for key, value in data.items():
            if any(sensitive in key.lower() for sensitive in sensitive_fields):
                sanitized[key] = "[REDACTED]"
            elif _is_pii_data(str(value)):
                sanitized[key] = _mask_pii_data(str(value))
            else:
                sanitized[key] = sanitize_log_data(value)
        return str(sanitized)
    elif isinstance(data, (list, tuple)):
        return str([sanitize_log_data(item) for item in data])
    elif isinstance(data, str):
        if _is_pii_data(data):
            return _mask_pii_data(data)
        elif len(data) > 100:
            return data[:97] + "..."
        else:
            return data
    else:
        return str(data)


def security_audit_log(action: str, user_id: int, resource_id: str = None, details: Dict[str, Any] = None):
    """
    Log security-relevant actions for audit purposes.
    
    Args:
        action: Action performed
        user_id: User ID
        resource_id: Resource ID (optional)
        details: Additional details (optional)
    """
    log_entry = {
        "action": action,
        "user_id": user_id,
        "resource_id": resource_id,
        "details": sanitize_log_data(details) if details else None
    }
    
    logger.info(f"SECURITY_AUDIT: {sanitize_log_data(log_entry)}")


def _is_pii_data(data: str) -> bool:
    """
    Enhanced PII detection with comprehensive patterns.

    Args:
        data: String data to check

    Returns:
        True if data appears to contain PII
    """
    if not data or len(data) < 3:
        return False

    # Email pattern (enhanced)
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    if re.search(email_pattern, data):
        return True

    # Phone number patterns (comprehensive)
    phone_patterns = [
        r'\b\d{3}-\d{3}-\d{4}\b',  # ************
        r'\b\(\d{3}\)\s*\d{3}-\d{4}\b',  # (*************
        r'\b\d{10}\b',  # 1234567890
        r'\b\+\d{1,3}[\s.-]?\d{3,4}[\s.-]?\d{3,4}[\s.-]?\d{3,4}\b',  # International
        r'\b\d{3}\.\d{3}\.\d{4}\b'  # ************
    ]

    for pattern in phone_patterns:
        if re.search(pattern, data):
            return True

    # SSN patterns (multiple formats)
    ssn_patterns = [
        r'\b\d{3}-\d{2}-\d{4}\b',  # ***********
        r'\b\d{3}\s\d{2}\s\d{4}\b',  # 123 45 6789
        r'\b\d{9}\b'  # 123456789 (if context suggests SSN)
    ]

    for pattern in ssn_patterns:
        if re.search(pattern, data):
            return True

    # Credit card patterns (enhanced)
    cc_patterns = [
        r'\b4\d{3}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b',  # Visa
        r'\b5[1-5]\d{2}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b',  # MasterCard
        r'\b3[47]\d{2}[\s-]?\d{6}[\s-]?\d{5}\b',  # American Express
        r'\b6011[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b'  # Discover
    ]

    for pattern in cc_patterns:
        if re.search(pattern, data):
            return True

    # IP Address patterns
    ip_patterns = [
        r'\b(?:\d{1,3}\.){3}\d{1,3}\b',  # IPv4
        r'\b(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\b'  # IPv6 (basic)
    ]

    for pattern in ip_patterns:
        if re.search(pattern, data):
            return True

    # Date of birth patterns
    dob_patterns = [
        r'\b\d{1,2}/\d{1,2}/\d{4}\b',  # MM/DD/YYYY
        r'\b\d{1,2}-\d{1,2}-\d{4}\b',  # MM-DD-YYYY
        r'\b\d{4}-\d{1,2}-\d{1,2}\b'   # YYYY-MM-DD
    ]

    for pattern in dob_patterns:
        if re.search(pattern, data):
            return True

    # Address patterns (basic)
    address_patterns = [
        r'\b\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln|Boulevard|Blvd)\b',
        r'\b\d{5}(?:-\d{4})?\b'  # ZIP codes
    ]

    for pattern in address_patterns:
        if re.search(pattern, data, re.IGNORECASE):
            return True

    # Financial identifiers
    financial_patterns = [
        r'\b[A-Z]{2}\d{2}[A-Z0-9]{4}\d{7}([A-Z0-9]?){0,16}\b',  # IBAN
        r'\b[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?\b'  # SWIFT/BIC
    ]

    for pattern in financial_patterns:
        if re.search(pattern, data):
            return True

    return False


def _mask_pii_data(data: str) -> str:
    """
    Mask PII data while preserving some structure for debugging.

    Args:
        data: String data to mask

    Returns:
        Masked string
    """
    if not data or len(data) < 3:
        return data

    # Email masking
    email_pattern = r'\b([A-Za-z0-9._%+-]+)@([A-Za-z0-9.-]+\.[A-Z|a-z]{2,})\b'
    data = re.sub(email_pattern, r'\1***@***.\2', data)

    # Phone number masking
    phone_patterns = [
        (r'\b(\d{3})-(\d{3})-(\d{4})\b', r'\1-***-\3'),  # ************ -> 123-***-7890
        (r'\b\((\d{3})\)\s*(\d{3})-(\d{4})\b', r'(\1) ***-\3'),  # (************* -> (123) ***-7890
        (r'\b(\d{3})(\d{3})(\d{4})\b', r'\1***\3')  # 1234567890 -> 123***7890
    ]

    for pattern, replacement in phone_patterns:
        data = re.sub(pattern, replacement, data)

    # SSN masking
    ssn_patterns = [
        (r'\b(\d{3})-(\d{2})-(\d{4})\b', r'***-**-\3'),  # *********** -> ***-**-6789
        (r'\b(\d{3})\s(\d{2})\s(\d{4})\b', r'*** ** \3'),  # 123 45 6789 -> *** ** 6789
        (r'\b(\d{3})(\d{2})(\d{4})\b', r'*****\3')  # 123456789 -> *****6789
    ]

    for pattern, replacement in ssn_patterns:
        data = re.sub(pattern, replacement, data)

    # Credit card masking
    cc_patterns = [
        (r'\b(\d{4})[\s-]?(\d{4})[\s-]?(\d{4})[\s-]?(\d{4})\b', r'****-****-****-\4')
    ]

    for pattern, replacement in cc_patterns:
        data = re.sub(pattern, replacement, data)

    # IP address masking
    data = re.sub(r'\b(\d{1,3}\.\d{1,3}\.\d{1,3}\.)\d{1,3}\b', r'\1***', data)

    # Generic masking for remaining patterns
    if len(data) > 10:
        # Show first 3 and last 3 characters for longer strings
        return data[:3] + '*' * (len(data) - 6) + data[-3:]
    elif len(data) > 6:
        # Show first 2 and last 2 characters for medium strings
        return data[:2] + '*' * (len(data) - 4) + data[-2:]
    else:
        # Mask most of short strings
        return data[0] + '*' * (len(data) - 1)
