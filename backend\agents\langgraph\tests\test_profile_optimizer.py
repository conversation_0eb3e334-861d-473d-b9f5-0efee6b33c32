"""
Unit tests for Business Profile Workflow Optimizer.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch

from ..optimization.profile_optimizer import (
    BusinessProfileWorkflowOptimizer,
    BusinessProfile,
    BusinessType,
    OptimizationStrategy,
    OptimizationResult
)


class TestBusinessProfileWorkflowOptimizer:
    """Test cases for BusinessProfileWorkflowOptimizer."""
    
    @pytest.fixture
    def optimizer(self):
        """Create optimizer instance."""
        return BusinessProfileWorkflowOptimizer()
    
    @pytest.fixture
    def sample_business_profile(self):
        """Create sample business profile."""
        return BusinessProfile(
            profile_id="test-profile-1",
            name="Test Company",
            industry="technology",
            business_type=BusinessType.SMB,
            business_size="medium",
            target_audience="B2B software companies",
            products_services="AI-powered analytics platform",
            marketing_goals="Increase market share and customer acquisition",
            context_metadata={"region": "North America"},
            performance_metrics={"revenue_growth": 0.15},
            user_preferences={"prefers_visual": True}
        )
    
    @pytest.mark.asyncio
    async def test_optimize_workflow_hybrid_strategy(self, optimizer, sample_business_profile):
        """Test workflow optimization with hybrid strategy."""
        result = await optimizer.optimize_workflow(
            workflow_template="analysis_template",
            business_profile=sample_business_profile,
            user_query="Analyze our customer acquisition metrics",
            strategy=OptimizationStrategy.HYBRID
        )
        
        # Assertions
        assert isinstance(result, OptimizationResult)
        assert result.original_workflow == "analysis_template"
        assert result.optimization_strategy == OptimizationStrategy.HYBRID
        assert result.confidence_score > 0.0
        assert result.expected_improvement >= 0.0
        assert len(result.reasoning) > 0
        assert result.metadata["profile_id"] == sample_business_profile.profile_id
    
    @pytest.mark.asyncio
    async def test_optimize_workflow_industry_specific(self, optimizer, sample_business_profile):
        """Test workflow optimization with industry-specific strategy."""
        result = await optimizer.optimize_workflow(
            workflow_template="dashboard_generation",
            business_profile=sample_business_profile,
            user_query="Create a technology industry dashboard",
            strategy=OptimizationStrategy.INDUSTRY_SPECIFIC
        )
        
        # Assertions
        assert result.optimization_strategy == OptimizationStrategy.INDUSTRY_SPECIFIC
        assert "industry_focus" in result.optimization_params
        assert result.optimization_params["industry"] == "technology"
        assert result.confidence_score > 0.5  # Should be higher for industry-specific
    
    @pytest.mark.asyncio
    async def test_optimize_workflow_goal_oriented(self, optimizer, sample_business_profile):
        """Test workflow optimization with goal-oriented strategy."""
        result = await optimizer.optimize_workflow(
            workflow_template="marketing_analysis",
            business_profile=sample_business_profile,
            user_query="Help with customer acquisition strategy",
            strategy=OptimizationStrategy.GOAL_ORIENTED
        )
        
        # Assertions
        assert result.optimization_strategy == OptimizationStrategy.GOAL_ORIENTED
        assert "goal_oriented" in result.optimization_params
        assert "customer_analysis" in result.optimization_params
    
    @pytest.mark.asyncio
    async def test_get_industry_recommendations_technology(self, optimizer):
        """Test getting industry recommendations for technology sector."""
        recommendations = await optimizer.get_industry_recommendations(
            industry="technology",
            business_type=BusinessType.SMB
        )
        
        # Assertions
        assert "preferred_workflows" in recommendations
        assert "optimization_params" in recommendations
        assert "industry_insights" in recommendations
        assert "business_type_insights" in recommendations
        
        # Check technology-specific recommendations
        assert "analysis_template" in recommendations["preferred_workflows"]
        assert recommendations["optimization_params"]["data_focus"] is True
    
    @pytest.mark.asyncio
    async def test_get_industry_recommendations_healthcare(self, optimizer):
        """Test getting industry recommendations for healthcare sector."""
        recommendations = await optimizer.get_industry_recommendations(
            industry="healthcare",
            business_type=BusinessType.ENTERPRISE
        )
        
        # Assertions
        assert "compliance_analysis" in recommendations["preferred_workflows"]
        assert recommendations["optimization_params"]["compliance_focus"] is True
        assert recommendations["optimization_params"]["privacy_emphasis"] is True
    
    @pytest.mark.asyncio
    async def test_analyze_profile_performance(self, optimizer, sample_business_profile):
        """Test profile performance analysis."""
        # Add some optimization history
        optimizer.optimization_history = [
            OptimizationResult(
                original_workflow="template1",
                optimized_workflow="template1_optimized",
                optimization_strategy=OptimizationStrategy.HYBRID,
                confidence_score=0.8,
                expected_improvement=0.2,
                optimization_params={},
                reasoning=["Test reasoning"],
                metadata={
                    "profile_id": sample_business_profile.profile_id,
                    "timestamp": datetime.now().isoformat()
                }
            ),
            OptimizationResult(
                original_workflow="template2",
                optimized_workflow="template2_optimized",
                optimization_strategy=OptimizationStrategy.INDUSTRY_SPECIFIC,
                confidence_score=0.7,
                expected_improvement=0.15,
                optimization_params={},
                reasoning=["Test reasoning 2"],
                metadata={
                    "profile_id": sample_business_profile.profile_id,
                    "timestamp": datetime.now().isoformat()
                }
            )
        ]
        
        # Analyze performance
        analysis = await optimizer.analyze_profile_performance(sample_business_profile.profile_id)
        
        # Assertions
        assert analysis["profile_id"] == sample_business_profile.profile_id
        assert analysis["total_optimizations"] == 2
        assert analysis["average_confidence"] == 0.75
        assert analysis["average_improvement"] == 0.175
        assert analysis["most_used_strategy"] in ["hybrid", "industry_specific"]
        assert "strategy_distribution" in analysis
        assert "performance_trend" in analysis
        assert "recommendations" in analysis
    
    def test_extract_industry_keywords(self, optimizer):
        """Test industry keyword extraction."""
        tech_keywords = optimizer._extract_industry_keywords("technology")
        assert "innovation" in tech_keywords
        assert "digital" in tech_keywords
        
        healthcare_keywords = optimizer._extract_industry_keywords("healthcare")
        assert "patient" in healthcare_keywords
        assert "medical" in healthcare_keywords
        
        unknown_keywords = optimizer._extract_industry_keywords("unknown_industry")
        assert unknown_keywords == []
    
    def test_extract_goals(self, optimizer):
        """Test goal extraction from marketing goals text."""
        goals_text = "We want to increase revenue and grow our customer base while improving brand awareness"
        goals = optimizer._extract_goals(goals_text)
        
        assert "revenue" in goals
        assert "customers" in goals
        assert "brand" in goals
        
        empty_goals = optimizer._extract_goals("")
        assert empty_goals == []
    
    def test_analyze_audience(self, optimizer):
        """Test target audience analysis."""
        b2b_audience = "Enterprise software companies and IT departments"
        analysis = optimizer._analyze_audience(b2b_audience)
        
        assert analysis["b2b"] is True
        assert analysis["technical"] is True
        
        b2c_audience = "Individual consumers and personal users"
        analysis = optimizer._analyze_audience(b2c_audience)
        
        assert analysis["b2c"] is True
        assert analysis["b2b"] is False
    
    def test_analyze_query_intent(self, optimizer):
        """Test user query intent analysis."""
        analytical_query = "Please analyze our sales data and show performance metrics"
        intent = optimizer._analyze_query_intent(analytical_query)
        
        assert intent["analytical"] is True
        assert intent["financial"] is False
        
        strategic_query = "Help me develop a long-term strategy and roadmap"
        intent = optimizer._analyze_query_intent(strategic_query)
        
        assert intent["strategic"] is True
        assert intent["analytical"] is False
        
        urgent_query = "I need this analysis urgently, ASAP please"
        intent = optimizer._analyze_query_intent(urgent_query)
        
        assert intent["urgent"] is True
    
    def test_assess_business_maturity(self, optimizer):
        """Test business maturity assessment."""
        startup_profile = BusinessProfile(
            profile_id="startup",
            name="Startup Co",
            industry="technology",
            business_type=BusinessType.STARTUP,
            business_size="small",
            target_audience="",
            products_services="",
            marketing_goals=""
        )
        
        maturity = optimizer._assess_business_maturity(startup_profile)
        assert maturity == "early"
        
        enterprise_profile = BusinessProfile(
            profile_id="enterprise",
            name="Enterprise Corp",
            industry="finance",
            business_type=BusinessType.ENTERPRISE,
            business_size="large",
            target_audience="",
            products_services="",
            marketing_goals=""
        )
        
        maturity = optimizer._assess_business_maturity(enterprise_profile)
        assert maturity == "mature"
    
    def test_assess_complexity_needs(self, optimizer, sample_business_profile):
        """Test complexity needs assessment."""
        simple_query = "Give me a quick overview of basic metrics"
        complexity = optimizer._assess_complexity_needs(sample_business_profile, simple_query)
        assert complexity == "low"
        
        complex_query = "Provide a comprehensive detailed analysis with advanced insights"
        complexity = optimizer._assess_complexity_needs(sample_business_profile, complex_query)
        assert complexity == "medium"  # SMB profile moderates complexity
        
        # Test with enterprise profile
        enterprise_profile = BusinessProfile(
            profile_id="enterprise",
            name="Enterprise Corp",
            industry="finance",
            business_type=BusinessType.ENTERPRISE,
            business_size="large",
            target_audience="",
            products_services="",
            marketing_goals=""
        )
        
        complexity = optimizer._assess_complexity_needs(enterprise_profile, complex_query)
        assert complexity == "high"
    
    def test_calculate_profile_completeness(self, optimizer, sample_business_profile):
        """Test profile completeness calculation."""
        completeness = optimizer._calculate_profile_completeness(sample_business_profile)
        assert completeness == 1.0  # All fields filled
        
        incomplete_profile = BusinessProfile(
            profile_id="incomplete",
            name="Incomplete Co",
            industry="",  # Missing
            business_type=BusinessType.SMB,
            business_size="",  # Missing
            target_audience="",  # Missing
            products_services="",  # Missing
            marketing_goals=""  # Missing
        )
        
        completeness = optimizer._calculate_profile_completeness(incomplete_profile)
        assert completeness < 1.0
    
    @pytest.mark.asyncio
    async def test_get_optimization_insights(self, optimizer, sample_business_profile):
        """Test getting optimization insights."""
        # Add some optimization history
        optimizer.optimization_history = [
            OptimizationResult(
                original_workflow="template1",
                optimized_workflow="template1_optimized",
                optimization_strategy=OptimizationStrategy.HYBRID,
                confidence_score=0.9,
                expected_improvement=0.25,
                optimization_params={},
                reasoning=["High confidence optimization"],
                metadata={
                    "profile_id": sample_business_profile.profile_id,
                    "timestamp": datetime.now().isoformat()
                }
            )
        ]
        
        insights = await optimizer.get_optimization_insights(sample_business_profile.profile_id)
        
        # Assertions
        assert insights["profile_id"] == sample_business_profile.profile_id
        assert insights["total_optimizations"] == 1
        assert insights["most_successful_strategy"] == "hybrid"
        assert "optimization_trends" in insights
        assert "improvement_opportunities" in insights
        assert "insights" in insights
        assert "recommendations" in insights
    
    @pytest.mark.asyncio
    async def test_get_optimization_insights_no_history(self, optimizer):
        """Test getting optimization insights with no history."""
        insights = await optimizer.get_optimization_insights("no-history-profile")
        
        # Assertions
        assert insights["profile_id"] == "no-history-profile"
        assert "No optimization history available" in insights["insights"]
        assert "Start using workflow optimizations" in insights["recommendations"]
