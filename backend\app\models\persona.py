"""
Persona models for the Datagenius backend.

This module provides Pydantic models for AI persona-related functionality.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class PersonaBase(BaseModel):
    """Base model for persona data."""
    id: str
    name: str
    description: str
    industry: str
    skills: List[str]
    rating: float
    review_count: int
    image_url: str
    is_available: bool = True
    capabilities: Optional[List[str]] = None
    # Additional fields for admin management
    price: float = 10.0
    provider: str = "groq"
    model: Optional[str] = None
    is_active: bool = True
    age_restriction: int = Field(0, ge=0)
    content_filters: Optional[Dict[str, Any]] = None
    # Purchase status
    is_purchased: bool = False


class PersonaCreate(BaseModel):
    """Model for creating a new persona."""
    id: str
    name: str
    description: str
    industry: str
    skills: List[str]
    rating: float = 0.0
    review_count: int = 0
    image_url: str = ""
    is_available: bool = True
    capabilities: Optional[List[str]] = None
    price: float = 10.0
    provider: str = "groq"
    model: Optional[str] = None
    is_active: bool = True
    age_restriction: int = Field(0, ge=0)
    content_filters: Optional[Dict[str, Any]] = None


class PersonaUpdate(BaseModel):
    """Model for updating a persona."""
    name: Optional[str] = None
    description: Optional[str] = None
    industry: Optional[str] = None
    skills: Optional[List[str]] = None
    rating: Optional[float] = None
    review_count: Optional[int] = None
    image_url: Optional[str] = None
    is_available: Optional[bool] = None
    capabilities: Optional[List[str]] = None
    price: Optional[float] = None
    provider: Optional[str] = None
    model: Optional[str] = None
    is_active: Optional[bool] = None
    age_restriction: Optional[int] = None
    content_filters: Optional[Dict[str, Any]] = None


class PersonaResponse(BaseModel):
    """Model for persona response."""
    persona: PersonaBase


class PersonaListResponse(BaseModel):
    """Model for persona list response."""
    personas: List[PersonaBase]


class UserPersonaAccessResponse(BaseModel):
    """Model for user persona access response."""
    has_access: bool
    is_purchased: bool


class PersonaVersionBase(BaseModel):
    """Base model for persona version data."""
    id: str
    persona_id: str
    version: str
    config: Dict[str, Any]
    is_active: bool = False
    created_at: Optional[datetime] = None
    created_by: Optional[int] = None
    is_file_only: bool = False


class PersonaVersionCreate(BaseModel):
    """Model for creating a new persona version."""
    version: str
    config: Dict[str, Any]
    is_active: bool = False


class PersonaVersionUpdate(BaseModel):
    """Model for updating a persona version."""
    version: Optional[str] = None
    config: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class PersonaVersionResponse(BaseModel):
    """Model for persona version response."""
    version: PersonaVersionBase


class PersonaVersionListResponse(BaseModel):
    """Model for persona version list response."""
    versions: List[PersonaVersionBase]
