"""
Metrics Collection and Analysis for LangGraph Workflows.

This module provides detailed metrics collection, aggregation, and
analysis capabilities for workflow performance monitoring.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass
from collections import defaultdict
import statistics

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

from app.config import get_config

logger = logging.getLogger(__name__)


class MetricsCollector:
    """
    Simple metrics collector for Phase 4 components.

    This class provides basic metrics collection functionality
    for marketplace, trading, certification, and AI components.
    """

    def __init__(self, component_name: str):
        self.component_name = component_name
        self.counters: Dict[str, int] = defaultdict(int)
        self.gauges: Dict[str, float] = {}
        self.timers: Dict[str, List[float]] = defaultdict(list)
        self.created_at = datetime.now()
        self.logger = logging.getLogger(__name__)

    def increment(self, metric_name: str, value: int = 1):
        """Increment a counter metric."""
        self.counters[metric_name] += value

    def set_gauge(self, metric_name: str, value: float):
        """Set a gauge metric value."""
        self.gauges[metric_name] = value

    def record_time(self, metric_name: str, duration: float):
        """Record a timing metric."""
        self.timers[metric_name].append(duration)

    def record(self, metric_name: str, value: Any = None, metadata: Dict[str, Any] = None, *args, **kwargs):
        """Record a general metric (for compatibility)."""
        if isinstance(value, (int, float)) and value > 0:
            self.increment(metric_name, int(value))
        else:
            self.increment(metric_name)

        # Store metadata if provided (for future use)
        if metadata:
            # For now, we'll just log the metadata since this is a simple collector
            # In a more advanced implementation, this could be stored with the metric
            pass

        # Handle any additional arguments for compatibility
        if args or kwargs:
            # Log additional arguments for debugging
            self.logger.debug(f"Additional arguments passed to record(): args={args}, kwargs={kwargs}")
            pass

    def get_counter(self, metric_name: str) -> int:
        """Get counter value."""
        return self.counters.get(metric_name, 0)

    def get_gauge(self, metric_name: str) -> Optional[float]:
        """Get gauge value."""
        return self.gauges.get(metric_name)

    def get_timer_stats(self, metric_name: str) -> Dict[str, float]:
        """Get timer statistics."""
        times = self.timers.get(metric_name, [])
        if not times:
            return {"count": 0, "avg": 0.0, "min": 0.0, "max": 0.0}

        return {
            "count": len(times),
            "avg": statistics.mean(times),
            "min": min(times),
            "max": max(times),
            "median": statistics.median(times)
        }

    def get_all_metrics(self) -> Dict[str, Any]:
        """Get all metrics for this component."""
        return {
            "component": self.component_name,
            "created_at": self.created_at.isoformat(),
            "counters": dict(self.counters),
            "gauges": dict(self.gauges),
            "timers": {name: self.get_timer_stats(name) for name in self.timers.keys()}
        }


@dataclass
class MetricsSummary:
    """Summary of workflow metrics over a time period."""
    period_start: datetime
    period_end: datetime
    total_workflows: int
    avg_execution_time: float
    median_execution_time: float
    p95_execution_time: float
    avg_success_rate: float
    total_errors: int
    avg_quality_score: Optional[float]
    top_performing_agents: List[str]
    bottleneck_agents: List[str]


class WorkflowMetrics:
    """
    Advanced metrics collection and analysis system.
    
    This class provides comprehensive metrics analysis including:
    - Time-series analysis
    - Performance benchmarking
    - Comparative analysis
    - Predictive insights
    """

    def __init__(self, database_url: str = None):
        """
        Initialize the metrics system.
        
        Args:
            database_url: Database connection URL
        """
        config = get_config()
        self.database_url = database_url or config.database.url
        self.engine = create_engine(self.database_url)
        self.SessionLocal = sessionmaker(bind=self.engine)
        self.logger = logging.getLogger(__name__)

    async def get_metrics_summary(
        self, 
        start_date: datetime, 
        end_date: datetime,
        workflow_ids: Optional[List[str]] = None
    ) -> MetricsSummary:
        """
        Get comprehensive metrics summary for a time period.
        
        Args:
            start_date: Start of the analysis period
            end_date: End of the analysis period
            workflow_ids: Optional list of specific workflow IDs to analyze
            
        Returns:
            MetricsSummary object with aggregated metrics
        """
        try:
            session = self.SessionLocal()
            
            # Build base query
            base_query = """
                SELECT 
                    wpm.execution_time_ms,
                    wpm.success_rate,
                    wpm.error_count,
                    wpm.quality_score,
                    lws.agent_id
                FROM workflow_performance_metrics wpm
                LEFT JOIN langgraph_workflow_states lws ON wpm.workflow_id = lws.workflow_id
                WHERE wpm.recorded_at BETWEEN :start_date AND :end_date
            """
            
            params = {
                "start_date": start_date,
                "end_date": end_date
            }
            
            if workflow_ids:
                base_query += " AND wpm.workflow_id = ANY(:workflow_ids)"
                params["workflow_ids"] = workflow_ids
            
            results = session.execute(text(base_query), params).fetchall()
            session.close()
            
            if not results:
                return MetricsSummary(
                    period_start=start_date,
                    period_end=end_date,
                    total_workflows=0,
                    avg_execution_time=0.0,
                    median_execution_time=0.0,
                    p95_execution_time=0.0,
                    avg_success_rate=0.0,
                    total_errors=0,
                    avg_quality_score=None,
                    top_performing_agents=[],
                    bottleneck_agents=[]
                )
            
            # Process results
            execution_times = [r.execution_time_ms / 1000.0 for r in results if r.execution_time_ms]
            success_rates = [r.success_rate for r in results if r.success_rate is not None]
            quality_scores = [r.quality_score for r in results if r.quality_score is not None]
            total_errors = sum(r.error_count for r in results if r.error_count)
            
            # Agent performance analysis
            agent_performance = defaultdict(list)
            for r in results:
                if r.agent_id and r.execution_time_ms:
                    agent_performance[r.agent_id].append(r.execution_time_ms / 1000.0)
            
            # Calculate agent rankings
            agent_avg_times = {
                agent: statistics.mean(times) 
                for agent, times in agent_performance.items() 
                if len(times) >= 3  # Minimum sample size
            }
            
            top_performing = sorted(agent_avg_times.items(), key=lambda x: x[1])[:5]
            bottleneck_agents = sorted(agent_avg_times.items(), key=lambda x: x[1], reverse=True)[:3]
            
            return MetricsSummary(
                period_start=start_date,
                period_end=end_date,
                total_workflows=len(set(r.execution_time_ms for r in results)),
                avg_execution_time=statistics.mean(execution_times) if execution_times else 0.0,
                median_execution_time=statistics.median(execution_times) if execution_times else 0.0,
                p95_execution_time=statistics.quantiles(execution_times, n=20)[18] if len(execution_times) >= 20 else (max(execution_times) if execution_times else 0.0),
                avg_success_rate=statistics.mean(success_rates) if success_rates else 0.0,
                total_errors=total_errors,
                avg_quality_score=statistics.mean(quality_scores) if quality_scores else None,
                top_performing_agents=[agent for agent, _ in top_performing],
                bottleneck_agents=[agent for agent, _ in bottleneck_agents]
            )
            
        except Exception as e:
            self.logger.error(f"Error getting metrics summary: {e}")
            return MetricsSummary(
                period_start=start_date,
                period_end=end_date,
                total_workflows=0,
                avg_execution_time=0.0,
                median_execution_time=0.0,
                p95_execution_time=0.0,
                avg_success_rate=0.0,
                total_errors=0,
                avg_quality_score=None,
                top_performing_agents=[],
                bottleneck_agents=[]
            )

    async def get_time_series_metrics(
        self, 
        start_date: datetime, 
        end_date: datetime,
        interval: str = "hour"  # "hour", "day", "week"
    ) -> List[Dict[str, Any]]:
        """
        Get time-series metrics data for visualization.
        
        Args:
            start_date: Start of the analysis period
            end_date: End of the analysis period
            interval: Time interval for aggregation
            
        Returns:
            List of time-series data points
        """
        try:
            session = self.SessionLocal()
            
            # Determine time truncation based on interval
            time_trunc = {
                "hour": "date_trunc('hour', recorded_at)",
                "day": "date_trunc('day', recorded_at)",
                "week": "date_trunc('week', recorded_at)"
            }.get(interval, "date_trunc('hour', recorded_at)")
            
            query = text(f"""
                SELECT 
                    {time_trunc} as time_bucket,
                    COUNT(*) as workflow_count,
                    AVG(execution_time_ms) as avg_execution_time_ms,
                    AVG(success_rate) as avg_success_rate,
                    SUM(error_count) as total_errors,
                    AVG(quality_score) as avg_quality_score,
                    AVG(memory_usage_mb) as avg_memory_usage,
                    AVG(cpu_usage_percent) as avg_cpu_usage
                FROM workflow_performance_metrics
                WHERE recorded_at BETWEEN :start_date AND :end_date
                GROUP BY {time_trunc}
                ORDER BY time_bucket
            """)
            
            results = session.execute(query, {
                "start_date": start_date,
                "end_date": end_date
            }).fetchall()
            
            session.close()
            
            time_series = []
            for result in results:
                time_series.append({
                    "timestamp": result.time_bucket.isoformat(),
                    "workflow_count": result.workflow_count,
                    "avg_execution_time": result.avg_execution_time_ms / 1000.0 if result.avg_execution_time_ms else 0.0,
                    "avg_success_rate": float(result.avg_success_rate) if result.avg_success_rate else 0.0,
                    "total_errors": result.total_errors or 0,
                    "avg_quality_score": float(result.avg_quality_score) if result.avg_quality_score else None,
                    "avg_memory_usage_mb": float(result.avg_memory_usage) if result.avg_memory_usage else 0.0,
                    "avg_cpu_usage_percent": float(result.avg_cpu_usage) if result.avg_cpu_usage else 0.0
                })
            
            return time_series
            
        except Exception as e:
            self.logger.error(f"Error getting time series metrics: {e}")
            return []

    async def get_agent_performance_comparison(
        self, 
        start_date: datetime, 
        end_date: datetime
    ) -> Dict[str, Dict[str, Any]]:
        """
        Compare performance across different agents.
        
        Args:
            start_date: Start of the analysis period
            end_date: End of the analysis period
            
        Returns:
            Dictionary with agent performance comparisons
        """
        try:
            session = self.SessionLocal()
            
            query = text("""
                SELECT 
                    lws.agent_id,
                    COUNT(*) as execution_count,
                    AVG(lws.duration_ms) as avg_duration_ms,
                    COUNT(CASE WHEN lws.error_data IS NULL THEN 1 END) as success_count,
                    COUNT(CASE WHEN lws.error_data IS NOT NULL THEN 1 END) as error_count,
                    AVG(wpm.quality_score) as avg_quality_score
                FROM langgraph_workflow_states lws
                LEFT JOIN workflow_performance_metrics wpm ON lws.workflow_id = wpm.workflow_id
                WHERE lws.started_at BETWEEN :start_date AND :end_date
                  AND lws.agent_id IS NOT NULL
                GROUP BY lws.agent_id
                HAVING COUNT(*) >= 3
                ORDER BY AVG(lws.duration_ms)
            """)
            
            results = session.execute(query, {
                "start_date": start_date,
                "end_date": end_date
            }).fetchall()
            
            session.close()
            
            agent_comparison = {}
            for result in results:
                success_rate = result.success_count / result.execution_count if result.execution_count > 0 else 0.0
                error_rate = result.error_count / result.execution_count if result.execution_count > 0 else 0.0
                
                agent_comparison[result.agent_id] = {
                    "execution_count": result.execution_count,
                    "avg_duration_ms": float(result.avg_duration_ms) if result.avg_duration_ms else 0.0,
                    "success_rate": success_rate,
                    "error_rate": error_rate,
                    "avg_quality_score": float(result.avg_quality_score) if result.avg_quality_score else None,
                    "performance_score": self._calculate_agent_performance_score(
                        result.avg_duration_ms or 0.0,
                        success_rate,
                        result.avg_quality_score or 0.5
                    )
                }
            
            return agent_comparison
            
        except Exception as e:
            self.logger.error(f"Error getting agent performance comparison: {e}")
            return {}

    def _calculate_agent_performance_score(
        self, 
        avg_duration_ms: float, 
        success_rate: float, 
        quality_score: float
    ) -> float:
        """Calculate overall performance score for an agent."""
        # Normalize duration (assume 30 seconds is baseline)
        duration_score = max(0, 1 - (avg_duration_ms / 30000))
        
        # Weighted score
        performance_score = (
            duration_score * 0.3 +
            success_rate * 0.5 +
            quality_score * 0.2
        ) * 100
        
        return round(performance_score, 2)

    async def get_error_analysis(
        self, 
        start_date: datetime, 
        end_date: datetime
    ) -> Dict[str, Any]:
        """
        Analyze error patterns and frequencies.
        
        Args:
            start_date: Start of the analysis period
            end_date: End of the analysis period
            
        Returns:
            Dictionary with error analysis results
        """
        try:
            session = self.SessionLocal()
            
            # Get error patterns from workflow states
            query = text("""
                SELECT 
                    lws.agent_id,
                    lws.error_data,
                    COUNT(*) as error_count,
                    lws.started_at
                FROM langgraph_workflow_states lws
                WHERE lws.started_at BETWEEN :start_date AND :end_date
                  AND lws.error_data IS NOT NULL
                GROUP BY lws.agent_id, lws.error_data, lws.started_at
                ORDER BY error_count DESC
            """)
            
            results = session.execute(query, {
                "start_date": start_date,
                "end_date": end_date
            }).fetchall()
            
            session.close()
            
            # Analyze error patterns
            error_by_agent = defaultdict(int)
            error_types = defaultdict(int)
            error_timeline = []
            
            for result in results:
                error_by_agent[result.agent_id or "unknown"] += result.error_count
                
                # Extract error type from error_data (simplified)
                if result.error_data:
                    try:
                        import json
                        error_data = json.loads(result.error_data) if isinstance(result.error_data, str) else result.error_data
                        error_type = error_data.get("type", "unknown")
                        error_types[error_type] += result.error_count
                    except:
                        error_types["parse_error"] += result.error_count
                
                error_timeline.append({
                    "timestamp": result.started_at.isoformat(),
                    "agent_id": result.agent_id,
                    "error_count": result.error_count
                })
            
            return {
                "total_errors": sum(error_by_agent.values()),
                "errors_by_agent": dict(error_by_agent),
                "error_types": dict(error_types),
                "error_timeline": error_timeline,
                "most_error_prone_agent": max(error_by_agent.items(), key=lambda x: x[1])[0] if error_by_agent else None,
                "most_common_error_type": max(error_types.items(), key=lambda x: x[1])[0] if error_types else None
            }
            
        except Exception as e:
            self.logger.error(f"Error getting error analysis: {e}")
            return {
                "total_errors": 0,
                "errors_by_agent": {},
                "error_types": {},
                "error_timeline": [],
                "most_error_prone_agent": None,
                "most_common_error_type": None
            }

    async def get_performance_benchmarks(self) -> Dict[str, Any]:
        """
        Get performance benchmarks based on historical data.
        
        Returns:
            Dictionary with performance benchmarks
        """
        try:
            session = self.SessionLocal()
            
            # Get last 30 days of data for benchmarking
            thirty_days_ago = datetime.now(timezone.utc) - timedelta(days=30)
            
            query = text("""
                SELECT 
                    execution_time_ms,
                    success_rate,
                    quality_score,
                    agent_transitions,
                    tool_executions
                FROM workflow_performance_metrics
                WHERE recorded_at >= :thirty_days_ago
                  AND execution_time_ms IS NOT NULL
                  AND success_rate IS NOT NULL
            """)
            
            results = session.execute(query, {
                "thirty_days_ago": thirty_days_ago
            }).fetchall()
            
            session.close()
            
            if not results:
                return {"message": "No data available for benchmarking"}
            
            # Calculate benchmarks
            execution_times = [r.execution_time_ms / 1000.0 for r in results]
            success_rates = [r.success_rate for r in results]
            quality_scores = [r.quality_score for r in results if r.quality_score is not None]
            agent_transitions = [r.agent_transitions for r in results if r.agent_transitions is not None]
            tool_executions = [r.tool_executions for r in results if r.tool_executions is not None]
            
            benchmarks = {
                "execution_time": {
                    "excellent": statistics.quantiles(execution_times, n=10)[2],  # 30th percentile
                    "good": statistics.quantiles(execution_times, n=10)[5],       # 60th percentile
                    "acceptable": statistics.quantiles(execution_times, n=10)[7], # 80th percentile
                    "poor": statistics.quantiles(execution_times, n=10)[8]       # 90th percentile
                },
                "success_rate": {
                    "excellent": 0.98,
                    "good": 0.95,
                    "acceptable": 0.90,
                    "poor": 0.85
                },
                "quality_score": {
                    "excellent": statistics.quantiles(quality_scores, n=10)[7] if quality_scores else 0.9,
                    "good": statistics.quantiles(quality_scores, n=10)[5] if quality_scores else 0.8,
                    "acceptable": statistics.quantiles(quality_scores, n=10)[3] if quality_scores else 0.7,
                    "poor": statistics.quantiles(quality_scores, n=10)[1] if quality_scores else 0.6
                },
                "agent_transitions": {
                    "excellent": statistics.quantiles(agent_transitions, n=10)[2] if agent_transitions else 2,
                    "good": statistics.quantiles(agent_transitions, n=10)[5] if agent_transitions else 4,
                    "acceptable": statistics.quantiles(agent_transitions, n=10)[7] if agent_transitions else 6,
                    "poor": statistics.quantiles(agent_transitions, n=10)[8] if agent_transitions else 8
                }
            }
            
            return benchmarks
            
        except Exception as e:
            self.logger.error(f"Error getting performance benchmarks: {e}")
            return {"error": str(e)}

    async def get_real_time_metrics(self) -> Dict[str, Any]:
        """
        Get real-time metrics for live monitoring.

        Returns:
            Dictionary with current system metrics
        """
        try:
            session = self.SessionLocal()

            # Get metrics from the last hour
            one_hour_ago = datetime.now(timezone.utc) - timedelta(hours=1)

            query = text("""
                SELECT
                    COUNT(*) as active_workflows,
                    AVG(execution_time_ms) as avg_execution_time_ms,
                    COUNT(CASE WHEN success_rate >= 0.95 THEN 1 END) as successful_workflows,
                    COUNT(CASE WHEN error_count > 0 THEN 1 END) as failed_workflows,
                    AVG(memory_usage_mb) as avg_memory_usage,
                    AVG(cpu_usage_percent) as avg_cpu_usage,
                    MAX(recorded_at) as last_update
                FROM workflow_performance_metrics
                WHERE recorded_at >= :one_hour_ago
            """)

            result = session.execute(query, {"one_hour_ago": one_hour_ago}).fetchone()
            session.close()

            if not result or result.active_workflows == 0:
                return {
                    "status": "idle",
                    "active_workflows": 0,
                    "avg_execution_time": 0.0,
                    "success_rate": 0.0,
                    "system_health": "unknown",
                    "last_update": None
                }

            success_rate = result.successful_workflows / result.active_workflows if result.active_workflows > 0 else 0.0

            # Determine system health
            health_score = self._calculate_system_health(
                success_rate,
                result.avg_execution_time_ms or 0.0,
                result.avg_memory_usage or 0.0,
                result.avg_cpu_usage or 0.0
            )

            return {
                "status": "active",
                "active_workflows": result.active_workflows,
                "avg_execution_time": (result.avg_execution_time_ms / 1000.0) if result.avg_execution_time_ms else 0.0,
                "success_rate": success_rate,
                "failed_workflows": result.failed_workflows,
                "avg_memory_usage_mb": float(result.avg_memory_usage) if result.avg_memory_usage else 0.0,
                "avg_cpu_usage_percent": float(result.avg_cpu_usage) if result.avg_cpu_usage else 0.0,
                "system_health": health_score["status"],
                "health_score": health_score["score"],
                "last_update": result.last_update.isoformat() if result.last_update else None
            }

        except Exception as e:
            self.logger.error(f"Error getting real-time metrics: {e}")
            return {
                "status": "error",
                "error": str(e),
                "last_update": datetime.now(timezone.utc).isoformat()
            }

    def _calculate_system_health(
        self,
        success_rate: float,
        avg_execution_time_ms: float,
        avg_memory_usage_mb: float,
        avg_cpu_usage_percent: float
    ) -> Dict[str, Any]:
        """Calculate overall system health score."""
        # Health scoring weights
        success_weight = 0.4
        performance_weight = 0.3
        resource_weight = 0.3

        # Success rate score (0-100)
        success_score = success_rate * 100

        # Performance score (inverse of execution time, normalized)
        # Assume 10 seconds is baseline, 30 seconds is poor
        performance_score = max(0, 100 - (avg_execution_time_ms / 300))  # 300ms = 30 points

        # Resource usage score (inverse of usage)
        memory_score = max(0, 100 - (avg_memory_usage_mb / 10))  # 1GB = 100 points penalty
        cpu_score = max(0, 100 - avg_cpu_usage_percent)
        resource_score = (memory_score + cpu_score) / 2

        # Overall health score
        health_score = (
            success_score * success_weight +
            performance_score * performance_weight +
            resource_score * resource_weight
        )

        # Determine status
        if health_score >= 90:
            status = "excellent"
        elif health_score >= 75:
            status = "good"
        elif health_score >= 60:
            status = "fair"
        elif health_score >= 40:
            status = "poor"
        else:
            status = "critical"

        return {
            "score": round(health_score, 2),
            "status": status,
            "components": {
                "success": round(success_score, 2),
                "performance": round(performance_score, 2),
                "resources": round(resource_score, 2)
            }
        }

    async def get_workflow_efficiency_metrics(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """
        Calculate workflow efficiency metrics.

        Args:
            start_date: Start of the analysis period
            end_date: End of the analysis period

        Returns:
            Dictionary with efficiency metrics
        """
        try:
            session = self.SessionLocal()

            query = text("""
                SELECT
                    wpm.workflow_id,
                    wpm.execution_time_ms,
                    wpm.agent_transitions,
                    wpm.tool_executions,
                    wpm.success_rate,
                    wpm.quality_score,
                    COUNT(lws.id) as total_steps,
                    AVG(lws.duration_ms) as avg_step_duration
                FROM workflow_performance_metrics wpm
                LEFT JOIN langgraph_workflow_states lws ON wpm.workflow_id = lws.workflow_id
                WHERE wpm.recorded_at BETWEEN :start_date AND :end_date
                GROUP BY wpm.workflow_id, wpm.execution_time_ms, wpm.agent_transitions,
                         wpm.tool_executions, wpm.success_rate, wpm.quality_score
                HAVING COUNT(lws.id) > 0
            """)

            results = session.execute(query, {
                "start_date": start_date,
                "end_date": end_date
            }).fetchall()

            session.close()

            if not results:
                return {"message": "No workflow data available for efficiency analysis"}

            # Calculate efficiency metrics
            efficiency_scores = []
            step_efficiency = []
            transition_efficiency = []

            for result in results:
                # Step efficiency: quality per time unit
                if result.avg_step_duration and result.quality_score:
                    step_eff = result.quality_score / (result.avg_step_duration / 1000.0)
                    step_efficiency.append(step_eff)

                # Transition efficiency: success rate per transition
                if result.agent_transitions and result.success_rate:
                    trans_eff = result.success_rate / result.agent_transitions
                    transition_efficiency.append(trans_eff)

                # Overall efficiency score
                if result.execution_time_ms and result.quality_score and result.success_rate:
                    efficiency = (result.quality_score * result.success_rate) / (result.execution_time_ms / 1000.0)
                    efficiency_scores.append(efficiency)

            return {
                "overall_efficiency": {
                    "avg_score": statistics.mean(efficiency_scores) if efficiency_scores else 0.0,
                    "median_score": statistics.median(efficiency_scores) if efficiency_scores else 0.0,
                    "top_10_percent": statistics.quantiles(efficiency_scores, n=10)[8] if len(efficiency_scores) >= 10 else (max(efficiency_scores) if efficiency_scores else 0.0)
                },
                "step_efficiency": {
                    "avg_quality_per_second": statistics.mean(step_efficiency) if step_efficiency else 0.0,
                    "best_performers": sorted(step_efficiency, reverse=True)[:5] if step_efficiency else []
                },
                "transition_efficiency": {
                    "avg_success_per_transition": statistics.mean(transition_efficiency) if transition_efficiency else 0.0,
                    "optimal_transition_count": self._find_optimal_transition_count(results)
                },
                "recommendations": self._generate_efficiency_recommendations(results)
            }

        except Exception as e:
            self.logger.error(f"Error calculating workflow efficiency: {e}")
            return {"error": str(e)}

    def _find_optimal_transition_count(self, results) -> int:
        """Find the optimal number of agent transitions."""
        transition_success = defaultdict(list)

        for result in results:
            if result.agent_transitions and result.success_rate:
                transition_success[result.agent_transitions].append(result.success_rate)

        # Find transition count with highest average success rate
        avg_success_by_transitions = {
            transitions: statistics.mean(success_rates)
            for transitions, success_rates in transition_success.items()
            if len(success_rates) >= 3  # Minimum sample size
        }

        if not avg_success_by_transitions:
            return 3  # Default recommendation

        optimal_transitions = max(avg_success_by_transitions.items(), key=lambda x: x[1])
        return optimal_transitions[0]

    def _generate_efficiency_recommendations(self, results) -> List[str]:
        """Generate efficiency improvement recommendations."""
        recommendations = []

        if not results:
            return ["No data available for recommendations"]

        # Analyze execution times
        execution_times = [r.execution_time_ms / 1000.0 for r in results if r.execution_time_ms]
        if execution_times:
            avg_time = statistics.mean(execution_times)
            if avg_time > 30:
                recommendations.append("Consider optimizing workflow steps - average execution time is high")

        # Analyze agent transitions
        transitions = [r.agent_transitions for r in results if r.agent_transitions]
        if transitions:
            avg_transitions = statistics.mean(transitions)
            if avg_transitions > 6:
                recommendations.append("Reduce agent transitions - workflows may be over-engineered")
            elif avg_transitions < 2:
                recommendations.append("Consider adding more specialized agents for better task distribution")

        # Analyze success rates
        success_rates = [r.success_rate for r in results if r.success_rate]
        if success_rates:
            avg_success = statistics.mean(success_rates)
            if avg_success < 0.9:
                recommendations.append("Improve error handling and validation - success rate is below optimal")

        # Analyze quality scores
        quality_scores = [r.quality_score for r in results if r.quality_score]
        if quality_scores:
            avg_quality = statistics.mean(quality_scores)
            if avg_quality < 0.8:
                recommendations.append("Enhance agent prompts and capabilities - quality scores are suboptimal")

        if not recommendations:
            recommendations.append("Workflow efficiency is within acceptable parameters")

        return recommendations

    async def record_workflow_metrics(
        self,
        workflow_id: str,
        execution_time_ms: float,
        success_rate: float,
        quality_score: Optional[float] = None,
        agent_transitions: Optional[int] = None,
        tool_executions: Optional[int] = None,
        memory_usage_mb: Optional[float] = None,
        cpu_usage_percent: Optional[float] = None,
        error_count: int = 0
    ) -> bool:
        """
        Record workflow performance metrics.

        Args:
            workflow_id: Unique workflow identifier
            execution_time_ms: Total execution time in milliseconds
            success_rate: Success rate (0.0 to 1.0)
            quality_score: Optional quality score (0.0 to 1.0)
            agent_transitions: Number of agent transitions
            tool_executions: Number of tool executions
            memory_usage_mb: Memory usage in MB
            cpu_usage_percent: CPU usage percentage
            error_count: Number of errors encountered

        Returns:
            True if metrics were recorded successfully
        """
        try:
            session = self.SessionLocal()

            # Insert or update metrics
            query = text("""
                INSERT INTO workflow_performance_metrics (
                    workflow_id, execution_time_ms, success_rate, quality_score,
                    agent_transitions, tool_executions, memory_usage_mb,
                    cpu_usage_percent, error_count, recorded_at
                ) VALUES (
                    :workflow_id, :execution_time_ms, :success_rate, :quality_score,
                    :agent_transitions, :tool_executions, :memory_usage_mb,
                    :cpu_usage_percent, :error_count, :recorded_at
                )
                ON CONFLICT (workflow_id) DO UPDATE SET
                    execution_time_ms = EXCLUDED.execution_time_ms,
                    success_rate = EXCLUDED.success_rate,
                    quality_score = EXCLUDED.quality_score,
                    agent_transitions = EXCLUDED.agent_transitions,
                    tool_executions = EXCLUDED.tool_executions,
                    memory_usage_mb = EXCLUDED.memory_usage_mb,
                    cpu_usage_percent = EXCLUDED.cpu_usage_percent,
                    error_count = EXCLUDED.error_count,
                    recorded_at = EXCLUDED.recorded_at
            """)

            session.execute(query, {
                "workflow_id": workflow_id,
                "execution_time_ms": execution_time_ms,
                "success_rate": success_rate,
                "quality_score": quality_score,
                "agent_transitions": agent_transitions,
                "tool_executions": tool_executions,
                "memory_usage_mb": memory_usage_mb,
                "cpu_usage_percent": cpu_usage_percent,
                "error_count": error_count,
                "recorded_at": datetime.now(timezone.utc)
            })

            session.commit()
            session.close()

            return True

        except Exception as e:
            self.logger.error(f"Error recording workflow metrics: {e}")
            return False
