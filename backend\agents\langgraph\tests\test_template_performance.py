"""
Performance tests for the LangGraph template system.

This module tests the performance characteristics of the template system
including workflow creation speed, caching effectiveness, and memory usage.
"""

import pytest
import asyncio
import time
from unittest.mock import AsyncMock, patch
from concurrent.futures import ThreadPoolExecutor

from ..templates.template_manager import TemplateManager
from ..templates.simple_analysis_template import SimpleAnalysisTemplate
from ..templates.business_analysis_template import BusinessAnalysisTemplate
from ..templates.dashboard_generation_template import DashboardGenerationTemplate


class TestTemplatePerformance:
    """Test template system performance."""
    
    @pytest.fixture
    def manager(self):
        """Create template manager with caching enabled."""
        manager = TemplateManager(enable_caching=True, cache_ttl=3600)
        
        # Register all templates
        manager.register_template(SimpleAnalysisTemplate, "simple_analysis", "1.0.0")
        manager.register_template(BusinessAnalysisTemplate, "business_analysis", "1.0.0")
        manager.register_template(DashboardGenerationTemplate, "dashboard_generation", "1.0.0")
        
        return manager
    
    @pytest.mark.asyncio
    async def test_workflow_creation_speed(self, manager):
        """Test workflow creation speed."""
        params = {
            "data_source": "business_profile",
            "user_id": "test_user",
            "business_profile_id": "profile_123"
        }
        
        # Mock event bus to avoid actual event publishing overhead
        with patch('backend.agents.langgraph.templates.base_template.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            # Measure workflow creation time
            start_time = time.time()
            
            workflow = await manager.create_workflow("simple_analysis", params, use_cache=False)
            
            creation_time = time.time() - start_time
            
            assert workflow is not None
            assert creation_time < 1.0  # Should create workflow in less than 1 second
            
            print(f"Workflow creation time: {creation_time:.3f} seconds")
    
    @pytest.mark.asyncio
    async def test_cache_performance(self, manager):
        """Test caching performance improvement."""
        params = {
            "data_source": "business_profile",
            "user_id": "test_user",
            "business_profile_id": "profile_123"
        }
        
        # Mock event bus
        with patch('backend.agents.langgraph.templates.base_template.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            # First creation (cache miss)
            start_time = time.time()
            workflow1 = await manager.create_workflow("simple_analysis", params, use_cache=True)
            first_creation_time = time.time() - start_time
            
            # Second creation (cache hit)
            start_time = time.time()
            workflow2 = await manager.create_workflow("simple_analysis", params, use_cache=True)
            second_creation_time = time.time() - start_time
            
            assert workflow1 is not None
            assert workflow2 is not None
            
            # Cache hit should be significantly faster
            assert second_creation_time < first_creation_time * 0.5
            
            # Check cache metrics
            metrics = manager.get_metrics()
            assert metrics["cache_hits"] >= 1
            assert metrics["cache_hit_rate"] > 0
            
            print(f"First creation (cache miss): {first_creation_time:.3f} seconds")
            print(f"Second creation (cache hit): {second_creation_time:.3f} seconds")
            print(f"Cache speedup: {first_creation_time / second_creation_time:.1f}x")
    
    @pytest.mark.asyncio
    async def test_concurrent_workflow_creation(self, manager):
        """Test concurrent workflow creation performance."""
        params_template = {
            "data_source": "business_profile",
            "user_id": "test_user_{}",
            "business_profile_id": "profile_{}"
        }
        
        # Mock event bus
        with patch('backend.agents.langgraph.templates.base_template.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            async def create_workflow(index):
                params = {
                    "data_source": "business_profile",
                    "user_id": f"test_user_{index}",
                    "business_profile_id": f"profile_{index}"
                }
                return await manager.create_workflow("simple_analysis", params, use_cache=False)
            
            # Create multiple workflows concurrently
            num_workflows = 10
            start_time = time.time()
            
            tasks = [create_workflow(i) for i in range(num_workflows)]
            workflows = await asyncio.gather(*tasks)
            
            total_time = time.time() - start_time
            avg_time_per_workflow = total_time / num_workflows
            
            assert len(workflows) == num_workflows
            assert all(w is not None for w in workflows)
            assert avg_time_per_workflow < 0.5  # Average should be less than 0.5 seconds
            
            print(f"Created {num_workflows} workflows in {total_time:.3f} seconds")
            print(f"Average time per workflow: {avg_time_per_workflow:.3f} seconds")
    
    @pytest.mark.asyncio
    async def test_template_validation_performance(self, manager):
        """Test parameter validation performance."""
        params = {
            "data_source": "business_profile",
            "user_id": "test_user",
            "business_profile_id": "profile_123",
            "analysis_type": "comprehensive",
            "output_format": "markdown",
            "include_visualizations": True,
            "focus_areas": ["overview", "trends", "insights"]
        }
        
        # Test validation speed
        num_validations = 1000
        start_time = time.time()
        
        for _ in range(num_validations):
            is_valid, errors = await manager.validate_template_parameters("simple_analysis", params)
            assert is_valid is True
        
        total_time = time.time() - start_time
        avg_time_per_validation = total_time / num_validations
        
        assert avg_time_per_validation < 0.001  # Should validate in less than 1ms
        
        print(f"Validated {num_validations} parameter sets in {total_time:.3f} seconds")
        print(f"Average validation time: {avg_time_per_validation * 1000:.3f} ms")
    
    @pytest.mark.asyncio
    async def test_complex_template_performance(self, manager):
        """Test performance of complex business analysis template."""
        params = {
            "business_profile_id": "profile_123",
            "user_id": "test_user",
            "analysis_depth": "comprehensive",
            "include_competitive_analysis": True,
            "include_financial_projections": True,
            "include_strategic_recommendations": True,
            "market_focus_areas": ["target_market", "market_size", "growth_trends"],
            "projection_time_horizon": "12_months"
        }
        
        # Mock event bus
        with patch('backend.agents.langgraph.templates.base_template.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            # Measure complex workflow creation time
            start_time = time.time()
            
            workflow = await manager.create_workflow("business_analysis", params, use_cache=False)
            
            creation_time = time.time() - start_time
            
            assert workflow is not None
            assert creation_time < 2.0  # Complex workflow should still create in less than 2 seconds
            
            print(f"Complex workflow creation time: {creation_time:.3f} seconds")
    
    @pytest.mark.asyncio
    async def test_dashboard_template_performance(self, manager):
        """Test performance of dashboard generation template."""
        params = {
            "dashboard_name": "Performance Test Dashboard",
            "user_id": "test_user",
            "dashboard_type": "business_overview",
            "data_sources": ["business_profile", "analytics_data", "sales_data"],
            "widget_types": ["metric", "chart", "table", "text"],
            "include_interactive_features": True
        }
        
        # Mock event bus
        with patch('backend.agents.langgraph.templates.base_template.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            # Measure dashboard workflow creation time
            start_time = time.time()
            
            workflow = await manager.create_workflow("dashboard_generation", params, use_cache=False)
            
            creation_time = time.time() - start_time
            
            assert workflow is not None
            assert creation_time < 1.5  # Dashboard workflow should create in less than 1.5 seconds
            
            print(f"Dashboard workflow creation time: {creation_time:.3f} seconds")
    
    def test_cache_memory_efficiency(self, manager):
        """Test cache memory efficiency."""
        import sys
        
        # Get initial cache stats
        initial_stats = manager.cache.get_stats()
        initial_memory = sys.getsizeof(manager.cache.cache)
        
        # Add many entries to cache
        mock_workflow = object()
        num_entries = 100
        
        for i in range(num_entries):
            cache_key = f"test_key_{i}"
            manager.cache.set(cache_key, mock_workflow)
        
        # Check final stats
        final_stats = manager.cache.get_stats()
        final_memory = sys.getsizeof(manager.cache.cache)
        
        assert final_stats["total_entries"] >= num_entries
        
        # Memory usage should be reasonable (less than 1MB for 100 entries)
        memory_per_entry = (final_memory - initial_memory) / num_entries
        assert memory_per_entry < 10000  # Less than 10KB per entry
        
        print(f"Cache memory usage: {final_memory - initial_memory} bytes for {num_entries} entries")
        print(f"Memory per entry: {memory_per_entry:.1f} bytes")
    
    @pytest.mark.asyncio
    async def test_template_registry_performance(self, manager):
        """Test template registry performance."""
        # Test template listing performance
        start_time = time.time()
        
        for _ in range(1000):
            templates = manager.list_templates()
            assert len(templates) >= 3
        
        listing_time = time.time() - start_time
        avg_listing_time = listing_time / 1000
        
        assert avg_listing_time < 0.001  # Should list templates in less than 1ms
        
        print(f"Template listing time: {avg_listing_time * 1000:.3f} ms average")
        
        # Test template retrieval performance
        start_time = time.time()
        
        for _ in range(1000):
            schema = manager.get_template_schema("simple_analysis")
            assert schema is not None
        
        retrieval_time = time.time() - start_time
        avg_retrieval_time = retrieval_time / 1000
        
        assert avg_retrieval_time < 0.001  # Should retrieve schema in less than 1ms
        
        print(f"Schema retrieval time: {avg_retrieval_time * 1000:.3f} ms average")
    
    @pytest.mark.asyncio
    async def test_workflow_creation_under_load(self, manager):
        """Test workflow creation under high load."""
        # Mock event bus
        with patch('backend.agents.langgraph.templates.base_template.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            async def create_multiple_workflows(batch_size):
                tasks = []
                for i in range(batch_size):
                    params = {
                        "data_source": "business_profile",
                        "user_id": f"load_test_user_{i}",
                        "business_profile_id": f"profile_{i}"
                    }
                    task = manager.create_workflow("simple_analysis", params, use_cache=False)
                    tasks.append(task)
                
                return await asyncio.gather(*tasks)
            
            # Test different batch sizes
            batch_sizes = [10, 25, 50]
            
            for batch_size in batch_sizes:
                start_time = time.time()
                workflows = await create_multiple_workflows(batch_size)
                total_time = time.time() - start_time
                
                assert len(workflows) == batch_size
                assert all(w is not None for w in workflows)
                
                throughput = batch_size / total_time
                
                # Should maintain reasonable throughput even under load
                assert throughput > 5  # At least 5 workflows per second
                
                print(f"Batch size {batch_size}: {throughput:.1f} workflows/second")
    
    @pytest.mark.asyncio
    async def test_cache_hit_rate_optimization(self, manager):
        """Test cache hit rate optimization."""
        # Mock event bus
        with patch('backend.agents.langgraph.templates.base_template.event_bus') as mock_event_bus:
            mock_event_bus.publish = AsyncMock()
            
            # Create workflows with repeated parameters to test cache effectiveness
            common_params = {
                "data_source": "business_profile",
                "user_id": "cache_test_user",
                "business_profile_id": "cache_test_profile"
            }
            
            # Create same workflow multiple times
            num_requests = 20
            
            for _ in range(num_requests):
                workflow = await manager.create_workflow("simple_analysis", common_params, use_cache=True)
                assert workflow is not None
            
            # Check cache metrics
            metrics = manager.get_metrics()
            cache_hit_rate = metrics["cache_hit_rate"]
            
            # Should achieve high cache hit rate with repeated requests
            assert cache_hit_rate > 0.8  # At least 80% cache hit rate
            
            print(f"Cache hit rate: {cache_hit_rate:.1%}")
            print(f"Cache hits: {metrics['cache_hits']}")
            print(f"Cache misses: {metrics['cache_misses']}")


class TestTemplateScalability:
    """Test template system scalability."""
    
    @pytest.mark.asyncio
    async def test_large_parameter_sets(self):
        """Test handling of large parameter sets."""
        template = SimpleAnalysisTemplate()
        
        # Create large parameter set
        large_params = {
            "data_source": "business_profile",
            "user_id": "scalability_test_user",
            "business_profile_id": "scalability_test_profile",
            "focus_areas": [f"area_{i}" for i in range(100)],  # Large list
            "custom_data": {f"key_{i}": f"value_{i}" for i in range(1000)}  # Large dict
        }
        
        # Test validation performance with large parameters
        start_time = time.time()
        is_valid, errors = template.validate_parameters(large_params)
        validation_time = time.time() - start_time
        
        # Should handle large parameters efficiently
        assert validation_time < 0.1  # Less than 100ms
        
        print(f"Large parameter validation time: {validation_time * 1000:.1f} ms")
    
    @pytest.mark.asyncio
    async def test_many_template_versions(self):
        """Test handling of many template versions."""
        manager = TemplateManager(enable_caching=False)
        
        # Register many versions of the same template
        num_versions = 50
        
        start_time = time.time()
        
        for i in range(num_versions):
            version = f"1.{i}.0"
            manager.register_template(SimpleAnalysisTemplate, "scalability_test", version, set_as_default=(i == 0))
        
        registration_time = time.time() - start_time
        
        # Test retrieval performance
        start_time = time.time()
        
        for i in range(num_versions):
            version = f"1.{i}.0"
            template_class = manager.registry.get_template_class("scalability_test", version)
            assert template_class is not None
        
        retrieval_time = time.time() - start_time
        
        # Should handle many versions efficiently
        assert registration_time < 1.0  # Less than 1 second to register 50 versions
        assert retrieval_time < 0.1  # Less than 100ms to retrieve all versions
        
        print(f"Registered {num_versions} versions in {registration_time:.3f} seconds")
        print(f"Retrieved {num_versions} versions in {retrieval_time:.3f} seconds")
