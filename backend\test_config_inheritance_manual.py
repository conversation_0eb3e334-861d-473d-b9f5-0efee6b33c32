#!/usr/bin/env python3
"""
Manual test script for configuration inheritance.
"""

import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Direct import to avoid dependency issues
sys.path.append(str(backend_dir / "agents" / "langgraph" / "config"))
from config_manager import Config<PERSON><PERSON><PERSON>

def test_config_inheritance():
    """Test configuration inheritance functionality."""
    print("Testing Configuration Inheritance...")
    
    try:
        # Initialize ConfigManager with development environment
        config_manager = ConfigManager(environment="development")
        
        print(f"Environment: {config_manager.environment}")
        print(f"Config directory: {config_manager.config_dir}")
        
        # Test inheritance paths
        inheritance_info = config_manager.get_inheritance_info()
        print("\nInheritance Paths:")
        for i, path_info in enumerate(inheritance_info["inheritance_paths"]):
            print(f"  {i+1}. {path_info['path']} (exists: {path_info['exists']})")
            if path_info['files']:
                print(f"     Files: {', '.join(path_info['files'])}")
        
        # Test configuration summary
        summary = config_manager.get_configuration_summary()
        print(f"\nConfiguration Summary:")
        print(f"  Environment: {summary['environment']}")
        print(f"  LangGraph enabled: {summary['langgraph_enabled']}")
        print(f"  Migration phase: {summary['migration_phase']}")
        print(f"  Rollout percentage: {summary['rollout_percentage']}")
        print(f"  Database host: {summary['database_host']}")
        print(f"  Logging level: {summary['logging_level']}")
        
        # Test specific configurations
        if config_manager.langgraph_config:
            print(f"\nLangGraph Configuration:")
            print(f"  Enabled: {config_manager.langgraph_config.enabled}")
            print(f"  Max workflow duration: {config_manager.langgraph_config.max_workflow_duration}")
            print(f"  Rollout percentage: {config_manager.langgraph_config.rollout_percentage}")
        
        if config_manager.database_config:
            print(f"\nDatabase Configuration:")
            print(f"  Host: {config_manager.database_config.host}")
            print(f"  Port: {config_manager.database_config.port}")
            print(f"  Database: {config_manager.database_config.database}")
        
        if config_manager.logging_config:
            print(f"\nLogging Configuration:")
            print(f"  Level: {config_manager.logging_config.level}")
            print(f"  File logging: {config_manager.logging_config.enable_file_logging}")
            print(f"  Log file path: {config_manager.logging_config.log_file_path}")
        
        print("\n✅ Configuration inheritance test completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Configuration inheritance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_config_inheritance()
    sys.exit(0 if success else 1)
