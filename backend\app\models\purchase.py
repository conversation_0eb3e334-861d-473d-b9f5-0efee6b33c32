"""
Purchase models for the Datagenius backend.

This module provides Pydantic models for purchase functionality.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any

from pydantic import BaseModel


class PurchasedItemBase(BaseModel):
    """Base model for purchased item data."""
    persona_id: str
    quantity: int = 1
    price: float


class PurchasedItemCreate(PurchasedItemBase):
    """Model for creating a new purchased item."""
    pass


class PurchasedItemResponse(PurchasedItemBase):
    """Model for purchased item data returned to the client."""
    id: str
    purchase_id: str
    created_at: datetime

    class Config:
        from_attributes = True


class PurchaseBase(BaseModel):
    """Base model for purchase data."""
    total_amount: float
    payment_status: str  # "pending", "completed", "failed"
    payment_method: Optional[str] = None
    purchase_metadata: Optional[Dict[str, Any]] = None


class PurchaseCreate(PurchaseBase):
    """Model for creating a new purchase."""
    pass


class PurchaseUpdate(BaseModel):
    """Model for updating a purchase."""
    total_amount: Optional[float] = None
    payment_status: Optional[str] = None
    payment_method: Optional[str] = None
    purchase_metadata: Optional[Dict[str, Any]] = None


class PurchaseResponse(PurchaseBase):
    """Model for purchase data returned to the client."""
    id: str
    user_id: int
    created_at: datetime
    items: List[PurchasedItemResponse]

    class Config:
        from_attributes = True


class PurchaseListResponse(BaseModel):
    """Model for purchase list response."""
    purchases: List[PurchaseResponse]


class CreatePurchaseRequest(BaseModel):
    """Model for creating a purchase from cart."""
    payment_method: str
