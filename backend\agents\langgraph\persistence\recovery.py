"""
Workflow Recovery System for LangGraph Workflows.

This module provides comprehensive recovery capabilities for failed or
interrupted workflows, including automatic recovery strategies and
manual recovery options.
"""

import logging
import json
import uuid
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timezone
from enum import Enum

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError

from ..states.agent_state import DatageniusAgentState, create_initial_state
from .checkpointer import DatageniusCheckpointer
from app.config import get_config

logger = logging.getLogger(__name__)


class RecoveryTrigger(str, Enum):
    """Types of recovery triggers."""
    ERROR = "error"
    TIMEOUT = "timeout"
    MANUAL = "manual"
    SYSTEM_RESTART = "system_restart"
    QUALITY_FAILURE = "quality_failure"


class RecoveryStrategy(str, Enum):
    """Recovery strategies."""
    RESUME = "resume"
    RESTART = "restart"
    ROLLBACK = "rollback"
    ESCALATE = "escalate"
    SKIP = "skip"


class WorkflowRecovery:
    """
    Comprehensive workflow recovery system.
    
    This class provides automatic and manual recovery capabilities
    for LangGraph workflows, including state restoration, error
    handling, and recovery strategy selection.
    """

    def __init__(self, database_url: str = None, checkpointer: DatageniusCheckpointer = None):
        """
        Initialize the recovery system.
        
        Args:
            database_url: Database connection URL
            checkpointer: Checkpointer instance for state management
        """
        config = get_config()
        self.database_url = database_url or config.database.url
        self.engine = create_engine(self.database_url)
        self.SessionLocal = sessionmaker(bind=self.engine)
        self.checkpointer = checkpointer or DatageniusCheckpointer(database_url)
        self.logger = logging.getLogger(__name__)
        
        # Recovery strategy rules
        self.recovery_rules = {
            "timeout": RecoveryStrategy.RESUME,
            "system_error": RecoveryStrategy.RESTART,
            "agent_error": RecoveryStrategy.ROLLBACK,
            "tool_error": RecoveryStrategy.SKIP,
            "quality_failure": RecoveryStrategy.ROLLBACK,
            "critical_error": RecoveryStrategy.ESCALATE
        }

    async def recover_workflow(
        self, 
        workflow_id: str, 
        trigger: RecoveryTrigger,
        error_context: Optional[Dict[str, Any]] = None,
        strategy: Optional[RecoveryStrategy] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Recover a failed workflow.
        
        Args:
            workflow_id: Workflow identifier
            trigger: What triggered the recovery
            error_context: Context about the error that caused recovery
            strategy: Specific recovery strategy to use (optional)
            
        Returns:
            Tuple of (success, recovery_info)
        """
        try:
            recovery_id = await self._log_recovery_attempt(workflow_id, trigger, error_context)
            
            # Determine recovery strategy
            if not strategy:
                strategy = await self._determine_recovery_strategy(workflow_id, trigger, error_context)
            
            self.logger.info(f"Starting recovery for workflow {workflow_id} using strategy {strategy}")
            
            # Execute recovery strategy
            if strategy == RecoveryStrategy.RESUME:
                success, info = await self._resume_workflow(workflow_id, error_context)
            elif strategy == RecoveryStrategy.RESTART:
                success, info = await self._restart_workflow(workflow_id, error_context)
            elif strategy == RecoveryStrategy.ROLLBACK:
                success, info = await self._rollback_workflow(workflow_id, error_context)
            elif strategy == RecoveryStrategy.ESCALATE:
                success, info = await self._escalate_workflow(workflow_id, error_context)
            elif strategy == RecoveryStrategy.SKIP:
                success, info = await self._skip_failed_step(workflow_id, error_context)
            else:
                raise ValueError(f"Unknown recovery strategy: {strategy}")
            
            # Log recovery outcome
            await self._log_recovery_outcome(recovery_id, success, strategy, info)
            
            recovery_result = {
                "recovery_id": recovery_id,
                "strategy": strategy,
                "success": success,
                "info": info,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            return success, recovery_result
            
        except Exception as e:
            self.logger.error(f"Recovery failed for workflow {workflow_id}: {e}")
            return False, {"error": str(e), "strategy": strategy}

    async def _determine_recovery_strategy(
        self, 
        workflow_id: str, 
        trigger: RecoveryTrigger,
        error_context: Optional[Dict[str, Any]]
    ) -> RecoveryStrategy:
        """Determine the best recovery strategy based on context."""
        try:
            # Get workflow history and error patterns
            error_history = await self._get_workflow_error_history(workflow_id)
            recovery_history = await self._get_recovery_history(workflow_id)
            
            # Analyze error context
            error_type = "general"
            if error_context:
                error_type = error_context.get("type", "general")
                error_severity = error_context.get("severity", "medium")
            
            # Apply recovery rules
            if error_type in self.recovery_rules:
                base_strategy = self.recovery_rules[error_type]
            else:
                base_strategy = RecoveryStrategy.RESUME
            
            # Adjust based on history
            if len(recovery_history) > 3:
                # Too many recovery attempts, escalate
                return RecoveryStrategy.ESCALATE
            
            if len(error_history) > 5:
                # Persistent errors, restart from beginning
                return RecoveryStrategy.RESTART
            
            # Check for specific patterns
            if trigger == RecoveryTrigger.TIMEOUT:
                return RecoveryStrategy.RESUME
            elif trigger == RecoveryTrigger.SYSTEM_RESTART:
                return RecoveryStrategy.RESUME
            elif trigger == RecoveryTrigger.QUALITY_FAILURE:
                return RecoveryStrategy.ROLLBACK
            
            return base_strategy
            
        except Exception as e:
            self.logger.error(f"Error determining recovery strategy: {e}")
            return RecoveryStrategy.RESUME  # Safe default

    async def _resume_workflow(self, workflow_id: str, error_context: Optional[Dict[str, Any]]) -> Tuple[bool, Dict[str, Any]]:
        """Resume workflow from the last checkpoint."""
        try:
            # Find the latest checkpoint
            checkpoints = await self.checkpointer.list_checkpoints(workflow_id, limit=1)
            
            if not checkpoints:
                return False, {"error": "No checkpoints found for resume"}
            
            latest_checkpoint = checkpoints[0]
            checkpoint_id = latest_checkpoint["checkpoint_id"]
            thread_id = latest_checkpoint["thread_id"]
            
            # Load the checkpoint state
            checkpoint_data = await self.checkpointer.load_checkpoint(checkpoint_id, thread_id)
            
            if not checkpoint_data:
                return False, {"error": "Failed to load checkpoint data"}
            
            state, metadata = checkpoint_data
            
            # Clean up any error state
            if isinstance(state, dict):
                # Remove recent errors that might have caused the failure
                error_history = state.get("error_history", [])
                if error_history:
                    # Keep only older errors, remove recent ones that might be transient
                    state["error_history"] = error_history[:-2] if len(error_history) > 2 else []
                
                # Reset workflow status
                state["workflow_status"] = "running"
                
                # Add recovery note
                state["recovery_info"] = {
                    "recovered_at": datetime.now(timezone.utc).isoformat(),
                    "recovery_strategy": "resume",
                    "checkpoint_id": checkpoint_id
                }
            
            return True, {
                "checkpoint_id": checkpoint_id,
                "resumed_from": latest_checkpoint["created_at"],
                "state_restored": True
            }
            
        except Exception as e:
            self.logger.error(f"Error resuming workflow: {e}")
            return False, {"error": str(e)}

    async def _restart_workflow(self, workflow_id: str, error_context: Optional[Dict[str, Any]]) -> Tuple[bool, Dict[str, Any]]:
        """Restart workflow from the beginning."""
        try:
            # Get original workflow information
            session = self.SessionLocal()
            
            query = text("""
                SELECT name, description, user_id, session_id, context
                FROM workflow_executions
                WHERE id = :workflow_id
            """)
            
            result = session.execute(query, {"workflow_id": workflow_id}).fetchone()
            session.close()
            
            if not result:
                return False, {"error": "Workflow not found"}
            
            # Create fresh initial state
            initial_state = create_initial_state(
                user_id=result.user_id,
                conversation_id=result.session_id,
                workflow_id=workflow_id,
                business_profile_id=None  # Will be restored from context
            )
            
            # Restore business context from original workflow
            if result.context:
                try:
                    original_context = json.loads(result.context)
                    initial_state["business_context"] = original_context
                    initial_state["business_profile_id"] = original_context.get("business_profile_id")
                except:
                    pass
            
            # Add restart information
            initial_state["recovery_info"] = {
                "recovered_at": datetime.now(timezone.utc).isoformat(),
                "recovery_strategy": "restart",
                "original_workflow_id": workflow_id,
                "restart_reason": error_context.get("type", "unknown") if error_context else "unknown"
            }
            
            # Save new initial checkpoint
            checkpoint_id = f"restart_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            thread_id = f"thread_{workflow_id}"
            
            await self.checkpointer.save_checkpoint(
                workflow_id=workflow_id,
                checkpoint_id=checkpoint_id,
                thread_id=thread_id,
                state=initial_state,
                metadata={"recovery": True, "strategy": "restart"}
            )
            
            return True, {
                "checkpoint_id": checkpoint_id,
                "restarted": True,
                "fresh_state": True
            }
            
        except Exception as e:
            self.logger.error(f"Error restarting workflow: {e}")
            return False, {"error": str(e)}

    async def _log_recovery_attempt(
        self,
        workflow_id: str,
        trigger: RecoveryTrigger,
        error_context: Optional[Dict[str, Any]]
    ) -> str:
        """Log a recovery attempt."""
        try:
            session = self.SessionLocal()
            recovery_id = str(uuid.uuid4())

            insert_query = text("""
                INSERT INTO workflow_recovery_log
                (id, workflow_id, recovery_trigger, attempted_at, error_context)
                VALUES (:id, :workflow_id, :recovery_trigger, :attempted_at, :error_context)
            """)

            session.execute(insert_query, {
                "id": recovery_id,
                "workflow_id": workflow_id,
                "recovery_trigger": trigger.value,
                "attempted_at": datetime.now(timezone.utc),
                "error_context": json.dumps(error_context or {}, default=str)
            })

            session.commit()
            session.close()

            return recovery_id

        except Exception as e:
            self.logger.error(f"Error logging recovery attempt: {e}")
            if 'session' in locals():
                session.rollback()
                session.close()
            return str(uuid.uuid4())  # Return a UUID anyway

    async def _log_recovery_outcome(
        self,
        recovery_id: str,
        success: bool,
        strategy: RecoveryStrategy,
        info: Dict[str, Any]
    ):
        """Log the outcome of a recovery attempt."""
        try:
            session = self.SessionLocal()

            update_query = text("""
                UPDATE workflow_recovery_log
                SET
                    recovery_strategy = :strategy,
                    completed_at = :completed_at,
                    success = :success,
                    recovery_actions = :recovery_actions,
                    outcome_description = :outcome_description
                WHERE id = :recovery_id
            """)

            session.execute(update_query, {
                "recovery_id": recovery_id,
                "strategy": strategy.value,
                "completed_at": datetime.now(timezone.utc),
                "success": success,
                "recovery_actions": json.dumps(info, default=str),
                "outcome_description": "Recovery successful" if success else f"Recovery failed: {info.get('error', 'Unknown error')}"
            })

            session.commit()
            session.close()

        except Exception as e:
            self.logger.error(f"Error logging recovery outcome: {e}")
            if 'session' in locals():
                session.rollback()
                session.close()

    async def _get_workflow_error_history(self, workflow_id: str) -> List[Dict[str, Any]]:
        """Get error history for a workflow."""
        try:
            # Get errors from checkpoints
            checkpoints = await self.checkpointer.list_checkpoints(workflow_id, limit=10)

            all_errors = []
            for checkpoint in checkpoints:
                checkpoint_data = await self.checkpointer.load_checkpoint(
                    checkpoint["checkpoint_id"],
                    checkpoint["thread_id"]
                )

                if checkpoint_data:
                    state, _ = checkpoint_data
                    if isinstance(state, dict):
                        error_history = state.get("error_history", [])
                        all_errors.extend(error_history)

            return all_errors

        except Exception as e:
            self.logger.error(f"Error getting workflow error history: {e}")
            return []

    async def _get_recovery_history(self, workflow_id: str) -> List[Dict[str, Any]]:
        """Get recovery history for a workflow."""
        try:
            session = self.SessionLocal()

            query = text("""
                SELECT recovery_trigger, recovery_strategy, attempted_at,
                       completed_at, success, outcome_description
                FROM workflow_recovery_log
                WHERE workflow_id = :workflow_id
                ORDER BY attempted_at DESC
                LIMIT 10
            """)

            results = session.execute(query, {"workflow_id": workflow_id}).fetchall()
            session.close()

            recovery_history = []
            for result in results:
                recovery_history.append({
                    "trigger": result.recovery_trigger,
                    "strategy": result.recovery_strategy,
                    "attempted_at": result.attempted_at.isoformat() if result.attempted_at else None,
                    "completed_at": result.completed_at.isoformat() if result.completed_at else None,
                    "success": result.success,
                    "outcome": result.outcome_description
                })

            return recovery_history

        except Exception as e:
            self.logger.error(f"Error getting recovery history: {e}")
            if 'session' in locals():
                session.close()
            return []

    async def get_recoverable_workflows(self) -> List[Dict[str, Any]]:
        """Get list of workflows that can be recovered."""
        try:
            session = self.SessionLocal()

            query = text("""
                SELECT DISTINCT we.id, we.name, we.status, we.updated_at,
                       COUNT(lc.id) as checkpoint_count,
                       MAX(lc.created_at) as last_checkpoint
                FROM workflow_executions we
                LEFT JOIN langgraph_checkpoints lc ON we.id = lc.workflow_id
                WHERE we.status IN ('failed', 'error', 'timeout', 'interrupted')
                   OR (we.status = 'running' AND we.updated_at < NOW() - INTERVAL '1 hour')
                GROUP BY we.id, we.name, we.status, we.updated_at
                HAVING COUNT(lc.id) > 0
                ORDER BY we.updated_at DESC
            """)

            results = session.execute(query).fetchall()
            session.close()

            recoverable_workflows = []
            for result in results:
                recoverable_workflows.append({
                    "workflow_id": str(result.id),
                    "name": result.name,
                    "status": result.status,
                    "updated_at": result.updated_at.isoformat(),
                    "checkpoint_count": result.checkpoint_count,
                    "last_checkpoint": result.last_checkpoint.isoformat() if result.last_checkpoint else None
                })

            return recoverable_workflows

        except Exception as e:
            self.logger.error(f"Error getting recoverable workflows: {e}")
            if 'session' in locals():
                session.close()
            return []

    async def auto_recover_stalled_workflows(self) -> Dict[str, Any]:
        """Automatically recover stalled workflows."""
        try:
            recoverable_workflows = await self.get_recoverable_workflows()

            recovery_results = {
                "attempted": 0,
                "successful": 0,
                "failed": 0,
                "details": []
            }

            for workflow in recoverable_workflows:
                workflow_id = workflow["workflow_id"]

                # Determine trigger based on status
                if workflow["status"] in ["failed", "error"]:
                    trigger = RecoveryTrigger.ERROR
                elif workflow["status"] == "timeout":
                    trigger = RecoveryTrigger.TIMEOUT
                else:
                    trigger = RecoveryTrigger.SYSTEM_RESTART

                # Attempt recovery
                success, info = await self.recover_workflow(workflow_id, trigger)

                recovery_results["attempted"] += 1
                if success:
                    recovery_results["successful"] += 1
                else:
                    recovery_results["failed"] += 1

                recovery_results["details"].append({
                    "workflow_id": workflow_id,
                    "workflow_name": workflow["name"],
                    "success": success,
                    "info": info
                })

            self.logger.info(f"Auto-recovery completed: {recovery_results['successful']}/{recovery_results['attempted']} successful")
            return recovery_results

        except Exception as e:
            self.logger.error(f"Error in auto-recovery: {e}")
            return {"error": str(e), "attempted": 0, "successful": 0, "failed": 0}

    async def _rollback_workflow(self, workflow_id: str, error_context: Optional[Dict[str, Any]]) -> Tuple[bool, Dict[str, Any]]:
        """Rollback workflow to a previous stable checkpoint."""
        try:
            # Get recent checkpoints
            checkpoints = await self.checkpointer.list_checkpoints(workflow_id, limit=5)
            
            if len(checkpoints) < 2:
                # Not enough checkpoints to rollback, try restart instead
                return await self._restart_workflow(workflow_id, error_context)
            
            # Find a stable checkpoint (skip the most recent one)
            stable_checkpoint = None
            for checkpoint in checkpoints[1:]:  # Skip the first (most recent)
                # Load and check the checkpoint
                checkpoint_data = await self.checkpointer.load_checkpoint(
                    checkpoint["checkpoint_id"], 
                    checkpoint["thread_id"]
                )
                
                if checkpoint_data:
                    state, metadata = checkpoint_data
                    # Check if this checkpoint has fewer errors
                    error_count = len(state.get("error_history", []))
                    if error_count < 3:  # Arbitrary threshold for "stable"
                        stable_checkpoint = checkpoint
                        break
            
            if not stable_checkpoint:
                return False, {"error": "No stable checkpoint found for rollback"}
            
            # Load the stable checkpoint
            checkpoint_data = await self.checkpointer.load_checkpoint(
                stable_checkpoint["checkpoint_id"],
                stable_checkpoint["thread_id"]
            )
            
            if not checkpoint_data:
                return False, {"error": "Failed to load rollback checkpoint"}
            
            state, metadata = checkpoint_data
            
            # Update state for rollback
            if isinstance(state, dict):
                state["workflow_status"] = "running"
                state["recovery_info"] = {
                    "recovered_at": datetime.now(timezone.utc).isoformat(),
                    "recovery_strategy": "rollback",
                    "rollback_checkpoint": stable_checkpoint["checkpoint_id"],
                    "rollback_reason": error_context.get("type", "unknown") if error_context else "unknown"
                }
            
            # Save rollback checkpoint
            rollback_checkpoint_id = f"rollback_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            thread_id = stable_checkpoint["thread_id"]
            
            await self.checkpointer.save_checkpoint(
                workflow_id=workflow_id,
                checkpoint_id=rollback_checkpoint_id,
                thread_id=thread_id,
                state=state,
                metadata={"recovery": True, "strategy": "rollback"},
                parent_checkpoint_id=stable_checkpoint["checkpoint_id"]
            )
            
            return True, {
                "checkpoint_id": rollback_checkpoint_id,
                "rolled_back_to": stable_checkpoint["created_at"],
                "rollback_successful": True
            }
            
        except Exception as e:
            self.logger.error(f"Error rolling back workflow: {e}")
            return False, {"error": str(e)}

    async def _escalate_workflow(self, workflow_id: str, error_context: Optional[Dict[str, Any]]) -> Tuple[bool, Dict[str, Any]]:
        """Escalate workflow for human intervention."""
        try:
            # Mark workflow as requiring human intervention
            session = self.SessionLocal()
            
            update_query = text("""
                UPDATE workflow_executions 
                SET 
                    status = 'escalated',
                    workflow_metadata = workflow_metadata || :escalation_info,
                    updated_at = NOW()
                WHERE id = :workflow_id
            """)
            
            escalation_info = json.dumps({
                "escalated_at": datetime.now(timezone.utc).isoformat(),
                "escalation_reason": error_context.get("type", "unknown") if error_context else "unknown",
                "requires_human_intervention": True,
                "error_context": error_context
            })
            
            session.execute(update_query, {
                "workflow_id": workflow_id,
                "escalation_info": escalation_info
            })
            
            session.commit()
            session.close()
            
            # TODO: Send notification to administrators
            # This could integrate with notification systems
            
            return True, {
                "escalated": True,
                "requires_human_intervention": True,
                "escalation_reason": error_context.get("type", "unknown") if error_context else "unknown"
            }
            
        except Exception as e:
            self.logger.error(f"Error escalating workflow: {e}")
            if 'session' in locals():
                session.rollback()
                session.close()
            return False, {"error": str(e)}

    async def _skip_failed_step(self, workflow_id: str, error_context: Optional[Dict[str, Any]]) -> Tuple[bool, Dict[str, Any]]:
        """Skip the failed step and continue workflow."""
        try:
            # Get the latest checkpoint
            checkpoints = await self.checkpointer.list_checkpoints(workflow_id, limit=1)
            
            if not checkpoints:
                return False, {"error": "No checkpoints found"}
            
            latest_checkpoint = checkpoints[0]
            checkpoint_data = await self.checkpointer.load_checkpoint(
                latest_checkpoint["checkpoint_id"],
                latest_checkpoint["thread_id"]
            )
            
            if not checkpoint_data:
                return False, {"error": "Failed to load checkpoint"}
            
            state, metadata = checkpoint_data
            
            # Mark the failed step as skipped
            if isinstance(state, dict):
                state["workflow_status"] = "running"
                
                # Add skip information
                skipped_steps = state.get("skipped_steps", [])
                skipped_steps.append({
                    "step": state.get("current_step", "unknown"),
                    "reason": error_context.get("type", "error") if error_context else "error",
                    "skipped_at": datetime.now(timezone.utc).isoformat()
                })
                state["skipped_steps"] = skipped_steps
                
                # Clear recent errors
                state["error_history"] = state.get("error_history", [])[:-1]
                
                state["recovery_info"] = {
                    "recovered_at": datetime.now(timezone.utc).isoformat(),
                    "recovery_strategy": "skip",
                    "skipped_step": state.get("current_step", "unknown")
                }
            
            # Save updated state
            skip_checkpoint_id = f"skip_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            await self.checkpointer.save_checkpoint(
                workflow_id=workflow_id,
                checkpoint_id=skip_checkpoint_id,
                thread_id=latest_checkpoint["thread_id"],
                state=state,
                metadata={"recovery": True, "strategy": "skip"}
            )
            
            return True, {
                "checkpoint_id": skip_checkpoint_id,
                "step_skipped": True,
                "can_continue": True
            }
            
        except Exception as e:
            self.logger.error(f"Error skipping failed step: {e}")
            return False, {"error": str(e)}
