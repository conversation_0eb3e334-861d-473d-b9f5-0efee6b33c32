"""
Agent API endpoints for the Datagenius backend.

This module provides API endpoints for interacting with agents.
"""

import logging
import uuid
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks

from ..models.agent import AgentInvokeRequest, AgentResponse, AgentListResponse, AgentInfo
from ..models.auth import User
from ..auth import get_current_active_user
# Import the agent factory and dynamic manager
from agents.langgraph.core.agent_factory import agent_factory
from agents.langgraph.core.dynamic_agent_manager import dynamic_agent_manager

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/agents", tags=["Agents"])


@router.get("", response_model=AgentListResponse)
async def list_agents(
    current_user: User = Depends(get_current_active_user)
):
    """
    List all available agents.

    Returns a list of all registered agents and their capabilities.
    """
    logger.info(f"User {current_user.id} requested list of agents")

    # Get all available agent IDs from the factory
    persona_ids = agent_factory.get_available_agents()

    # Create a list of AgentInfo objects
    agents = []
    for persona_id in persona_ids:
        # Get configuration for the persona
        config = agent_factory.agent_configs.get(persona_id)

        if config:
            # Use configuration data if available
            capabilities = config.get("capabilities", [])
            agents.append(AgentInfo(
                persona_id=persona_id,
                name=config.get("name", persona_id),
                description=config.get("description", "No description available"),
                capabilities=capabilities
            ))
        else:
            # Fall back to creating a temporary instance if no configuration is available
            agent_class = agent_factory.agent_classes.get(persona_id)
            if agent_class:
                # Create a temporary instance to get capabilities
                agent = agent_class()
                capabilities = await agent.get_capabilities()

                # Add agent info to the list
                agents.append(AgentInfo(
                    persona_id=persona_id,
                    name=agent_class.__name__,
                    description=agent_class.__doc__ or "No description available",
                    capabilities=capabilities
                ))

    return AgentListResponse(agents=agents)


@router.post("/{persona_id}/invoke", response_model=AgentResponse)
async def invoke_agent(
    persona_id: str,
    request: AgentInvokeRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """
    Invoke an agent for a specific persona.

    Args:
        persona_id: ID of the persona to invoke
        request: Agent invocation request

    Returns:
        Agent response
    """
    logger.info(f"User {current_user.id} invoked agent for persona {persona_id}")

    try:
        # Create an agent node using the factory
        agent = agent_factory.create_agent_node(persona_id)
        if not agent:
            logger.error(f"No agent found for persona: {persona_id}")
            raise HTTPException(status_code=404, detail=f"No agent found for persona: {persona_id}")

        # Override configuration with request config if provided
        if request.config:
            # Merge the request config with the agent's existing config
            agent.config.update(request.config)

        # Process the message
        response = await agent.process_message(
            user_id=current_user.id,
            message=request.message,
            conversation_id=request.conversation_id or str(uuid.uuid4()),
            context=request.context
        )

        return AgentResponse(
            message=response.get("message", ""),
            conversation_id=request.conversation_id,
            metadata=response.get("metadata")
        )
    except Exception as e:
        logger.error(f"Error invoking agent for persona {persona_id}: {str(e)}", exc_info=True)
        return AgentResponse(
            message="An error occurred while processing your request.",
            conversation_id=request.conversation_id,
            error=str(e)
        )


# Dynamic Agent Management Endpoints

@router.get("/status")
async def get_agents_status(
    agent_id: Optional[str] = None,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get status information for agents.

    Args:
        agent_id: Optional specific agent ID to get status for

    Returns:
        Agent status information
    """
    try:
        status_info = dynamic_agent_manager.get_agent_status(agent_id)

        return {
            "status": "success",
            "data": status_info
        }

    except Exception as e:
        logger.error(f"Error getting agent status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/discover")
async def discover_agents(
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """
    Discover and load all available agents.

    Returns:
        Discovery results
    """
    try:
        logger.info(f"User {current_user.email} initiated agent discovery")

        # Run discovery
        results = await dynamic_agent_manager.discover_and_load_agents()

        return {
            "status": "success",
            "message": "Agent discovery completed",
            "results": results
        }

    except Exception as e:
        logger.error(f"Error in agent discovery: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reload/{agent_id}")
async def reload_agent(
    agent_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Reload a specific agent (unload then load).

    Args:
        agent_id: Agent identifier to reload

    Returns:
        Reload result
    """
    try:
        logger.info(f"User {current_user.email} reloading agent: {agent_id}")

        success = await dynamic_agent_manager.reload_agent(agent_id)

        if success:
            return {
                "status": "success",
                "message": f"Agent {agent_id} reloaded successfully"
            }
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Failed to reload agent {agent_id}"
            )

    except Exception as e:
        logger.error(f"Error reloading agent {agent_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/refresh")
async def refresh_all_agents(
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """
    Refresh all agents by re-discovering and reloading.

    Returns:
        Refresh results
    """
    try:
        logger.info(f"User {current_user.email} refreshing all agents")

        results = await dynamic_agent_manager.refresh_all_agents()

        return {
            "status": "success",
            "message": "All agents refreshed successfully",
            "results": results
        }

    except Exception as e:
        logger.error(f"Error refreshing agents: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def get_agent_health():
    """
    Get overall health status of the agent system.

    Returns:
        Health status information
    """
    try:
        status_info = dynamic_agent_manager.get_agent_status()
        summary = status_info.get("summary", {})

        # Determine overall health
        total = summary.get("total", 0)
        active = summary.get("active", 0)
        error = summary.get("error", 0)

        if total == 0:
            health_status = "no_agents"
        elif error > 0:
            health_status = "degraded"
        elif active == total:
            health_status = "healthy"
        else:
            health_status = "partial"

        return {
            "status": "success",
            "health": health_status,
            "summary": summary,
            "timestamp": dynamic_agent_manager.last_config_check.isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting agent health: {e}")
        raise HTTPException(status_code=500, detail=str(e))
