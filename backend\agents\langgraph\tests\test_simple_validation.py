"""
Simple validation tests for Phase 1 implementation.

This module provides basic validation tests that don't require complex imports
to verify the core functionality is working.
"""

import pytest
from datetime import datetime


def test_workflow_visualizer_basic():
    """Test basic workflow visualizer functionality."""
    try:
        from ..visualization.workflow_visualizer import WorkflowVisualizer
        
        visualizer = WorkflowVisualizer()
        
        # Test basic workflow definition
        workflow_def = {
            "nodes": {
                "start": {"label": "Start", "type": "start"},
                "end": {"label": "End", "type": "end"}
            },
            "edges": [{"source": "start", "target": "end"}]
        }
        
        # Generate flowchart
        flowchart = visualizer.generate_mermaid_flowchart(workflow_def)
        
        assert isinstance(flowchart, str)
        assert "flowchart TD" in flowchart
        assert "start" in flowchart
        assert "end" in flowchart
        
        print("✅ Workflow visualizer basic test passed")
        return True
        
    except Exception as e:
        print(f"❌ Workflow visualizer test failed: {e}")
        return False


def test_visualization_timeline():
    """Test timeline visualization."""
    try:
        from ..visualization.workflow_visualizer import WorkflowVisualizer
        
        visualizer = WorkflowVisualizer()
        
        # Test timeline data
        timeline_data = [
            {
                "agent_id": "test_agent",
                "task_name": "Test Task",
                "start_time": "2024-01-01T10:00:00Z",
                "end_time": "2024-01-01T10:00:05Z",
                "status": "completed",
                "execution_time": 5.0
            }
        ]
        
        # Generate timeline
        timeline = visualizer.generate_execution_timeline(timeline_data)
        
        assert isinstance(timeline, str)
        assert "gantt" in timeline
        assert "test_agent" in timeline
        
        print("✅ Timeline visualization test passed")
        return True
        
    except Exception as e:
        print(f"❌ Timeline visualization test failed: {e}")
        return False


def test_visualization_performance_analysis():
    """Test performance analysis functionality."""
    try:
        from ..visualization.workflow_visualizer import WorkflowVisualizer
        
        visualizer = WorkflowVisualizer()
        
        # Test workflow definition
        workflow_def = {
            "nodes": {"node1": {"type": "agent"}, "node2": {"type": "tool"}},
            "edges": [{"source": "node1", "target": "node2"}]
        }
        
        # Test execution history
        execution_history = [
            {
                "agent_id": "agent1",
                "execution_time": 2.0,
                "status": "completed"
            },
            {
                "agent_id": "agent2", 
                "execution_time": 8.0,  # This should be identified as bottleneck
                "status": "completed"
            }
        ]
        
        # Test performance analysis
        analysis = visualizer._analyze_workflow_performance(workflow_def, execution_history)
        
        assert isinstance(analysis, dict)
        assert "structure" in analysis
        assert "performance" in analysis
        
        # Test bottleneck identification
        bottlenecks = visualizer._identify_bottlenecks(execution_history)
        assert len(bottlenecks) == 1  # agent2 should be identified as bottleneck
        assert bottlenecks[0]["agent_id"] == "agent2"
        
        print("✅ Performance analysis test passed")
        return True
        
    except Exception as e:
        print(f"❌ Performance analysis test failed: {e}")
        return False


def test_event_system_basic():
    """Test basic event system functionality."""
    try:
        from ..events.event_bus import LangGraphEvent
        from datetime import datetime
        
        # Create a basic event
        event = LangGraphEvent(
            event_type="test.event",
            timestamp=datetime.now(),
            source="test",
            data={"test": True}
        )
        
        assert event.event_type == "test.event"
        assert event.source == "test"
        assert event.data["test"] is True
        assert isinstance(event.timestamp, datetime)
        
        print("✅ Event system basic test passed")
        return True
        
    except Exception as e:
        print(f"❌ Event system test failed: {e}")
        return False


def test_visualization_integration_service():
    """Test visualization integration service."""
    try:
        from ..integrations.visualization_service import VisualizationIntegrationService
        
        # Create service instance
        service = VisualizationIntegrationService()
        
        # Test service status
        status = service.get_service_status()
        
        assert isinstance(status, dict)
        assert "is_running" in status
        assert "metrics" in status
        
        print("✅ Integration service test passed")
        return True
        
    except Exception as e:
        print(f"❌ Integration service test failed: {e}")
        return False


def test_interactive_workflow_explorer():
    """Test interactive workflow explorer."""
    try:
        from ..visualization.workflow_visualizer import WorkflowVisualizer
        
        visualizer = WorkflowVisualizer()
        
        # Test workflow definition
        workflow_def = {
            "id": "test-workflow",
            "nodes": {
                "start": {"label": "Start", "type": "start"},
                "agent": {"label": "Agent", "type": "agent"},
                "end": {"label": "End", "type": "end"}
            },
            "edges": [
                {"source": "start", "target": "agent"},
                {"source": "agent", "target": "end"}
            ]
        }
        
        # Test execution history
        execution_history = [
            {
                "agent_id": "agent1",
                "start_time": "2024-01-01T10:00:00Z",
                "end_time": "2024-01-01T10:00:03Z",
                "status": "completed",
                "execution_time": 3.0
            }
        ]
        
        # Generate interactive explorer
        explorer = visualizer.create_interactive_workflow_explorer(
            workflow_def, execution_history
        )
        
        assert isinstance(explorer, dict)
        assert "metadata" in explorer
        assert "diagrams" in explorer
        assert "analysis" in explorer
        
        # Check metadata
        metadata = explorer["metadata"]
        assert metadata["workflow_id"] == "test-workflow"
        assert metadata["node_count"] == 3
        assert metadata["edge_count"] == 2
        
        print("✅ Interactive workflow explorer test passed")
        return True
        
    except Exception as e:
        print(f"❌ Interactive workflow explorer test failed: {e}")
        return False


def run_all_validation_tests():
    """Run all validation tests and return summary."""
    tests = [
        test_workflow_visualizer_basic,
        test_visualization_timeline,
        test_visualization_performance_analysis,
        test_event_system_basic,
        test_visualization_integration_service,
        test_interactive_workflow_explorer
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    passed = sum(results)
    total = len(results)
    
    print(f"\n{'='*60}")
    print(f"VALIDATION SUMMARY: {passed}/{total} tests passed")
    print(f"{'='*60}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Phase 1 implementation is working correctly.")
    else:
        print(f"⚠️  {total - passed} tests failed. Please review the implementation.")
    
    return passed == total


if __name__ == "__main__":
    run_all_validation_tests()
