"""
LangGraph Checkpointer for Datagenius Workflows.

This module provides state persistence and recovery capabilities for
LangGraph workflows using the existing PostgreSQL database.
"""

import logging
import json
import uuid
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timezone
import asyncio

try:
    from langgraph.checkpoint.base import BaseCheckpointSaver
    from langgraph.checkpoint import Checkpoint
except ImportError:
    # Fallback for development without LangGraph installed
    BaseCheckpointSaver = object
    Checkpoint = dict

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError

from ..states.agent_state import DatageniusAgentState
from app.config import get_config

logger = logging.getLogger(__name__)


class DatageniusCheckpointer(BaseCheckpointSaver if BaseCheckpointSaver != object else object):
    """
    Custom checkpointer for Datagenius workflows.
    
    This checkpointer integrates with the existing PostgreSQL database
    and provides enhanced functionality for workflow state persistence,
    recovery, and performance monitoring.
    """

    def __init__(self, database_url: str = None):
        """
        Initialize the checkpointer.
        
        Args:
            database_url: Database connection URL
        """
        config = get_config()
        self.database_url = database_url or config.database.url
        self.engine = create_engine(self.database_url)
        self.SessionLocal = sessionmaker(bind=self.engine)
        self.logger = logging.getLogger(__name__)
        
        # Initialize database schema if needed
        self._ensure_schema_exists()

    def _ensure_schema_exists(self):
        """Ensure the required database schema exists."""
        try:
            with self.engine.connect() as conn:
                # Check if langgraph_checkpoints table exists
                result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = 'langgraph_checkpoints'
                    );
                """))
                
                if not result.scalar():
                    self.logger.warning("LangGraph checkpoint tables not found. Please run schema_extensions.sql")
                else:
                    self.logger.info("LangGraph checkpoint schema verified")
                    
        except Exception as e:
            self.logger.error(f"Error checking schema: {e}")

    async def save_checkpoint(
        self, 
        workflow_id: str, 
        checkpoint_id: str,
        thread_id: str,
        state: DatageniusAgentState,
        metadata: Optional[Dict[str, Any]] = None,
        parent_checkpoint_id: Optional[str] = None
    ) -> bool:
        """
        Save workflow checkpoint with enhanced metadata.
        
        Args:
            workflow_id: Workflow identifier
            checkpoint_id: Checkpoint identifier
            thread_id: Thread identifier
            state: Current workflow state
            metadata: Additional metadata
            parent_checkpoint_id: Parent checkpoint identifier
            
        Returns:
            True if successful, False otherwise
        """
        try:
            session = self.SessionLocal()
            
            # Prepare checkpoint data
            checkpoint_data = {
                "workflow_id": workflow_id,
                "checkpoint_id": checkpoint_id,
                "thread_id": thread_id,
                "parent_checkpoint_id": parent_checkpoint_id,
                "state": json.dumps(state, default=str),
                "metadata": json.dumps(metadata or {}, default=str),
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }
            
            # Insert or update checkpoint
            query = text("""
                INSERT INTO langgraph_checkpoints 
                (workflow_id, checkpoint_id, thread_id, parent_checkpoint_id, state, metadata, created_at, updated_at)
                VALUES (:workflow_id, :checkpoint_id, :thread_id, :parent_checkpoint_id, :state, :metadata, :created_at, :updated_at)
                ON CONFLICT (checkpoint_id, thread_id) 
                DO UPDATE SET 
                    state = EXCLUDED.state,
                    metadata = EXCLUDED.metadata,
                    updated_at = EXCLUDED.updated_at
            """)
            
            session.execute(query, checkpoint_data)
            session.commit()
            
            # Also save to existing workflow tables for compatibility
            await self._save_to_workflow_tables(session, workflow_id, state)
            
            # Save performance metrics
            await self._save_performance_metrics(session, workflow_id, checkpoint_id, state)
            
            session.close()
            
            self.logger.info(f"Checkpoint saved: {checkpoint_id} for workflow {workflow_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving checkpoint: {e}")
            if 'session' in locals():
                session.rollback()
                session.close()
            return False

    async def load_checkpoint(
        self, 
        checkpoint_id: str, 
        thread_id: str
    ) -> Optional[Tuple[DatageniusAgentState, Dict[str, Any]]]:
        """
        Load workflow checkpoint.
        
        Args:
            checkpoint_id: Checkpoint identifier
            thread_id: Thread identifier
            
        Returns:
            Tuple of (state, metadata) if found, None otherwise
        """
        try:
            session = self.SessionLocal()
            
            query = text("""
                SELECT state, metadata, created_at, updated_at
                FROM langgraph_checkpoints
                WHERE checkpoint_id = :checkpoint_id AND thread_id = :thread_id
                ORDER BY updated_at DESC
                LIMIT 1
            """)
            
            result = session.execute(query, {
                "checkpoint_id": checkpoint_id,
                "thread_id": thread_id
            }).fetchone()
            
            session.close()
            
            if result:
                state = json.loads(result.state)
                metadata = json.loads(result.metadata)
                
                self.logger.info(f"Checkpoint loaded: {checkpoint_id}")
                return state, metadata
            else:
                self.logger.warning(f"Checkpoint not found: {checkpoint_id}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error loading checkpoint: {e}")
            if 'session' in locals():
                session.close()
            return None

    async def list_checkpoints(
        self, 
        workflow_id: str, 
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        List checkpoints for a workflow.
        
        Args:
            workflow_id: Workflow identifier
            limit: Maximum number of checkpoints to return
            
        Returns:
            List of checkpoint information
        """
        try:
            session = self.SessionLocal()
            
            query = text("""
                SELECT checkpoint_id, thread_id, parent_checkpoint_id, 
                       metadata, created_at, updated_at
                FROM langgraph_checkpoints
                WHERE workflow_id = :workflow_id
                ORDER BY created_at DESC
                LIMIT :limit
            """)
            
            results = session.execute(query, {
                "workflow_id": workflow_id,
                "limit": limit
            }).fetchall()
            
            session.close()
            
            checkpoints = []
            for result in results:
                checkpoints.append({
                    "checkpoint_id": result.checkpoint_id,
                    "thread_id": result.thread_id,
                    "parent_checkpoint_id": result.parent_checkpoint_id,
                    "metadata": json.loads(result.metadata),
                    "created_at": result.created_at.isoformat(),
                    "updated_at": result.updated_at.isoformat()
                })
            
            return checkpoints
            
        except Exception as e:
            self.logger.error(f"Error listing checkpoints: {e}")
            if 'session' in locals():
                session.close()
            return []

    async def delete_checkpoint(self, checkpoint_id: str, thread_id: str) -> bool:
        """
        Delete a specific checkpoint.
        
        Args:
            checkpoint_id: Checkpoint identifier
            thread_id: Thread identifier
            
        Returns:
            True if successful, False otherwise
        """
        try:
            session = self.SessionLocal()
            
            query = text("""
                DELETE FROM langgraph_checkpoints
                WHERE checkpoint_id = :checkpoint_id AND thread_id = :thread_id
            """)
            
            result = session.execute(query, {
                "checkpoint_id": checkpoint_id,
                "thread_id": thread_id
            })
            
            session.commit()
            session.close()
            
            deleted_count = result.rowcount
            self.logger.info(f"Deleted {deleted_count} checkpoint(s): {checkpoint_id}")
            return deleted_count > 0
            
        except Exception as e:
            self.logger.error(f"Error deleting checkpoint: {e}")
            if 'session' in locals():
                session.rollback()
                session.close()
            return False

    async def cleanup_old_checkpoints(self, days_old: int = 30) -> int:
        """
        Clean up old checkpoints.
        
        Args:
            days_old: Delete checkpoints older than this many days
            
        Returns:
            Number of checkpoints deleted
        """
        try:
            session = self.SessionLocal()
            
            query = text("""
                DELETE FROM langgraph_checkpoints
                WHERE created_at < NOW() - INTERVAL ':days days'
            """)
            
            result = session.execute(query, {"days": days_old})
            session.commit()
            session.close()
            
            deleted_count = result.rowcount
            self.logger.info(f"Cleaned up {deleted_count} old checkpoints")
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"Error cleaning up checkpoints: {e}")
            if 'session' in locals():
                session.rollback()
                session.close()
            return 0

    async def _save_to_workflow_tables(self, session: Session, workflow_id: str, state: DatageniusAgentState):
        """Save state to existing workflow execution tables for compatibility."""
        try:
            # Update workflow execution status
            update_query = text("""
                UPDATE workflow_executions 
                SET 
                    status = :status,
                    context = :context,
                    updated_at = NOW()
                WHERE id = :workflow_id
            """)
            
            session.execute(update_query, {
                "workflow_id": workflow_id,
                "status": state.get("workflow_status", "running"),
                "context": json.dumps(state.get("business_context", {}), default=str)
            })
            
        except Exception as e:
            self.logger.error(f"Error updating workflow tables: {e}")

    async def _save_performance_metrics(
        self, 
        session: Session, 
        workflow_id: str, 
        checkpoint_id: str, 
        state: DatageniusAgentState
    ):
        """Save performance metrics for the checkpoint."""
        try:
            execution_metrics = state.get("execution_metrics", {})
            
            # Calculate metrics
            agent_transitions = len(state.get("agent_history", []))
            tool_executions = execution_metrics.get("tool_executions", 0)
            error_count = len(state.get("error_history", []))
            
            # Calculate success rate
            total_operations = agent_transitions + tool_executions
            success_rate = max(0.0, 1.0 - (error_count / max(total_operations, 1)))
            
            # Calculate quality score
            quality_scores = state.get("quality_scores", {})
            avg_quality = sum(quality_scores.values()) / len(quality_scores) if quality_scores else None
            
            # Calculate execution time
            start_time_str = execution_metrics.get("start_time")
            execution_time_ms = 0
            if start_time_str:
                try:
                    start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
                    execution_time_ms = int((datetime.now(timezone.utc) - start_time).total_seconds() * 1000)
                except:
                    pass
            
            metrics_data = {
                "workflow_id": workflow_id,
                "checkpoint_id": checkpoint_id,
                "execution_time_ms": execution_time_ms,
                "agent_transitions": agent_transitions,
                "tool_executions": tool_executions,
                "success_rate": success_rate,
                "error_count": error_count,
                "quality_score": avg_quality,
                "resource_usage": json.dumps(execution_metrics, default=str),
                "recorded_at": datetime.now(timezone.utc)
            }
            
            insert_query = text("""
                INSERT INTO workflow_performance_metrics 
                (workflow_id, checkpoint_id, execution_time_ms, agent_transitions, 
                 tool_executions, success_rate, error_count, quality_score, 
                 resource_usage, recorded_at)
                VALUES (:workflow_id, :checkpoint_id, :execution_time_ms, :agent_transitions,
                        :tool_executions, :success_rate, :error_count, :quality_score,
                        :resource_usage, :recorded_at)
            """)
            
            session.execute(insert_query, metrics_data)
            
        except Exception as e:
            self.logger.error(f"Error saving performance metrics: {e}")

    async def get_workflow_statistics(self, workflow_id: str) -> Dict[str, Any]:
        """
        Get comprehensive statistics for a workflow.
        
        Args:
            workflow_id: Workflow identifier
            
        Returns:
            Dictionary containing workflow statistics
        """
        try:
            session = self.SessionLocal()
            
            # Get basic workflow info
            workflow_query = text("""
                SELECT name, status, created_at, updated_at
                FROM workflow_executions
                WHERE id = :workflow_id
            """)
            
            workflow_result = session.execute(workflow_query, {"workflow_id": workflow_id}).fetchone()
            
            # Get checkpoint count
            checkpoint_query = text("""
                SELECT COUNT(*) as checkpoint_count
                FROM langgraph_checkpoints
                WHERE workflow_id = :workflow_id
            """)
            
            checkpoint_result = session.execute(checkpoint_query, {"workflow_id": workflow_id}).fetchone()
            
            # Get performance metrics
            metrics_query = text("""
                SELECT 
                    AVG(execution_time_ms) as avg_execution_time,
                    MAX(execution_time_ms) as max_execution_time,
                    AVG(success_rate) as avg_success_rate,
                    SUM(error_count) as total_errors,
                    AVG(quality_score) as avg_quality_score
                FROM workflow_performance_metrics
                WHERE workflow_id = :workflow_id
            """)
            
            metrics_result = session.execute(metrics_query, {"workflow_id": workflow_id}).fetchone()
            
            session.close()
            
            statistics = {
                "workflow_id": workflow_id,
                "name": workflow_result.name if workflow_result else "Unknown",
                "status": workflow_result.status if workflow_result else "Unknown",
                "created_at": workflow_result.created_at.isoformat() if workflow_result else None,
                "updated_at": workflow_result.updated_at.isoformat() if workflow_result else None,
                "checkpoint_count": checkpoint_result.checkpoint_count if checkpoint_result else 0,
                "performance": {
                    "avg_execution_time_ms": float(metrics_result.avg_execution_time) if metrics_result and metrics_result.avg_execution_time else 0,
                    "max_execution_time_ms": metrics_result.max_execution_time if metrics_result else 0,
                    "avg_success_rate": float(metrics_result.avg_success_rate) if metrics_result and metrics_result.avg_success_rate else 0,
                    "total_errors": metrics_result.total_errors if metrics_result else 0,
                    "avg_quality_score": float(metrics_result.avg_quality_score) if metrics_result and metrics_result.avg_quality_score else None
                }
            }
            
            return statistics
            
        except Exception as e:
            self.logger.error(f"Error getting workflow statistics: {e}")
            if 'session' in locals():
                session.close()
            return {"workflow_id": workflow_id, "error": str(e)}
